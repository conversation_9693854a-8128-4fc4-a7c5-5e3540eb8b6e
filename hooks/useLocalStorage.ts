import { useState, useEffect, useCallback } from 'react';

/**
 * Hook pour gérer le localStorage avec synchronisation
 * @param key - Clé localStorage
 * @param initialValue - Valeur initiale si rien n'est stocké
 * @returns [value, setValue, removeValue]
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  // Fonction pour lire la valeur initiale
  const readValue = useCallback((): T => {
    // Prévenir les erreurs SSR
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? (JSON.parse(item) as T) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  }, [initialValue, key]);

  // État avec valeur initiale lazy
  const [storedValue, setStoredValue] = useState<T>(readValue);

  // Fonction pour mettre à jour la valeur
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      // Prévenir les erreurs SSR
      if (typeof window === 'undefined') {
        console.warn(
          `Tried setting localStorage key "${key}" even though environment is not a client`
        );
        return;
      }

      try {
        // Permettre les fonctions de mise à jour comme useState
        const newValue = value instanceof Function ? value(storedValue) : value;

        // Sauvegarder dans le state
        setStoredValue(newValue);

        // Sauvegarder dans localStorage
        window.localStorage.setItem(key, JSON.stringify(newValue));

        // Dispatch un event custom pour synchroniser entre onglets
        window.dispatchEvent(
          new CustomEvent('local-storage', {
            detail: { key, newValue },
          })
        );
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  // Fonction pour supprimer la valeur
  const removeValue = useCallback(() => {
    // Prévenir les erreurs SSR
    if (typeof window === 'undefined') {
      console.warn(
        `Tried removing localStorage key "${key}" even though environment is not a client`
      );
      return;
    }

    try {
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);

      // Dispatch un event custom pour synchroniser entre onglets
      window.dispatchEvent(
        new CustomEvent('local-storage', {
          detail: { key, newValue: null },
        })
      );
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
    }
  }, [initialValue, key]);

  useEffect(() => {
    setStoredValue(readValue());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Écouter les changements de localStorage (depuis d'autres onglets)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key !== key || e.storageArea !== localStorage) return;

      try {
        setStoredValue(e.newValue ? JSON.parse(e.newValue) : initialValue);
      } catch (error) {
        console.warn(`Error parsing localStorage change for key "${key}":`, error);
      }
    };

    // Écouter l'event custom pour synchronisation dans le même onglet
    const handleCustomStorageChange = (e: CustomEvent) => {
      if (e.detail.key !== key) return;
      setStoredValue(e.detail.newValue ?? initialValue);
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener(
      'local-storage',
      handleCustomStorageChange as EventListener
    );

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener(
        'local-storage',
        handleCustomStorageChange as EventListener
      );
    };
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue];
}

// Hook spécialisé pour les préférences utilisateur
export function useUserPreferences() {
  const [preferences, setPreferences] = useLocalStorage('pillarscan_preferences', {
    theme: 'light',
    language: 'fr',
    notifications: true,
    soundEffects: true,
    hapticFeedback: true,
  });

  return {
    preferences,
    updatePreference: <K extends keyof typeof preferences>(
      key: K,
      value: typeof preferences[K]
    ) => {
      setPreferences((prev) => ({ ...prev, [key]: value }));
    },
  };
}

// Hook pour stocker l'état d'onboarding
export function useOnboardingState() {
  const [state, setState] = useLocalStorage('pillarscan_onboarding', {
    completed: false,
    currentStep: 0,
    skipped: false,
    completedAt: null as string | null,
  });

  return {
    ...state,
    markCompleted: () => {
      setState({
        completed: true,
        currentStep: 0,
        skipped: false,
        completedAt: new Date().toISOString(),
      });
    },
    markSkipped: () => {
      setState({
        completed: true,
        currentStep: 0,
        skipped: true,
        completedAt: new Date().toISOString(),
      });
    },
    setStep: (step: number) => {
      setState((prev) => ({ ...prev, currentStep: step }));
    },
    reset: () => {
      setState({
        completed: false,
        currentStep: 0,
        skipped: false,
        completedAt: null,
      });
    },
  };
}