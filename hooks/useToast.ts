import { useCallback } from 'react';

export interface ToastOptions {
  title: string;
  description?: string;
  variant?: 'default' | 'success' | 'error' | 'warning';
  duration?: number;
}

export function useToast() {
  const toast = useCallback((options: ToastOptions) => {
    // For now, just log to console
    // TODO: Implement actual toast notifications
    console.log('[Toast]', options);
  }, []);

  return { toast };
}