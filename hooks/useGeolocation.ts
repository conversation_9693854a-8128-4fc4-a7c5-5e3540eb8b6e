import { useState, useEffect, useCallback } from 'react';

interface GeolocationState {
  loading: boolean;
  error: string | null;
  coordinates: GeolocationCoordinates | null;
  locationName: string | null;
}

interface UseGeolocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
  autoStart?: boolean;
}

export function useGeolocation(options: UseGeolocationOptions = {}) {
  const {
    enableHighAccuracy = true,
    timeout = 10000,
    maximumAge = 30000,
    autoStart = false,
  } = options;

  const [state, setState] = useState<GeolocationState>({
    loading: false,
    error: null,
    coordinates: null,
    locationName: null,
  });

  // Mock de géolocalisation pour le dev
  const mockGeolocation = useCallback(() => {
    // Simule une position à Paris par défaut
    const mockCoords = {
      latitude: 48.8566,
      longitude: 2.3522,
      accuracy: 50,
      altitude: null,
      altitudeAccuracy: null,
      heading: null,
      speed: null,
      toJSON: () => ({
        latitude: 48.8566,
        longitude: 2.3522,
        accuracy: 50,
        altitude: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      })
    } as GeolocationCoordinates;

    setState({
      loading: false,
      error: null,
      coordinates: mockCoords,
      locationName: 'Paris, France',
    });

    return mockCoords;
  }, []);

  // Obtenir le nom de la ville depuis les coordonnées (simulation)
  const getLocationName = useCallback(async (coords: GeolocationCoordinates) => {
    // En production, utiliser une API de géocodage inversé
    // Pour le moment, on simule
    const cities = [
      { lat: 48.8566, lng: 2.3522, name: 'Paris' },
      { lat: 45.764, lng: 4.8357, name: 'Lyon' },
      { lat: 43.2965, lng: 5.3698, name: 'Marseille' },
      { lat: 43.7102, lng: 7.2620, name: 'Nice' },
      { lat: 47.2184, lng: -1.5536, name: 'Nantes' },
    ];

    // Trouve la ville la plus proche
    let closestCity = cities[0];
    let minDistance = Infinity;

    cities.forEach(city => {
      const distance = Math.sqrt(
        Math.pow(city.lat - coords.latitude, 2) + 
        Math.pow(city.lng - coords.longitude, 2)
      );
      if (distance < minDistance) {
        minDistance = distance;
        closestCity = city;
      }
    });

    return closestCity.name;
  }, []);

  // Fonction pour obtenir la position
  const getPosition = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    // Vérifier si la géolocalisation est supportée
    if (!navigator.geolocation) {
      setState({
        loading: false,
        error: 'La géolocalisation n\'est pas supportée par votre navigateur',
        coordinates: null,
        locationName: null,
      });
      return mockGeolocation();
    }

    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy,
            timeout,
            maximumAge,
          }
        );
      });

      const locationName = await getLocationName(position.coords);

      setState({
        loading: false,
        error: null,
        coordinates: position.coords,
        locationName,
      });

      return position.coords;
    } catch (error) {
      let errorMessage = 'Impossible d\'obtenir votre position';
      
      if (error instanceof GeolocationPositionError) {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Permission refusée. Veuillez autoriser l\'accès à votre position.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Position indisponible. Vérifiez vos paramètres de localisation.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Délai d\'attente dépassé. Réessayez.';
            break;
        }
      }

      setState({
        loading: false,
        error: errorMessage,
        coordinates: null,
        locationName: null,
      });

      // Utiliser la mock location en cas d'erreur
      return mockGeolocation();
    }
  }, [enableHighAccuracy, timeout, maximumAge, getLocationName, mockGeolocation]);

  // Auto-start si demandé
  useEffect(() => {
    if (autoStart) {
      getPosition();
    }
  }, [autoStart, getPosition]);

  // Fonction pour réinitialiser
  const reset = useCallback(() => {
    setState({
      loading: false,
      error: null,
      coordinates: null,
      locationName: null,
    });
  }, []);

  return {
    ...state,
    getPosition,
    reset,
    isSupported: typeof navigator !== 'undefined' && 'geolocation' in navigator,
  };
}

// Hook pour obtenir la permission de géolocalisation
export function useGeolocationPermission() {
  const [permission, setPermission] = useState<PermissionState | null>(null);

  useEffect(() => {
    if (!navigator.permissions) return;

    navigator.permissions
      .query({ name: 'geolocation' })
      .then(result => {
        setPermission(result.state);
        
        result.addEventListener('change', () => {
          setPermission(result.state);
        });
      })
      .catch(() => {
        // Permissions API non supportée
        setPermission(null);
      });
  }, []);

  return permission;
}