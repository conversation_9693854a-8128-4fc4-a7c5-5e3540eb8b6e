import { useState, useEffect, useCallback } from 'react';
import { useLocalStorage } from './useLocalStorage';
import { BADGES, Badge } from '@/lib/types/pillarscan';

interface BadgeProgress {
  badgeId: string;
  earnedAt: string | null;
  progress: number;
  isNew?: boolean;
}

interface UserBadgeData {
  totalXP: number;
  level: number;
  badges: Record<string, BadgeProgress>;
  lastCheck: string | null;
}

const INITIAL_BADGE_DATA: UserBadgeData = {
  totalXP: 0,
  level: 1,
  badges: {},
  lastCheck: null,
};

// XP requis par niveau (progression exponentielle)
const XP_PER_LEVEL = (level: number) => Math.floor(100 * Math.pow(1.5, level - 1));

export function useBadges() {
  const [badgeData, setBadgeData] = useLocalStorage<UserBadgeData>('pillarscan-badges', INITIAL_BADGE_DATA);
  const [newBadges, setNewBadges] = useState<Badge[]>([]);

  // Calcule le niveau basé sur l'XP total
  const calculateLevel = useCallback((xp: number): number => {
    let level = 1;
    let totalRequired = 0;
    
    while (totalRequired <= xp) {
      totalRequired += XP_PER_LEVEL(level);
      if (totalRequired <= xp) level++;
    }
    
    return level;
  }, []);

  // Calcule la progression vers le niveau suivant
  const getLevelProgress = useCallback((xp: number, level: number): number => {
    let xpForCurrentLevel = 0;
    for (let i = 1; i < level; i++) {
      xpForCurrentLevel += XP_PER_LEVEL(i);
    }
    
    const xpInCurrentLevel = xp - xpForCurrentLevel;
    const xpNeededForNext = XP_PER_LEVEL(level);
    
    return (xpInCurrentLevel / xpNeededForNext) * 100;
  }, []);

  // Ajoute de l'XP et vérifie les nouveaux niveaux
  const addXP = useCallback((amount: number) => {
    setBadgeData(prev => {
      const newXP = prev.totalXP + amount;
      const newLevel = calculateLevel(newXP);
      
      return {
        ...prev,
        totalXP: newXP,
        level: newLevel,
      };
    });
  }, [calculateLevel, setBadgeData]);

  // Débloque un badge
  const unlockBadge = useCallback((badgeId: string) => {
    const badge = BADGES[badgeId];
    if (!badge) return;

    setBadgeData(prev => {
      // Si déjà débloqué, ne rien faire
      if (prev.badges[badgeId]?.earnedAt) return prev;

      const newBadges = {
        ...prev.badges,
        [badgeId]: {
          badgeId,
          earnedAt: new Date().toISOString(),
          progress: 100,
          isNew: true,
        },
      };

      // Ajoute l'XP du badge
      const newXP = prev.totalXP + badge.xp;
      const newLevel = calculateLevel(newXP);

      return {
        ...prev,
        totalXP: newXP,
        level: newLevel,
        badges: newBadges,
      };
    });

    // Ajoute à la liste des nouveaux badges
    setNewBadges(prev => [...prev, badge]);
    
    // Retire le badge de la liste après 5 secondes
    setTimeout(() => {
      setNewBadges(prev => prev.filter(b => b.id !== badgeId));
      setBadgeData(prev => ({
        ...prev,
        badges: {
          ...prev.badges,
          [badgeId]: {
            ...prev.badges[badgeId],
            isNew: false,
          },
        },
      }));
    }, 5000);
  }, [calculateLevel, setBadgeData]);

  // Vérifie les badges basés sur les conditions
  const checkBadges = useCallback((stats: {
    totalExpressions?: number;
    currentStreak?: number;
    totalRelates?: number;
    helpfulExpressions?: number;
  }) => {
    // Badge première expression
    if (stats.totalExpressions && stats.totalExpressions >= 1 && !badgeData.badges.first_expression?.earnedAt) {
      unlockBadge('first_expression');
    }

    // Badges de streak
    if (stats.currentStreak) {
      if (stats.currentStreak >= 3 && !badgeData.badges.streak_3?.earnedAt) {
        unlockBadge('streak_3');
      }
      if (stats.currentStreak >= 7 && !badgeData.badges.streak_7?.earnedAt) {
        unlockBadge('streak_7');
      }
      if (stats.currentStreak >= 30 && !badgeData.badges.streak_30?.earnedAt) {
        unlockBadge('streak_30');
      }
    }

    // Badges d'impact
    if (stats.totalRelates) {
      if (stats.totalRelates >= 10 && !badgeData.badges.impact_10?.earnedAt) {
        unlockBadge('impact_10');
      }
      if (stats.totalRelates >= 50 && !badgeData.badges.impact_50?.earnedAt) {
        unlockBadge('impact_50');
      }
    }

    // Badge helper
    if (stats.helpfulExpressions && stats.helpfulExpressions >= 5 && !badgeData.badges.helper?.earnedAt) {
      unlockBadge('helper');
    }
  }, [badgeData.badges, unlockBadge]);

  // Obtient tous les badges débloqués
  const getUnlockedBadges = useCallback((): Badge[] => {
    return Object.keys(badgeData.badges)
      .filter(id => badgeData.badges[id].earnedAt)
      .map(id => BADGES[id])
      .filter(Boolean);
  }, [badgeData.badges]);

  // Obtient la progression d'un badge spécifique
  const getBadgeProgress = useCallback((badgeId: string): BadgeProgress | null => {
    return badgeData.badges[badgeId] || null;
  }, [badgeData.badges]);

  return {
    totalXP: badgeData.totalXP,
    level: badgeData.level,
    levelProgress: getLevelProgress(badgeData.totalXP, badgeData.level),
    xpForNextLevel: XP_PER_LEVEL(badgeData.level),
    badges: badgeData.badges,
    unlockedBadges: getUnlockedBadges(),
    newBadges,
    addXP,
    unlockBadge,
    checkBadges,
    getBadgeProgress,
  };
}

// Hook pour l'animation de gain d'XP
export function useXPAnimation() {
  const [animatingXP, setAnimatingXP] = useState<number[]>([]);

  const animateXP = useCallback((amount: number) => {
    const id = Date.now();
    setAnimatingXP(prev => [...prev, id]);
    
    setTimeout(() => {
      setAnimatingXP(prev => prev.filter(x => x !== id));
    }, 2000);
    
    return id;
  }, []);

  return {
    animatingXP,
    animateXP,
  };
}