import { useState, useEffect, useCallback } from 'react';
import { useLocalStorage } from './useLocalStorage';

interface StreakData {
  currentStreak: number;
  longestStreak: number;
  lastExpressionDate: string | null;
  totalExpressions: number;
  streakStartDate: string | null;
}

interface UseStreakReturn extends StreakData {
  recordExpression: () => void;
  isStreakActive: boolean;
  daysUntilStreakLoss: number;
  checkStreak: () => void;
}

const INITIAL_STREAK_DATA: StreakData = {
  currentStreak: 0,
  longestStreak: 0,
  lastExpressionDate: null,
  totalExpressions: 0,
  streakStartDate: null,
};

export function useStreak(): UseStreakReturn {
  const [streakData, setStreakData] = useLocalStorage<StreakData>('pillarscan-streak', INITIAL_STREAK_DATA);
  const [isStreakActive, setIsStreakActive] = useState(false);
  const [daysUntilStreakLoss, setDaysUntilStreakLoss] = useState(1);

  // Vérifie si deux dates sont le même jour
  const isSameDay = (date1: Date, date2: Date): boolean => {
    return date1.toDateString() === date2.toDateString();
  };

  // Vérifie si deux dates sont des jours consécutifs
  const isConsecutiveDay = (date1: Date, date2: Date): boolean => {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays === 1;
  };

  // Calcule le nombre d'heures avant la perte du streak
  const calculateDaysUntilStreakLoss = useCallback((lastDate: string | null): number => {
    if (!lastDate) return 1;
    
    const last = new Date(lastDate);
    const now = new Date();
    const tomorrow = new Date(last);
    tomorrow.setDate(tomorrow.getDate() + 2); // Fin du jour suivant
    tomorrow.setHours(0, 0, 0, 0);
    
    const hoursLeft = Math.max(0, (tomorrow.getTime() - now.getTime()) / (1000 * 60 * 60));
    return Math.ceil(hoursLeft / 24);
  }, []);

  // Vérifie et met à jour le streak
  const checkStreak = useCallback(() => {
    if (!streakData.lastExpressionDate) {
      setIsStreakActive(false);
      setDaysUntilStreakLoss(1);
      return;
    }

    const lastDate = new Date(streakData.lastExpressionDate);
    const today = new Date();
    
    // Si c'est aujourd'hui, le streak est actif
    if (isSameDay(lastDate, today)) {
      setIsStreakActive(true);
      setDaysUntilStreakLoss(1);
      return;
    }
    
    // Si c'était hier, le streak est toujours actif mais à risque
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (isSameDay(lastDate, yesterday)) {
      setIsStreakActive(true);
      setDaysUntilStreakLoss(calculateDaysUntilStreakLoss(streakData.lastExpressionDate));
      return;
    }
    
    // Sinon, le streak est perdu
    setIsStreakActive(false);
    setDaysUntilStreakLoss(0);
    
    // Reset le streak si nécessaire
    if (streakData.currentStreak > 0) {
      setStreakData(prev => ({
        ...prev,
        currentStreak: 0,
        streakStartDate: null,
      }));
    }
  }, [streakData, calculateDaysUntilStreakLoss, setStreakData]);

  // Enregistre une nouvelle expression
  const recordExpression = useCallback(() => {
    const today = new Date();
    const todayStr = today.toISOString();
    
    setStreakData(prev => {
      const lastDate = prev.lastExpressionDate ? new Date(prev.lastExpressionDate) : null;
      
      // Si c'est la première expression ou même jour
      if (!lastDate || isSameDay(lastDate, today)) {
        return {
          ...prev,
          lastExpressionDate: todayStr,
          totalExpressions: prev.totalExpressions + 1,
          currentStreak: prev.currentStreak || 1,
          streakStartDate: prev.streakStartDate || todayStr,
        };
      }
      
      // Si c'est le jour suivant
      if (isConsecutiveDay(lastDate, today)) {
        const newStreak = prev.currentStreak + 1;
        const newLongest = Math.max(newStreak, prev.longestStreak);
        
        return {
          ...prev,
          currentStreak: newStreak,
          longestStreak: newLongest,
          lastExpressionDate: todayStr,
          totalExpressions: prev.totalExpressions + 1,
        };
      }
      
      // Si le streak est cassé
      return {
        ...prev,
        currentStreak: 1,
        streakStartDate: todayStr,
        lastExpressionDate: todayStr,
        totalExpressions: prev.totalExpressions + 1,
      };
    });
    
    // Déclenche une vérification immédiate
    checkStreak();
  }, [setStreakData, checkStreak]);

  // Vérifie le streak au chargement et toutes les heures
  useEffect(() => {
    checkStreak();
    
    const interval = setInterval(checkStreak, 1000 * 60 * 60); // Toutes les heures
    
    return () => clearInterval(interval);
  }, [checkStreak]);

  return {
    ...streakData,
    recordExpression,
    isStreakActive,
    daysUntilStreakLoss,
    checkStreak,
  };
}

// Hook pour obtenir les milestones de streak
export function useStreakMilestones(currentStreak: number) {
  const milestones = [3, 7, 14, 30, 50, 100, 365];
  
  const nextMilestone = milestones.find(m => m > currentStreak) || null;
  const reachedMilestones = milestones.filter(m => m <= currentStreak);
  const progress = nextMilestone 
    ? ((currentStreak % nextMilestone) / nextMilestone) * 100 
    : 100;
  
  return {
    nextMilestone,
    reachedMilestones,
    progress,
    isAtMilestone: milestones.includes(currentStreak),
  };
}