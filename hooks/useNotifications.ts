import { useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNotificationStore } from '@/stores/notificationStore';
import { getNotificationService, type Notification } from '@/lib/services/NotificationService';
import { toast } from 'react-hot-toast';
import { pillarScanAPI } from '@/lib/api/client';

export function useNotifications() {
  const { isAuthenticated } = useAuth();
  const notificationService = useRef(getNotificationService());
  const {
    addNotification,
    setNotifications,
    markAsRead,
    dismiss,
    setConnectionStatus,
    setLoading,
    setError,
    notifications,
    unreadCount,
    isConnected
  } = useNotificationStore();

  useEffect(() => {
    if (!isAuthenticated) {
      notificationService.current.disconnect();
      setConnectionStatus(false);
      return;
    }

    const token = pillarScanAPI.getToken();
    if (!token) {
      console.log('No token available yet, waiting...');
      return;
    }

    // Se connecter au service de notifications
    const connectToNotifications = async () => {
      try {
        setLoading(true);
        
        // Se connecter au flux SSE d'abord pour définir le token
        await notificationService.current.connect(token);
        
        // Ensuite charger l'historique des notifications
        const history = await notificationService.current.getHistory({
          limit: 20,
          unread_only: false
        });
        setNotifications(history.results);
      } catch (error) {
        console.error('Failed to connect to notifications:', error);
        setError('Impossible de se connecter aux notifications');
        
        // Réessayer après un délai
        setTimeout(connectToNotifications, 5000);
      } finally {
        setLoading(false);
      }
    };

    // Écouter les événements du service
    const handleConnected = () => {
      setConnectionStatus(true);
      console.log('Connected to notifications');
    };

    const handleDisconnected = () => {
      setConnectionStatus(false);
      console.log('Disconnected from notifications');
    };

    const handleNotification = (notification: Notification) => {
      addNotification(notification);
      
      // Afficher un toast pour les notifications importantes
      const notificationIcons: Record<string, string> = {
        'badge_earned': '🏆',
        'streak_milestone': '🔥',
        'expression_related': '💬',
        'pillar_expert': '👑',
        'weekly_summary': '📊',
        'expression_classified': '🎯',
        'community_milestone': '🎉'
      };
      
      if (notification.priority >= 7 || notification.type === 'badge_earned') {
        toast.success(notification.title, {
          duration: 7000,
          icon: notificationIcons[notification.type] || '📢'
        });
      } else if (notification.priority >= 4) {
        toast(notification.title, {
          duration: 5000,
          icon: notificationIcons[notification.type] || '📢'
        });
      }
    };

    const handleError = (error: Error) => {
      console.error('Notification service error:', error);
      setError(error.message);
    };

    // Attacher les listeners
    notificationService.current.on('connected', handleConnected);
    notificationService.current.on('disconnected', handleDisconnected);
    notificationService.current.on('notification', handleNotification);
    notificationService.current.on('error', handleError);

    // Se connecter
    connectToNotifications();

    // Cleanup
    return () => {
      notificationService.current.off('connected', handleConnected);
      notificationService.current.off('disconnected', handleDisconnected);
      notificationService.current.off('notification', handleNotification);
      notificationService.current.off('error', handleError);
      notificationService.current.disconnect();
    };
  }, [isAuthenticated]);

  // Actions exposées
  const markNotificationAsRead = async (id: string) => {
    try {
      markAsRead(id);
      await notificationService.current.markAsRead(id);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      setError('Impossible de marquer la notification comme lue');
    }
  };

  const dismissNotification = async (id: string) => {
    try {
      dismiss(id);
      await notificationService.current.dismiss(id);
    } catch (error) {
      console.error('Failed to dismiss notification:', error);
      setError('Impossible de masquer la notification');
    }
  };

  const markAllAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter(n => !n.is_read);
      
      // Marquer toutes comme lues localement
      useNotificationStore.getState().markAllAsRead();
      
      // Envoyer les requêtes au serveur
      await Promise.all(
        unreadNotifications.map(n => {
          const notifId = n.notification_id || n.id;
          if (notifId) {
            return notificationService.current.markAsRead(notifId).catch(console.error);
          }
          return Promise.resolve();
        })
      );
    } catch (error) {
      console.error('Failed to mark all as read:', error);
      setError('Impossible de marquer toutes les notifications comme lues');
    }
  };

  return {
    notifications,
    unreadCount,
    isConnected,
    markAsRead: markNotificationAsRead,
    dismiss: dismissNotification,
    markAllAsRead
  };
}