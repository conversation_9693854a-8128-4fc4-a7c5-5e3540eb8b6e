import { useState, useCallback, useMemo } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useDebounce } from '@/hooks/useDebounce';
// import { pillarScanAPI } from '@/lib/api/client';
// import { api } from '@/lib/api';

export interface SearchFilters {
  mood?: string;
  pillar?: string;
  date_from?: string;
  date_to?: string;
  near_lat?: number;
  near_lon?: number;
  radius_km?: number;
}

export interface SearchResult {
  id: string;
  content: string;
  mood: string;
  country: string;
  highlight?: string;
  score: number;
  created_at: string;
  location: {
    city?: string;
    region?: string;
  };
  engagement: {
    relates: number;
    views: number;
    impact_score: number;
  };
  user?: {
    id: string;
    nickname: string;
    level: number;
  };
}

export interface SearchAggregations {
  moods: Array<{ key: string; count: number }>;
  pillars: Array<{ key: string; count: number }>;
  cities: Array<{ key: string; count: number }>;
}

export interface SearchResponse {
  total: number;
  country: string;
  page: number;
  page_size: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
  results: SearchResult[];
  aggregations: SearchAggregations;
  query: string;
  filters: SearchFilters;
}

interface UseExpressionSearchOptions {
  initialQuery?: string;
  initialFilters?: SearchFilters;
  pageSize?: number;
  debounceMs?: number;
  sortBy?: 'relevance' | 'date' | 'engagement';
}

export function useExpressionSearch(options: UseExpressionSearchOptions = {}) {
  // Le pays est maintenant géré automatiquement par le client API
  const queryClient = useQueryClient();
  
  const [searchTerm, setSearchTerm] = useState(options.initialQuery || '');
  const [filters, setFilters] = useState<SearchFilters>(options.initialFilters || {});
  const [page, setPage] = useState(1);
  const [sortBy, setSortBy] = useState(options.sortBy || 'relevance');
  
  // Debounce le terme de recherche pour éviter trop de requêtes
  const debouncedSearchTerm = useDebounce(searchTerm, options.debounceMs || 300);
  
  // Construire les paramètres de requête
  const queryParams = useMemo(() => {
    const params = new URLSearchParams();
    
    if (debouncedSearchTerm) {
      params.append('q', debouncedSearchTerm);
    }
    
    // Ajouter les filtres
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value));
      }
    });
    
    params.append('page', String(page));
    params.append('page_size', String(options.pageSize || 20));
    params.append('sort_by', sortBy);
    
    return params.toString();
  }, [debouncedSearchTerm, filters, page, sortBy, options.pageSize]);
  
  // Query pour la recherche
  const searchQuery = useQuery<SearchResponse>({
    queryKey: ['expression-search', queryParams],
    queryFn: async () => {
      // TODO: Utiliser pillarScanAPI.searchExpressions quand l'endpoint sera implémenté
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v2/pillarscan/expressions/search/?${queryParams}`,
        {
          headers: {
            'Accept': 'application/json',
            // Le header X-Country-Code sera ajouté automatiquement par le client API
          },
          credentials: 'include'
        }
      );
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Search failed');
      }
      
      return response.json();
    },
    enabled: true,
    staleTime: 30000, // Cache pendant 30 secondes
    gcTime: 300000, // Garde en cache 5 minutes
  });
  
  // Fonctions de manipulation
  const updateSearchTerm = useCallback((term: string) => {
    setSearchTerm(term);
    setPage(1); // Reset à la première page
  }, []);
  
  const updateFilters = useCallback((newFilters: Partial<SearchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPage(1); // Reset à la première page
  }, []);
  
  const clearFilters = useCallback(() => {
    setFilters({});
    setPage(1);
  }, []);
  
  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setFilters({});
    setPage(1);
  }, []);
  
  const goToPage = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);
  
  const nextPage = useCallback(() => {
    if (searchQuery.data?.has_next) {
      setPage(prev => prev + 1);
    }
  }, [searchQuery.data?.has_next]);
  
  const previousPage = useCallback(() => {
    if (searchQuery.data?.has_previous) {
      setPage(prev => Math.max(1, prev - 1));
    }
  }, [searchQuery.data?.has_previous]);
  
  const changeSortBy = useCallback((newSort: 'relevance' | 'date' | 'engagement') => {
    setSortBy(newSort);
    setPage(1);
  }, []);
  
  // Préfetch de la page suivante pour une navigation fluide
  const prefetchNextPage = useCallback(() => {
    if (searchQuery.data?.has_next) {
      const nextParams = new URLSearchParams(queryParams);
      nextParams.set('page', String(page + 1));
      
      queryClient.prefetchQuery({
        queryKey: ['expression-search', nextParams.toString()],
        queryFn: async () => {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/api/v2/pillarscan/expressions/search/?${nextParams}`,
            {
              headers: {
                'Accept': 'application/json',
              },
              credentials: 'include'
            }
          );
          return response.json();
        },
        staleTime: 30000,
      });
    }
  }, [searchQuery.data?.has_next, queryParams, page, queryClient]);
  
  return {
    // État de recherche
    searchTerm,
    debouncedSearchTerm,
    filters,
    page,
    sortBy,
    
    // Résultats
    results: searchQuery.data?.results || [],
    total: searchQuery.data?.total || 0,
    totalPages: searchQuery.data?.total_pages || 0,
    aggregations: searchQuery.data?.aggregations,
    hasNextPage: searchQuery.data?.has_next || false,
    hasPreviousPage: searchQuery.data?.has_previous || false,
    
    // État de la requête
    isLoading: searchQuery.isLoading,
    isError: searchQuery.isError,
    error: searchQuery.error,
    isFetching: searchQuery.isFetching,
    
    // Actions
    updateSearchTerm,
    updateFilters,
    clearFilters,
    clearSearch,
    goToPage,
    nextPage,
    previousPage,
    changeSortBy,
    prefetchNextPage,
    refetch: searchQuery.refetch,
  };
}

// Hook pour les suggestions de recherche
export function useSearchSuggestions(query: string) {
  const debouncedQuery = useDebounce(query, 200);
  
  return useQuery({
    queryKey: ['search-suggestions', debouncedQuery],
    queryFn: async () => {
      if (!debouncedQuery || debouncedQuery.length < 2) {
        return { suggestions: [] };
      }
      
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v2/pillarscan/expressions/search/suggestions/?q=${encodeURIComponent(debouncedQuery)}`,
        {
          headers: {
            'Accept': 'application/json',
          },
          credentials: 'include'
        }
      );
      
      return response.json();
    },
    enabled: !!debouncedQuery && debouncedQuery.length >= 2,
    staleTime: 60000, // Cache 1 minute
  });
}

// Hook pour les statistiques du pays
export function useCountryStatistics() {
  return useQuery({
    queryKey: ['country-statistics'],
    queryFn: async () => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v2/pillarscan/statistics/`,
        {
          headers: {
            'Accept': 'application/json',
          },
          credentials: 'include'
        }
      );
      
      if (!response.ok) {
        throw new Error('Failed to fetch statistics');
      }
      
      return response.json();
    },
    enabled: true,
    staleTime: 300000, // Cache 5 minutes
    gcTime: 900000, // Garde en cache 15 minutes
  });
}