import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';

export interface ClassificationResult {
  status: 'pending' | 'completed' | 'failed' | 'manual';
  classification?: {
    category: {
      code: string;
      name: string;
      confidence: number;
    };
    subcategory?: {
      code: string;
      name: string;
      confidence: number;
    };
    topic?: {
      code: string;
      name: string;
      confidence: number;
    };
  };
  sentiment?: {
    value: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL' | 'MIXED';
    intensity: number;
  };
  entities?: Array<{
    text: string;
    type: string;
    score: number;
    person_id?: string;
  }>;
  key_phrases?: Array<{
    text: string;
    score: number;
  }>;
  languages?: string[];
}

export interface PillarCategory {
  code: string;
  name: string;
  description: string;
  subcategories?: Record<string, PillarCategory>;
  topics?: Array<{
    topic_code: string;
    name: string;
    description: string;
  }>;
}

export function useClassification(expressionId?: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // TODO: Implement classification endpoints in PillarScanAPI
  // For now, return mock data
  const { data: classification, isLoading, error } = useQuery<ClassificationResult>({
    queryKey: ['classification', expressionId],
    queryFn: async () => {
      if (!expressionId) throw new Error('Expression ID required');
      
      // Mock data for now
      return {
        status: 'pending' as const,
        classification: {
          category: {
            code: 'MOCK',
            name: 'Mock Category',
            confidence: 0.95
          }
        }
      };
    },
    enabled: !!expressionId,
  });

  // Mutation pour corriger manuellement la classification
  const overrideClassification = useMutation({
    mutationFn: async ({
      expressionId,
      categoryCode,
      subcategoryCode,
      topicCode,
    }: {
      expressionId: string;
      categoryCode: string;
      subcategoryCode?: string;
      topicCode?: string;
    }) => {
      // TODO: Implement override endpoint
      console.log('Override classification:', { expressionId, categoryCode, subcategoryCode, topicCode });
      return { success: true };
    },
    onSuccess: (_data, variables) => {
      // Invalider le cache
      queryClient.invalidateQueries({ queryKey: ['classification', variables.expressionId] });
      queryClient.invalidateQueries({ queryKey: ['expressions'] });
      
      toast({
        title: 'Classification mise à jour',
        description: 'La classification a été corrigée manuellement.',
        variant: 'success',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Erreur',
        description: error.message || 'Impossible de mettre à jour la classification',
        variant: 'error',
      });
    },
  });

  return {
    classification,
    isLoading,
    error,
    overrideClassification: overrideClassification.mutate,
    isOverriding: overrideClassification.isPending,
  };
}

export function usePillarCategories() {
  // TODO: Implement categories endpoint
  const { data: categories, isLoading, error } = useQuery<Record<string, PillarCategory>>({
    queryKey: ['pillar-categories'],
    queryFn: async () => {
      // Mock data for now
      return {};
    },
    staleTime: 1000 * 60 * 60, // Cache for 1 hour
  });

  // Recherche dans les catégories
  const searchCategories = useCallback(
    async (query: string) => {
      if (!query || query.length < 2) return [];
      
      // TODO: Implement search
      return [];
    },
    []
  );

  // Obtenir le chemin complet d'une catégorie
  const getCategoryPath = useCallback(
    (/* categoryCode: string */): string[] => {
      // TODO: Implement category path logic
      return [];
    },
    []
  );

  return {
    categories,
    isLoading,
    error,
    searchCategories,
    getCategoryPath,
  };
}

export function useClassificationStats(period: string = '7d') {
  const { data: stats, isLoading, error } = useQuery({
    queryKey: ['classification-stats', period],
    queryFn: async () => {
      // TODO: Implement stats endpoint
      return {
        total: 0,
        by_category: {},
        by_sentiment: {},
      };
    },
    staleTime: 1000 * 60 * 5, // Cache for 5 minutes
  });

  return {
    stats,
    isLoading,
    error,
  };
}

// Hook pour la classification en temps réel pendant la saisie
export function useRealtimeClassification() {
  const [preview, setPreview] = useState<ClassificationResult | null>(null);
  const [isClassifying, setIsClassifying] = useState(false);

  const classifyText = useCallback(
    async (text: string) => {
      if (!text || text.length < 10) {
        setPreview(null);
        return;
      }

      setIsClassifying(true);
      
      try {
        // Pour l'instant, on simule une classification
        // En production, on pourrait appeler un endpoint léger de preview
        setTimeout(() => {
          setPreview({
            status: 'pending',
            classification: {
              category: {
                code: 'PREDICTED',
                name: 'Catégorie prédite...',
                confidence: 0.0,
              },
            },
          });
          setIsClassifying(false);
        }, 500);
      } catch (error) {
        console.error('Preview classification error:', error);
        setIsClassifying(false);
      }
    },
    []
  );

  return {
    preview,
    isClassifying,
    classifyText,
  };
}