import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CountryProvider } from '@/contexts/CountryContext';
import { CountrySelectionModal } from '@/components/country/CountrySelectionModal';
import { useCountryEnforcement, useRequiredCountry } from '@/hooks/useCountryEnforcement';
import { AuthProvider } from '@/contexts/AuthContext';

// Mock du localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as Storage;

// Mock de la géolocalisation
const mockGeolocation = {
  getCurrentPosition: jest.fn(),
};
global.navigator.geolocation = mockGeolocation as Geolocation;

// Mock de fetch pour l'API de géolocalisation IP
global.fetch = jest.fn();

describe('Country Enforcement', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('useCountryEnforcement Hook', () => {
    function TestComponent() {
      const { country, showCountryModal, isLoading } = useCountryEnforcement();
      
      return (
        <div>
          <div data-testid="country">{country || 'none'}</div>
          <div data-testid="show-modal">{showCountryModal ? 'true' : 'false'}</div>
          <div data-testid="loading">{isLoading ? 'true' : 'false'}</div>
        </div>
      );
    }

    it('should show country modal when no country is set', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('show-modal')).toHaveTextContent('true');
        expect(screen.getByTestId('country')).toHaveTextContent('none');
      });
    });

    it('should use country from localStorage if available', async () => {
      localStorageMock.getItem.mockReturnValue('FR');

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('country')).toHaveTextContent('FR');
        expect(screen.getByTestId('show-modal')).toHaveTextContent('false');
      });
    });

    it('should use geolocation when available', async () => {
      mockGeolocation.getCurrentPosition.mockImplementation((success) => {
        success({
          coords: {
            latitude: 48.8566,
            longitude: 2.3522,
          },
        });
      });

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ country_code: 'FR' }),
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('country')).toHaveTextContent('FR');
        expect(localStorageMock.setItem).toHaveBeenCalledWith('selectedCountry', 'FR');
      });
    });
  });

  describe('CountrySelectionModal', () => {
    it('should not allow closing without selection', () => {
      const mockOnSelect = jest.fn();
      
      render(
        <CountrySelectionModal 
          isOpen={true} 
          onCountrySelect={mockOnSelect}
        />
      );

      // Vérifier que le modal est ouvert
      expect(screen.getByText('Sélection du pays obligatoire')).toBeInTheDocument();
      
      // Essayer de cliquer en dehors (ne devrait pas fermer)
      const backdrop = document.querySelector('.dialog-backdrop');
      if (backdrop) {
        userEvent.click(backdrop);
      }
      
      // Le modal devrait toujours être visible
      expect(screen.getByText('Sélection du pays obligatoire')).toBeInTheDocument();
    });

    it('should allow country selection', async () => {
      const mockOnSelect = jest.fn();
      
      render(
        <CountrySelectionModal 
          isOpen={true} 
          onCountrySelect={mockOnSelect}
        />
      );

      // Rechercher et sélectionner la France
      const searchInput = screen.getByPlaceholderText('France, Canada, Sénégal...');
      await userEvent.type(searchInput, 'France');
      
      const franceButton = screen.getByText('France');
      await userEvent.click(franceButton);
      
      expect(mockOnSelect).toHaveBeenCalledWith('FR');
    });
  });

  describe('useRequiredCountry Hook', () => {
    it('should throw error when country is not set', () => {
      // Créer un composant qui utilise useRequiredCountry
      function TestComponent() {
        try {
          const country = useRequiredCountry();
          return <div>{country}</div>;
        } catch (error) {
          return <div data-testid="error">{(error as Error).message}</div>;
        }
      }

      render(
        <AuthProvider>
          <CountryProvider>
            <TestComponent />
          </CountryProvider>
        </AuthProvider>
      );

      expect(screen.getByTestId('error')).toHaveTextContent(
        'Country is required but not set'
      );
    });
  });
});

describe('API Client Country Header', () => {
  it('should include X-Country-Code header in requests', async () => {
    // Import dynamique pour éviter les problèmes de mock
    const { PillarScanAPI } = await import('@/lib/api/client');
    
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'selectedCountry') return 'FR';
      return null;
    });

    const api = new PillarScanAPI('http://test.com');
    
    // Mock fetch pour capturer la requête
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ data: 'test' }),
    });

    // Faire une requête
    await api.getExpressions();

    // Vérifier que le header X-Country-Code est présent
    expect(global.fetch).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        headers: expect.objectContaining({
          'X-Country-Code': 'FR',
        }),
      })
    );
  });

  it('should throw error when country is not set', async () => {
    const { PillarScanAPI } = await import('@/lib/api/client');
    
    localStorageMock.getItem.mockReturnValue(null);
    const api = new PillarScanAPI('http://test.com');
    
    // Devrait lancer une erreur
    await expect(api.getExpressions()).rejects.toThrow(
      'Country code not set. Application requires country selection.'
    );
  });
});