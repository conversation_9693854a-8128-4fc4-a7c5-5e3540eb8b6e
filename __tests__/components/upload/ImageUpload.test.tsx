import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ImageUpload } from '@/components/upload/ImageUpload';

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    const { fill, ...rest } = props;
    return <img {...rest} />;
  },
}));

describe('ImageUpload', () => {
  const mockOnUpload = jest.fn();
  const mockOnRemove = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders upload zone', () => {
    render(<ImageUpload onUpload={mockOnUpload} />);
    
    expect(screen.getByText(/Glissez vos images ici/i)).toBeInTheDocument();
    expect(screen.getByText(/Max 5 images/i)).toBeInTheDocument();
  });

  it('handles file validation for invalid type', async () => {
    const user = userEvent.setup();
    
    render(<ImageUpload onUpload={mockOnUpload} />);
    
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    const input = document.getElementById('file-input') as HTMLInputElement;
    
    await user.upload(input, file);
    
    await waitFor(() => {
      expect(screen.getByText(/Type de fichier non supporté/i)).toBeInTheDocument();
    });
    expect(mockOnUpload).not.toHaveBeenCalled();
  });

  it('handles file validation for size limit', async () => {
    const user = userEvent.setup();
    
    render(<ImageUpload onUpload={mockOnUpload} maxSizeInMB={1} />);
    
    // Create a 2MB file (exceeds 1MB limit)
    const content = new Array(2 * 1024 * 1024).fill('a').join('');
    const file = new File([content], 'large.jpg', { type: 'image/jpeg' });
    const input = document.getElementById('file-input') as HTMLInputElement;
    
    await user.upload(input, file);
    
    expect(await screen.findByText(/La taille du fichier dépasse 1MB/i)).toBeInTheDocument();
    expect(mockOnUpload).not.toHaveBeenCalled();
  });

  it('handles successful file upload', async () => {
    const user = userEvent.setup();
    mockOnUpload.mockResolvedValue({ id: '123', url: 'http://example.com/image.jpg' });
    
    render(<ImageUpload onUpload={mockOnUpload} />);
    
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const input = document.getElementById('file-input') as HTMLInputElement;
    
    await user.upload(input, file);
    
    await waitFor(() => {
      expect(mockOnUpload).toHaveBeenCalledWith(file);
      expect(screen.getByAltText('Image uploadée')).toBeInTheDocument();
    });
  });

  it('handles multiple file uploads', async () => {
    const user = userEvent.setup();
    mockOnUpload
      .mockResolvedValueOnce({ id: '1', url: 'http://example.com/1.jpg' })
      .mockResolvedValueOnce({ id: '2', url: 'http://example.com/2.jpg' });
    
    render(<ImageUpload onUpload={mockOnUpload} />);
    
    const files = [
      new File(['test1'], 'test1.jpg', { type: 'image/jpeg' }),
      new File(['test2'], 'test2.jpg', { type: 'image/jpeg' })
    ];
    const input = document.getElementById('file-input') as HTMLInputElement;
    
    await user.upload(input, files);
    
    await waitFor(() => {
      expect(mockOnUpload).toHaveBeenCalledTimes(2);
      expect(screen.getAllByAltText('Image uploadée')).toHaveLength(2);
    });
  });

  it('respects max files limit', async () => {
    const user = userEvent.setup();
    mockOnUpload.mockResolvedValue({ id: '1', url: 'http://example.com/1.jpg' });
    
    render(<ImageUpload onUpload={mockOnUpload} maxFiles={2} />);
    
    // Upload 2 files (max limit)
    const files = [
      new File(['test1'], 'test1.jpg', { type: 'image/jpeg' }),
      new File(['test2'], 'test2.jpg', { type: 'image/jpeg' })
    ];
    const input = document.getElementById('file-input') as HTMLInputElement;
    
    await user.upload(input, files);
    
    await waitFor(() => {
      expect(mockOnUpload).toHaveBeenCalledTimes(2);
    });
    
    // Try to upload one more
    const extraFile = new File(['test3'], 'test3.jpg', { type: 'image/jpeg' });
    await user.upload(input, extraFile);
    
    expect(await screen.findByText(/Limite de 2 images atteinte/i)).toBeInTheDocument();
    expect(mockOnUpload).toHaveBeenCalledTimes(2); // No additional call
  });

  it('handles image removal', async () => {
    const user = userEvent.setup();
    mockOnUpload.mockResolvedValue({ id: '123', url: 'http://example.com/image.jpg' });
    
    render(<ImageUpload onUpload={mockOnUpload} onRemove={mockOnRemove} />);
    
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const input = document.getElementById('file-input') as HTMLInputElement;
    
    await user.upload(input, file);
    
    await waitFor(() => {
      expect(screen.getByAltText('Image uploadée')).toBeInTheDocument();
    });
    
    // Click remove button
    const removeButton = screen.getByLabelText(/Supprimer l'image/i);
    await user.click(removeButton);
    
    expect(mockOnRemove).toHaveBeenCalledWith('123');
    expect(screen.queryByAltText('Image uploadée')).not.toBeInTheDocument();
  });

  it('handles drag and drop', async () => {
    mockOnUpload.mockResolvedValue({ id: '123', url: 'http://example.com/image.jpg' });
    
    render(<ImageUpload onUpload={mockOnUpload} />);
    
    const dropZone = screen.getByText(/Glissez vos images ici/i).closest('div')?.parentElement as HTMLElement;
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    // Simulate drag and drop
    fireEvent.dragEnter(dropZone);
    expect(dropZone).toHaveClass('border-blue-500');
    
    fireEvent.drop(dropZone, {
      dataTransfer: {
        files: [file],
      },
    });
    
    await waitFor(() => {
      expect(mockOnUpload).toHaveBeenCalledWith(file);
    });
  });

  it('shows loading state during upload', async () => {
    const user = userEvent.setup();
    
    // Mock a slow upload
    mockOnUpload.mockImplementation(() => new Promise(resolve => 
      setTimeout(() => resolve({ id: '123', url: 'http://example.com/image.jpg' }), 100)
    ));
    
    render(<ImageUpload onUpload={mockOnUpload} />);
    
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const input = document.getElementById('file-input') as HTMLInputElement;
    
    await user.upload(input, file);
    
    expect(screen.getByText(/Upload en cours/i)).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.queryByText(/Upload en cours/i)).not.toBeInTheDocument();
    });
  });

  it('is disabled when disabled prop is true', async () => {
    const user = userEvent.setup();
    
    render(<ImageUpload onUpload={mockOnUpload} disabled />);
    
    const dropZone = screen.getByText(/Glissez vos images ici/i).closest('div')?.parentElement as HTMLElement;
    expect(dropZone).toHaveClass('opacity-50', 'cursor-not-allowed');
    
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const input = document.getElementById('file-input') as HTMLInputElement;
    
    await user.upload(input, file);
    
    expect(mockOnUpload).not.toHaveBeenCalled();
  });
});