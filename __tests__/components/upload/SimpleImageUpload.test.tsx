import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SimpleImageUpload } from '@/components/upload/SimpleImageUpload';
import '@testing-library/jest-dom';

// Mock useTheme
jest.mock('@/contexts/ThemeContext', () => ({
  useTheme: () => ({
    theme: {
      text: {
        primary: 'text-gray-900',
        secondary: 'text-gray-700',
        muted: 'text-gray-500'
      },
      ui: {
        border: 'border-gray-300'
      }
    }
  })
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: React.PropsWithChildren<React.HTMLAttributes<HTMLDivElement>>) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: React.PropsWithChildren<React.ButtonHTMLAttributes<HTMLButtonElement>>) => <button {...props}>{children}</button>
  },
  AnimatePresence: ({ children }: React.PropsWithChildren) => <>{children}</>
}));

describe('SimpleImageUpload', () => {
  const mockOnFilesChange = jest.fn();
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock URL.createObjectURL
    global.URL.createObjectURL = jest.fn(() => 'mock-url');
    global.URL.revokeObjectURL = jest.fn();
  });

  const createMockFile = (name: string, size: number, type: string): File => {
    const file = new File(['test'], name, { type });
    Object.defineProperty(file, 'size', { value: size });
    return file;
  };

  it('should render upload zone correctly', () => {
    render(
      <SimpleImageUpload 
        files={[]} 
        onFilesChange={mockOnFilesChange}
      />
    );

    expect(screen.getByText('Glissez-déposez vos images ici')).toBeInTheDocument();
    expect(screen.getByText('ou cliquez pour sélectionner des fichiers')).toBeInTheDocument();
    expect(screen.getByText('Max 4 images • 5MB par fichier')).toBeInTheDocument();
  });

  it('should handle file selection via click', async () => {
    render(
      <SimpleImageUpload 
        files={[]} 
        onFilesChange={mockOnFilesChange}
      />
    );

    const input = document.querySelector('input[type="file"]') as HTMLInputElement;
    const file = createMockFile('test.jpg', 1024 * 1024, 'image/jpeg');

    await user.upload(input, file);

    expect(mockOnFilesChange).toHaveBeenCalledWith([file]);
  });

  it('should validate file type', async () => {
    render(
      <SimpleImageUpload 
        files={[]} 
        onFilesChange={mockOnFilesChange}
      />
    );

    const input = document.querySelector('input[type="file"]') as HTMLInputElement;
    const file = createMockFile('test.pdf', 1024 * 1024, 'application/pdf');

    await user.upload(input, file);

    expect(screen.getByText("test.pdf n'est pas une image")).toBeInTheDocument();
    expect(mockOnFilesChange).not.toHaveBeenCalled();
  });

  it('should validate file size', async () => {
    render(
      <SimpleImageUpload 
        files={[]} 
        onFilesChange={mockOnFilesChange}
        maxSize={1}
      />
    );

    const input = document.querySelector('input[type="file"]') as HTMLInputElement;
    const file = createMockFile('large.jpg', 2 * 1024 * 1024, 'image/jpeg');

    await user.upload(input, file);

    expect(screen.getByText('large.jpg dépasse 1MB')).toBeInTheDocument();
    expect(mockOnFilesChange).not.toHaveBeenCalled();
  });

  it('should respect maxFiles limit', async () => {
    const existingFiles = [
      createMockFile('existing1.jpg', 1024, 'image/jpeg'),
      createMockFile('existing2.jpg', 1024, 'image/jpeg')
    ];

    render(
      <SimpleImageUpload 
        files={existingFiles} 
        onFilesChange={mockOnFilesChange}
        maxFiles={3}
      />
    );

    const input = document.querySelector('input[type="file"]') as HTMLInputElement;
    const newFiles = [
      createMockFile('new1.jpg', 1024, 'image/jpeg'),
      createMockFile('new2.jpg', 1024, 'image/jpeg')
    ];

    await user.upload(input, newFiles);

    expect(screen.getByText('Maximum 3 images autorisées')).toBeInTheDocument();
    // Should only add one file to reach the limit
    expect(mockOnFilesChange).toHaveBeenCalledWith([
      ...existingFiles,
      newFiles[0]
    ]);
  });

  it('should display image previews', () => {
    const files = [
      createMockFile('image1.jpg', 1024 * 1024, 'image/jpeg'),
      createMockFile('image2.jpg', 2 * 1024 * 1024, 'image/jpeg')
    ];

    render(
      <SimpleImageUpload 
        files={files} 
        onFilesChange={mockOnFilesChange}
      />
    );

    // Check previews are displayed
    const images = screen.getAllByRole('img');
    expect(images).toHaveLength(2);
    
    // Check file sizes are displayed
    expect(screen.getByText('1.0MB')).toBeInTheDocument();
    expect(screen.getByText('2.0MB')).toBeInTheDocument();
  });

  it('should remove file when clicking remove button', async () => {
    const files = [
      createMockFile('image1.jpg', 1024, 'image/jpeg'),
      createMockFile('image2.jpg', 1024, 'image/jpeg')
    ];

    const { container } = render(
      <SimpleImageUpload 
        files={files} 
        onFilesChange={mockOnFilesChange}
      />
    );

    // Hover over first image to show remove button
    const firstImage = container.querySelector('.group');
    fireEvent.mouseEnter(firstImage!);

    // Click remove button
    const removeButton = container.querySelector('button');
    await user.click(removeButton!);

    expect(mockOnFilesChange).toHaveBeenCalledWith([files[1]]);
  });

  it('should handle drag and drop', async () => {
    render(
      <SimpleImageUpload 
        files={[]} 
        onFilesChange={mockOnFilesChange}
      />
    );

    const dropZone = screen.getByText('Glissez-déposez vos images ici').parentElement!;
    const file = createMockFile('drag.jpg', 1024, 'image/jpeg');

    // Simulate drag over
    fireEvent.dragOver(dropZone);
    expect(dropZone).toHaveClass('border-blue-500');

    // Simulate drop
    fireEvent.drop(dropZone, {
      dataTransfer: {
        files: [file]
      }
    });

    expect(mockOnFilesChange).toHaveBeenCalledWith([file]);
  });

  it('should show add more button when under limit', () => {
    const files = [
      createMockFile('image1.jpg', 1024, 'image/jpeg')
    ];

    render(
      <SimpleImageUpload 
        files={files} 
        onFilesChange={mockOnFilesChange}
        maxFiles={3}
      />
    );

    expect(screen.getByText('Ajouter')).toBeInTheDocument();
  });

  it('should not show add more button when at limit', () => {
    const files = [
      createMockFile('image1.jpg', 1024, 'image/jpeg'),
      createMockFile('image2.jpg', 1024, 'image/jpeg'),
      createMockFile('image3.jpg', 1024, 'image/jpeg')
    ];

    render(
      <SimpleImageUpload 
        files={files} 
        onFilesChange={mockOnFilesChange}
        maxFiles={3}
      />
    );

    expect(screen.queryByText('Ajouter')).not.toBeInTheDocument();
  });
});