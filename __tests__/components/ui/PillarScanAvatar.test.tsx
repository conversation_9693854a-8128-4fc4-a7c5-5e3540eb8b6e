import React from 'react';
import { render, screen } from '@testing-library/react';
import { PillarScanAvatar } from '@/components/ui/Avatar';

// Mock the stringToColor utility
jest.mock('@/lib/utils', () => ({
  ...jest.requireActual('@/lib/utils'),
  stringToColor: jest.fn((str: string) => '#' + str.charCodeAt(0).toString(16).padStart(6, '0')),
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

describe('PillarScanAvatar', () => {
  describe('Rendering with avatarStyle', () => {
    it('renders with emoji and color', () => {
      const avatarStyle = {
        emoji: '🚀',
        color: '#FF5733',
      };
      
      render(<PillarScanAvatar avatarStyle={avatarStyle} nickname="TestUser" />);
      
      expect(screen.getByText('🚀')).toBeInTheDocument();
      const avatar = screen.getByLabelText('TestUser');
      expect(avatar).toHaveStyle({ backgroundColor: '#FF5733' });
    });

    it('renders with only emoji (no nickname)', () => {
      const avatarStyle = {
        emoji: '🌟',
        color: '#3498DB',
      };
      
      render(<PillarScanAvatar avatarStyle={avatarStyle} />);
      
      expect(screen.getByText('🌟')).toBeInTheDocument();
      expect(screen.getByLabelText('Utilisateur')).toBeInTheDocument();
    });

    it('applies custom size', () => {
      const avatarStyle = {
        emoji: '😎',
        color: '#2ECC71',
      };
      
      const sizes = ['xs', 'sm', 'md', 'lg', 'xl'] as const;
      
      sizes.forEach(size => {
        const { unmount } = render(
          <PillarScanAvatar avatarStyle={avatarStyle} size={size} />
        );
        
        const avatar = screen.getByLabelText('Utilisateur');
        const sizeClasses = {
          xs: 'w-6 h-6',
          sm: 'w-8 h-8',
          md: 'w-10 h-10',
          lg: 'w-12 h-12',
          xl: 'w-16 h-16',
        };
        
        expect(avatar).toHaveClass(sizeClasses[size]);
        unmount();
      });
    });
  });

  describe('Rendering without avatarStyle', () => {
    it('renders with nickname fallback', () => {
      render(<PillarScanAvatar nickname="Jean Dupont" />);
      
      expect(screen.getByText('JD')).toBeInTheDocument();
      expect(screen.getByLabelText('Jean Dupont')).toBeInTheDocument();
    });

    it('renders with "Anonyme" when no nickname', () => {
      render(<PillarScanAvatar />);
      
      expect(screen.getByText('AN')).toBeInTheDocument();
      expect(screen.getByLabelText('Utilisateur anonyme')).toBeInTheDocument();
    });

    it('handles single word nicknames', () => {
      render(<PillarScanAvatar nickname="Alice" />);
      
      expect(screen.getByText('AL')).toBeInTheDocument();
    });

    it('handles nicknames with more than two words', () => {
      render(<PillarScanAvatar nickname="Jean Claude Van Damme" />);
      
      // Should only take first two initials
      expect(screen.getByText('JC')).toBeInTheDocument();
    });

    it('applies size to fallback avatar', () => {
      render(<PillarScanAvatar nickname="Test User" size="lg" />);
      
      const avatar = screen.getByLabelText('Test User');
      expect(avatar).toHaveClass('w-12', 'h-12');
    });
  });

  describe('Props validation', () => {
    it('prioritizes avatarStyle over nickname', () => {
      const avatarStyle = {
        emoji: '🎨',
        color: '#E74C3C',
      };
      
      render(
        <PillarScanAvatar 
          avatarStyle={avatarStyle} 
          nickname="Should Not Show"
        />
      );
      
      expect(screen.getByText('🎨')).toBeInTheDocument();
      expect(screen.queryByText('SN')).not.toBeInTheDocument();
    });

    it('defaults to md size when not specified', () => {
      render(<PillarScanAvatar nickname="Default Size" />);
      
      const avatar = screen.getByLabelText('Default Size');
      expect(avatar).toHaveClass('w-10', 'h-10');
    });

    it('maintains proper aria-label with special characters in nickname', () => {
      render(<PillarScanAvatar nickname="Test@User#123" />);
      
      expect(screen.getByLabelText('Test@User#123')).toBeInTheDocument();
    });
  });

  describe('Edge cases', () => {
    it('handles empty string nickname', () => {
      render(<PillarScanAvatar nickname="" />);
      
      // Should fallback to "Anonyme"
      expect(screen.getByText('AN')).toBeInTheDocument();
      expect(screen.getByLabelText('Utilisateur anonyme')).toBeInTheDocument();
    });

    it('handles whitespace-only nickname', () => {
      render(<PillarScanAvatar nickname="   " />);
      
      // Whitespace nickname keeps the whitespace as aria-label
      const avatar = document.querySelector('[aria-label="   "]');
      expect(avatar).toBeInTheDocument();
    });

    it('handles nickname with special characters', () => {
      render(<PillarScanAvatar nickname="🎉 Party Person" />);
      
      // Check that the component renders and has the correct aria-label
      expect(screen.getByLabelText('🎉 Party Person')).toBeInTheDocument();
      // The text content includes emoji as first character
      const avatarDiv = screen.getByLabelText('🎉 Party Person');
      const span = avatarDiv.querySelector('span');
      expect(span?.textContent).toMatch(/P$/); // Ends with P
    });

    it('handles avatarStyle with empty emoji', () => {
      const avatarStyle = {
        emoji: '',
        color: '#9B59B6',
      };
      
      render(<PillarScanAvatar avatarStyle={avatarStyle} nickname="Fallback" />);
      
      // Should still render with the color
      const avatar = screen.getByLabelText('Fallback');
      expect(avatar).toHaveStyle({ backgroundColor: '#9B59B6' });
    });

    it('handles avatarStyle with invalid color', () => {
      const avatarStyle = {
        emoji: '🌈',
        color: 'not-a-color',
      };
      
      render(<PillarScanAvatar avatarStyle={avatarStyle} />);
      
      expect(screen.getByText('🌈')).toBeInTheDocument();
      const avatar = screen.getByLabelText('Utilisateur');
      expect(avatar).toHaveStyle({ backgroundColor: 'not-a-color' });
    });

    it('handles very long nicknames', () => {
      const longNickname = 'This Is A Very Long Nickname That Should Be Truncated';
      render(<PillarScanAvatar nickname={longNickname} />);
      
      // Should still only show first two initials
      expect(screen.getByText('TI')).toBeInTheDocument();
    });

    it('handles nickname with numbers only', () => {
      render(<PillarScanAvatar nickname="123456" />);
      
      // Check that the component renders with numbers
      const avatarDiv = screen.getByLabelText('123456');
      const span = avatarDiv.querySelector('span');
      // Takes first 2 characters of the number string
      expect(span?.textContent).toBe('12');
    });

    it('renders correctly with all size options', () => {
      const testCases = [
        { size: 'xs' as const, expectedClass: 'text-xs' },
        { size: 'sm' as const, expectedClass: 'text-sm' },
        { size: 'md' as const, expectedClass: 'text-base' },
        { size: 'lg' as const, expectedClass: 'text-lg' },
        { size: 'xl' as const, expectedClass: 'text-2xl' },
      ];
      
      testCases.forEach(({ size, expectedClass }) => {
        const { unmount } = render(
          <PillarScanAvatar 
            avatarStyle={{ emoji: '🎯', color: '#FF0000' }} 
            size={size}
          />
        );
        
        const avatar = screen.getByLabelText('Utilisateur');
        expect(avatar).toHaveClass(expectedClass);
        unmount();
      });
    });

    it('combines avatar styles correctly', () => {
      const avatarStyle = {
        emoji: '🎭',
        color: '#1ABC9C',
      };
      
      render(
        <PillarScanAvatar 
          avatarStyle={avatarStyle} 
          nickname="Theater Person"
          size="xl"
        />
      );
      
      const avatar = screen.getByLabelText('Theater Person');
      expect(avatar).toHaveClass('w-16', 'h-16', 'text-2xl');
      expect(avatar).toHaveStyle({ backgroundColor: '#1ABC9C' });
      expect(screen.getByText('🎭')).toBeInTheDocument();
    });
  });

  describe('Integration with Avatar component', () => {
    it('passes through all necessary props to Avatar', () => {
      const avatarStyle = {
        emoji: '🚀',
        color: '#FF5733',
      };
      
      render(
        <PillarScanAvatar 
          avatarStyle={avatarStyle} 
          nickname="Space Explorer"
          size="lg"
        />
      );
      
      const avatar = screen.getByLabelText('Space Explorer');
      expect(avatar).toHaveClass('w-12', 'h-12'); // size lg
      expect(avatar).toHaveClass('rounded-full'); // Avatar base class
      expect(avatar).toHaveClass('inline-flex', 'items-center', 'justify-center'); // Avatar layout
    });

    it('generates consistent colors for same nickname', () => {
      const { unmount: unmount1 } = render(<PillarScanAvatar nickname="Consistent User" />);
      const avatar1 = screen.getByLabelText('Consistent User');
      const style1 = window.getComputedStyle(avatar1);
      unmount1();
      
      const { unmount: unmount2 } = render(<PillarScanAvatar nickname="Consistent User" />);
      const avatar2 = screen.getByLabelText('Consistent User');
      const style2 = window.getComputedStyle(avatar2);
      unmount2();
      
      // Colors should be the same for the same nickname
      expect(style1.backgroundColor).toBe(style2.backgroundColor);
    });
  });
});