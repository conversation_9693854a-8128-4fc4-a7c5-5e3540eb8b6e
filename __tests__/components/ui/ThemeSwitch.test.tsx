import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeSwitch } from '@/components/ui/ThemeSwitch';
import { useTheme, ThemeMode } from '@/contexts/ThemeContext';

// Mock the theme context
jest.mock('@/contexts/ThemeContext');

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

describe('ThemeSwitch', () => {
  const mockSetThemeMode = jest.fn();
  const mockTheme = {
    mode: 'light' as const,
    ui: {
      card: 'bg-white',
      cardHover: 'hover:bg-gray-50',
      border: 'border-gray-200',
    },
    text: {
      primary: 'text-gray-900',
      secondary: 'text-gray-600',
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useTheme as jest.Mock).mockReturnValue({
      themeMode: 'light',
      setThemeMode: mockSetThemeMode,
      theme: mockTheme,
    });
  });

  describe('Compact variant', () => {
    it('renders compact theme switch button', () => {
      render(<ThemeSwitch variant="compact" />);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(screen.getByText('☀️')).toBeInTheDocument();
    });

    it('opens theme menu on click', () => {
      render(<ThemeSwitch variant="compact" />);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      // Check all theme options are displayed
      expect(screen.getByText('Clair')).toBeInTheDocument();
      expect(screen.getByText('Sombre')).toBeInTheDocument();
      expect(screen.getByText('Océan')).toBeInTheDocument();
      expect(screen.getByText('Coucher')).toBeInTheDocument();
      expect(screen.getByText('Aurore')).toBeInTheDocument();
    });

    it('closes menu when clicking backdrop', () => {
      render(<ThemeSwitch variant="compact" />);
      
      // Open menu
      fireEvent.click(screen.getByRole('button'));
      expect(screen.getByText('Sombre')).toBeInTheDocument();
      
      // Click backdrop (first div with fixed positioning)
      const backdrop = document.querySelector('.fixed.inset-0');
      fireEvent.click(backdrop!);
      
      // Menu should be closed
      expect(screen.queryByText('Sombre')).not.toBeInTheDocument();
    });

    it('changes theme when option is clicked', async () => {
      render(<ThemeSwitch variant="compact" />);
      
      // Open menu
      fireEvent.click(screen.getByRole('button'));
      
      // Click dark theme
      fireEvent.click(screen.getByText('Sombre'));
      
      await waitFor(() => {
        expect(mockSetThemeMode).toHaveBeenCalledWith('dark');
      });
      
      // Menu should close after selection
      expect(screen.queryByText('Sombre')).not.toBeInTheDocument();
    });

    it('displays correct icon for current theme', () => {
      const { rerender } = render(<ThemeSwitch variant="compact" />);
      expect(screen.getByText('☀️')).toBeInTheDocument();
      
      // Change to dark theme
      (useTheme as jest.Mock).mockReturnValue({
        themeMode: 'dark',
        setThemeMode: mockSetThemeMode,
        theme: { ...mockTheme, mode: 'dark' },
      });
      
      rerender(<ThemeSwitch variant="compact" />);
      expect(screen.getByText('🌙')).toBeInTheDocument();
    });

    it('highlights current theme in menu', () => {
      render(<ThemeSwitch variant="compact" />);
      
      fireEvent.click(screen.getByRole('button'));
      
      // Light theme should be highlighted
      const lightButton = screen.getByText('Clair').closest('button');
      expect(lightButton).toHaveClass('bg-gradient-to-r', 'from-blue-500', 'to-purple-500');
    });

    it('applies custom className', () => {
      render(<ThemeSwitch variant="compact" className="custom-class" />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class');
    });

    it('handles all theme modes', () => {
      const themes: ThemeMode[] = ['light', 'dark', 'ocean', 'sunset', 'aurora'];
      
      themes.forEach(themeMode => {
        (useTheme as jest.Mock).mockReturnValue({
          themeMode,
          setThemeMode: mockSetThemeMode,
          theme: mockTheme,
        });
        
        const { unmount } = render(<ThemeSwitch variant="compact" />);
        
        const expectedIcons = {
          light: '☀️',
          dark: '🌙',
          ocean: '🌊',
          sunset: '🌅',
          aurora: '🌌',
        };
        
        expect(screen.getByText(expectedIcons[themeMode])).toBeInTheDocument();
        unmount();
      });
    });
  });

  describe('Full variant', () => {
    it('renders full theme switch with grid layout', () => {
      render(<ThemeSwitch variant="full" />);
      
      expect(screen.getByText('Apparence')).toBeInTheDocument();
      
      // All theme options should be visible
      expect(screen.getByText('☀️')).toBeInTheDocument();
      expect(screen.getByText('🌙')).toBeInTheDocument();
      expect(screen.getByText('🌊')).toBeInTheDocument();
      expect(screen.getByText('🌅')).toBeInTheDocument();
      expect(screen.getByText('🌌')).toBeInTheDocument();
    });

    it('changes theme on button click', () => {
      render(<ThemeSwitch variant="full" />);
      
      const oceanButton = screen.getByText('Océan').closest('button');
      fireEvent.click(oceanButton!);
      
      expect(mockSetThemeMode).toHaveBeenCalledWith('ocean');
    });

    it('highlights selected theme', () => {
      (useTheme as jest.Mock).mockReturnValue({
        themeMode: 'sunset',
        setThemeMode: mockSetThemeMode,
        theme: mockTheme,
      });
      
      render(<ThemeSwitch variant="full" />);
      
      const sunsetButton = screen.getByText('Coucher').closest('button');
      expect(sunsetButton).toHaveClass('border-blue-500');
    });

    it('applies custom className to full variant', () => {
      render(<ThemeSwitch variant="full" className="custom-spacing" />);
      
      const container = screen.getByText('Apparence').closest('div');
      expect(container).toHaveClass('custom-spacing');
    });

    it('renders with responsive grid', () => {
      render(<ThemeSwitch variant="full" />);
      
      const grid = screen.getByText('Clair').closest('button')?.parentElement;
      expect(grid).toHaveClass('grid', 'grid-cols-2', 'sm:grid-cols-3');
    });
  });

  describe('Edge cases', () => {
    it('defaults to compact variant', () => {
      render(<ThemeSwitch />);
      
      // Should render as compact (single button)
      const buttons = screen.getAllByRole('button');
      expect(buttons).toHaveLength(1);
    });

    it('handles rapid theme changes', async () => {
      render(<ThemeSwitch variant="compact" />);
      
      fireEvent.click(screen.getByRole('button'));
      
      // Rapidly click different themes
      fireEvent.click(screen.getByText('Sombre'));
      fireEvent.click(screen.getByRole('button'));
      fireEvent.click(screen.getByText('Océan'));
      fireEvent.click(screen.getByRole('button'));
      fireEvent.click(screen.getByText('Aurore'));
      
      await waitFor(() => {
        expect(mockSetThemeMode).toHaveBeenCalledTimes(3);
      });
    });

    it('handles menu toggle correctly', () => {
      render(<ThemeSwitch variant="compact" />);
      
      const button = screen.getByRole('button');
      
      // Open
      fireEvent.click(button);
      expect(screen.getByText('Sombre')).toBeInTheDocument();
      
      // Close by clicking button again
      fireEvent.click(button);
      expect(screen.queryByText('Sombre')).not.toBeInTheDocument();
      
      // Reopen
      fireEvent.click(button);
      expect(screen.getByText('Sombre')).toBeInTheDocument();
    });

    it('preserves theme mode when component unmounts and remounts', () => {
      const { unmount } = render(<ThemeSwitch variant="compact" />);
      
      fireEvent.click(screen.getByRole('button'));
      fireEvent.click(screen.getByText('Océan'));
      
      expect(mockSetThemeMode).toHaveBeenCalledWith('ocean');
      
      unmount();
      
      // Remount with ocean theme
      (useTheme as jest.Mock).mockReturnValue({
        themeMode: 'ocean',
        setThemeMode: mockSetThemeMode,
        theme: mockTheme,
      });
      
      render(<ThemeSwitch variant="compact" />);
      expect(screen.getByText('🌊')).toBeInTheDocument();
    });

    it('handles theme context with dark mode', () => {
      (useTheme as jest.Mock).mockReturnValue({
        themeMode: 'dark',
        setThemeMode: mockSetThemeMode,
        theme: {
          ...mockTheme,
          mode: 'dark',
        },
      });
      
      render(<ThemeSwitch variant="compact" />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('from-slate-700', 'to-slate-800');
    });

    it('applies correct spacing in full variant', () => {
      render(<ThemeSwitch variant="full" />);
      
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveClass('p-4', 'rounded-2xl');
      });
    });

    it('handles missing theme properties gracefully', () => {
      (useTheme as jest.Mock).mockReturnValue({
        themeMode: 'light',
        setThemeMode: mockSetThemeMode,
        theme: {
          mode: 'light',
          ui: {},
          text: {},
        },
      });
      
      render(<ThemeSwitch variant="compact" />);
      expect(screen.getByText('☀️')).toBeInTheDocument();
    });
  });
});