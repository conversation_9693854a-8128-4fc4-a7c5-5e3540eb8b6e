import React from 'react';
import { render, screen } from '@testing-library/react';
import { Badge, AchievementBadge, MoodBadge } from '@/components/ui/Badge';

describe('Badge', () => {
  describe('Basic Badge', () => {
    it('renders with default props', () => {
      render(<Badge>Default Badge</Badge>);
      const badge = screen.getByText('Default Badge');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('bg-gray-100', 'text-gray-800');
    });

    it('renders with different variants', () => {
      const variants = [
        { variant: 'default' as const, classes: ['bg-gray-100', 'text-gray-800'] },
        { variant: 'secondary' as const, classes: ['bg-blue-100', 'text-blue-800'] },
        { variant: 'success' as const, classes: ['bg-green-100', 'text-green-800'] },
        { variant: 'warning' as const, classes: ['bg-yellow-100', 'text-yellow-800'] },
        { variant: 'error' as const, classes: ['bg-red-100', 'text-red-800'] },
        { variant: 'info' as const, classes: ['bg-cyan-100', 'text-cyan-800'] },
      ];

      variants.forEach(({ variant, classes }) => {
        const { unmount } = render(<Badge variant={variant}>{variant} Badge</Badge>);
        const badge = screen.getByText(`${variant} Badge`);
        classes.forEach(className => {
          expect(badge).toHaveClass(className);
        });
        unmount();
      });
    });

    it('renders with different sizes', () => {
      const sizes = [
        { size: 'sm' as const, classes: ['text-xs', 'px-2', 'py-0.5'] },
        { size: 'md' as const, classes: ['text-sm', 'px-2.5', 'py-1'] },
        { size: 'lg' as const, classes: ['text-base', 'px-3', 'py-1.5'] },
      ];

      sizes.forEach(({ size, classes }) => {
        const { unmount } = render(<Badge size={size}>{size} Badge</Badge>);
        const badge = screen.getByText(`${size} Badge`);
        classes.forEach(className => {
          expect(badge).toHaveClass(className);
        });
        unmount();
      });
    });

    it('renders with icon', () => {
      render(<Badge icon={<span data-testid="badge-icon">🎯</span>}>With Icon</Badge>);
      expect(screen.getByTestId('badge-icon')).toBeInTheDocument();
      expect(screen.getByText('With Icon')).toBeInTheDocument();
    });

    it('applies custom className', () => {
      render(<Badge className="custom-class">Custom Class</Badge>);
      const badge = screen.getByText('Custom Class');
      expect(badge).toHaveClass('custom-class');
    });

    it('forwards additional props', () => {
      render(<Badge data-testid="custom-badge" title="Badge Title">Props Badge</Badge>);
      const badge = screen.getByTestId('custom-badge');
      expect(badge).toHaveAttribute('title', 'Badge Title');
    });

    it('forwards ref correctly', () => {
      const ref = React.createRef<HTMLSpanElement>();
      render(<Badge ref={ref}>Ref Badge</Badge>);
      expect(ref.current).toBeInstanceOf(HTMLSpanElement);
      expect(ref.current?.textContent).toBe('Ref Badge');
    });
  });

  describe('AchievementBadge', () => {
    const mockBadge = {
      id: 'first-expression',
      name: 'First Expression',
      description: 'Posted your first expression',
      emoji: '🎉',
      xp_reward: 100,
    };

    it('renders achievement badge with all properties', () => {
      render(<AchievementBadge badge={mockBadge} />);
      
      expect(screen.getByText('🎉')).toBeInTheDocument();
      expect(screen.getByText('First Expression')).toBeInTheDocument();
      expect(screen.getByText('+100 XP')).toBeInTheDocument();
    });

    it('hides XP reward for small size', () => {
      render(<AchievementBadge badge={mockBadge} size="sm" />);
      
      expect(screen.getByText('First Expression')).toBeInTheDocument();
      expect(screen.queryByText('+100 XP')).not.toBeInTheDocument();
    });

    it('shows description when showDescription is true', () => {
      render(<AchievementBadge badge={mockBadge} showDescription={true} />);
      
      expect(screen.getByText('Posted your first expression')).toBeInTheDocument();
    });

    it('hides description by default', () => {
      render(<AchievementBadge badge={mockBadge} />);
      
      expect(screen.queryByText('Posted your first expression')).not.toBeInTheDocument();
    });

    it('renders with different sizes', () => {
      const sizes = ['sm', 'md', 'lg'] as const;
      
      sizes.forEach(size => {
        const { unmount } = render(<AchievementBadge badge={mockBadge} size={size} />);
        expect(screen.getByText('First Expression')).toBeInTheDocument();
        unmount();
      });
    });

    it('uses default variant for achievement badges', () => {
      render(<AchievementBadge badge={mockBadge} />);
      const badge = screen.getByText('First Expression').closest('div')?.querySelector('span');
      expect(badge).toBeInTheDocument();
      // Just check it's rendered without specific classes
    });
  });

  describe('MoodBadge', () => {
    it('renders frustrated mood correctly', () => {
      render(<MoodBadge mood="frustrated" />);
      
      expect(screen.getByText('😤')).toBeInTheDocument();
      expect(screen.getByText('Frustré')).toBeInTheDocument();
      
      const badge = screen.getByText('Frustré').closest('span');
      expect(badge).toHaveClass('bg-red-100', 'text-red-800');
    });

    it('renders happy mood correctly', () => {
      render(<MoodBadge mood="happy" />);
      
      expect(screen.getByText('😊')).toBeInTheDocument();
      expect(screen.getByText('Heureux')).toBeInTheDocument();
      
      const badge = screen.getByText('Heureux').closest('span');
      expect(badge).toHaveClass('bg-green-100', 'text-green-800');
    });

    it('renders idea mood correctly', () => {
      render(<MoodBadge mood="idea" />);
      
      expect(screen.getByText('💡')).toBeInTheDocument();
      expect(screen.getByText('Idée')).toBeInTheDocument();
      
      const badge = screen.getByText('Idée').closest('span');
      expect(badge).toHaveClass('bg-yellow-100', 'text-yellow-800');
    });

    it('renders question mood correctly', () => {
      render(<MoodBadge mood="question" />);
      
      expect(screen.getByText('❓')).toBeInTheDocument();
      expect(screen.getByText('Question')).toBeInTheDocument();
      
      const badge = screen.getByText('Question').closest('span');
      expect(badge).toHaveClass('bg-cyan-100', 'text-cyan-800');
    });

    it('renders with different sizes', () => {
      const sizes = ['sm', 'md', 'lg'] as const;
      
      sizes.forEach(size => {
        const { unmount } = render(<MoodBadge mood="happy" size={size} />);
        expect(screen.getByText('Heureux')).toBeInTheDocument();
        unmount();
      });
    });

    it('handles all mood types', () => {
      const moods = ['frustrated', 'happy', 'idea', 'question'] as const;
      const expectedLabels = {
        frustrated: 'Frustré',
        happy: 'Heureux',
        idea: 'Idée',
        question: 'Question',
      };
      
      moods.forEach(mood => {
        const { unmount } = render(<MoodBadge mood={mood} />);
        expect(screen.getByText(expectedLabels[mood])).toBeInTheDocument();
        unmount();
      });
    });
  });

  describe('Edge cases', () => {
    it('renders empty badge', () => {
      render(<Badge />);
      const badges = screen.getAllByRole('generic').filter(el => 
        el.className.includes('rounded-full')
      );
      expect(badges.length).toBeGreaterThan(0);
    });

    it('renders badge with only icon', () => {
      render(<Badge icon={<span data-testid="only-icon">🌟</span>} />);
      expect(screen.getByTestId('only-icon')).toBeInTheDocument();
    });

    it('renders with multiple children', () => {
      render(
        <Badge>
          <span>First</span>
          <span>Second</span>
        </Badge>
      );
      expect(screen.getByText('First')).toBeInTheDocument();
      expect(screen.getByText('Second')).toBeInTheDocument();
    });

    it('handles long text content', () => {
      const longText = 'This is a very long badge text that might overflow';
      render(<Badge>{longText}</Badge>);
      expect(screen.getByText(longText)).toBeInTheDocument();
    });

    it('preserves dark mode classes', () => {
      render(<Badge variant="success">Dark Mode Badge</Badge>);
      const badge = screen.getByText('Dark Mode Badge');
      expect(badge).toHaveClass('dark:bg-green-900', 'dark:text-green-200');
    });

    it('achievement badge handles missing properties gracefully', () => {
      const minimalBadge = {
        id: 'minimal',
        name: 'Minimal Badge',
        description: '',
        emoji: '',
        xp_reward: 0,
      };
      
      render(<AchievementBadge badge={minimalBadge} />);
      expect(screen.getByText('Minimal Badge')).toBeInTheDocument();
      expect(screen.getByText('+0 XP')).toBeInTheDocument();
    });

    it('combines multiple class sources correctly', () => {
      render(
        <Badge 
          variant="info" 
          size="lg" 
          className="custom-margin"
        >
          Combined Classes
        </Badge>
      );
      
      const badge = screen.getByText('Combined Classes');
      expect(badge).toHaveClass('bg-cyan-100', 'text-cyan-800'); // variant
      expect(badge).toHaveClass('text-base', 'px-3', 'py-1.5'); // size
      expect(badge).toHaveClass('custom-margin'); // custom
      expect(badge).toHaveClass('rounded-full', 'font-medium'); // base
    });
  });
});