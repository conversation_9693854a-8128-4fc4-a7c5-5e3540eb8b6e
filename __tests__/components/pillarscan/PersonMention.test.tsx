import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { PersonMention } from '@/components/pillarscan/expression/PersonMention';
import { PersonType, PersonReference, Person } from '@/types/person';

// Mock de useDebounce
jest.mock('@/hooks/useDebounce', () => ({
  useDebounce: (value: string) => value
}));

describe('PersonMention', () => {
  const mockOnChange = jest.fn();
  const mockOnSearch = jest.fn();

  const mockPersons: Person[] = [
    {
      person_id: '1',
      person_code: 'PERSON_001',
      person_type: PersonType.HUMAN,
      display_name: '<PERSON>',
      human_first_name: '<PERSON>',
      human_last_name: '<PERSON><PERSON>',
      country_code: 'FR',
      tags: [],
      verified: true
    },
    {
      person_id: '2',
      person_code: 'ORG_001',
      person_type: PersonType.MORAL,
      display_name: '<PERSON><PERSON>',
      moral_legal_name: '<PERSON><PERSON>',
      moral_industry: 'Administration publique',
      country_code: 'FR',
      tags: ['gouvernement', 'paris'],
      verified: true
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockOnSearch.mockResolvedValue(mockPersons);
  });

  it('renders with empty state', () => {
    render(
      <PersonMention
        value={[]}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    expect(screen.getByPlaceholderText('Mentionner une personne ou organisation...')).toBeInTheDocument();
    expect(screen.getByText(/mentionnez des personnes/i)).toBeInTheDocument();
  });

  it('displays selected persons', () => {
    const selectedPersons: PersonReference[] = [
      {
        person_id: '1',
        person_code: 'PERSON_001',
        person_name: 'Jean Dupont',
        person_type: PersonType.HUMAN,
        role: 'target'
      }
    ];

    render(
      <PersonMention
        value={selectedPersons}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    expect(screen.getByText('Jean Dupont')).toBeInTheDocument();
    expect(screen.getByText('👤')).toBeInTheDocument();
  });

  it('searches for persons when typing', async () => {
    const user = userEvent.setup();
    
    render(
      <PersonMention
        value={[]}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    const input = screen.getByPlaceholderText('Mentionner une personne ou organisation...');
    await user.type(input, 'Jean');

    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith('Jean', PersonType.HUMAN);
    });
  });

  it('displays search results in dropdown', async () => {
    const user = userEvent.setup();
    
    render(
      <PersonMention
        value={[]}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    const input = screen.getByPlaceholderText('Mentionner une personne ou organisation...');
    await user.type(input, 'Jean');

    await waitFor(() => {
      expect(screen.getByText('Jean Dupont')).toBeInTheDocument();
      // Verify there are 2 verified badges (one for each person)
      const verifiedBadges = screen.getAllByText('✓ Vérifié');
      expect(verifiedBadges).toHaveLength(2);
    });
  });

  it('adds person from search results', async () => {
    const user = userEvent.setup();
    
    render(
      <PersonMention
        value={[]}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    const input = screen.getByPlaceholderText('Mentionner une personne ou organisation...');
    await user.type(input, 'Jean');

    await waitFor(() => {
      expect(screen.getByText('Jean Dupont')).toBeInTheDocument();
    });

    await user.click(screen.getByText('Jean Dupont'));

    expect(mockOnChange).toHaveBeenCalledWith([
      {
        person_id: '1',
        person_code: 'PERSON_001',
        person_name: 'Jean Dupont',
        person_type: PersonType.HUMAN,
        role: 'target',
        needs_resolution: false
      }
    ]);
  });

  it('creates new person when no results', async () => {
    const user = userEvent.setup();
    mockOnSearch.mockResolvedValue([]);
    
    render(
      <PersonMention
        value={[]}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    const input = screen.getByPlaceholderText('Mentionner une personne ou organisation...');
    await user.type(input, 'Nouvelle Personne');

    await waitFor(() => {
      expect(screen.getByText('Créer "Nouvelle Personne"')).toBeInTheDocument();
    });

    await user.click(screen.getByText('Créer "Nouvelle Personne"'));

    expect(mockOnChange).toHaveBeenCalledWith([
      expect.objectContaining({
        person_name: 'Nouvelle Personne',
        person_type: PersonType.HUMAN,
        role: 'target',
        needs_resolution: true,
        temp_id: expect.stringContaining('temp_')
      })
    ]);
  });

  it('changes person type', async () => {
    const user = userEvent.setup();
    
    render(
      <PersonMention
        value={[]}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    // Find all select elements and get the first one (type selector)
    const selects = screen.getAllByRole('combobox');
    const typeSelector = selects[0];
    await user.selectOptions(typeSelector, PersonType.MORAL);

    const input = screen.getByPlaceholderText('Mentionner une personne ou organisation...');
    await user.type(input, 'Mairie');

    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith('Mairie', PersonType.MORAL);
    });
  });

  it('removes person from list', async () => {
    const user = userEvent.setup();
    const selectedPersons: PersonReference[] = [
      {
        person_id: '1',
        person_code: 'PERSON_001',
        person_name: 'Jean Dupont',
        person_type: PersonType.HUMAN,
        role: 'target'
      }
    ];

    render(
      <PersonMention
        value={selectedPersons}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    const removeButton = screen.getByLabelText('Supprimer');
    await user.click(removeButton);

    expect(mockOnChange).toHaveBeenCalledWith([]);
  });

  it('changes person role', async () => {
    const user = userEvent.setup();
    const selectedPersons: PersonReference[] = [
      {
        person_id: '1',
        person_code: 'PERSON_001',
        person_name: 'Jean Dupont',
        person_type: PersonType.HUMAN,
        role: 'target'
      }
    ];

    render(
      <PersonMention
        value={selectedPersons}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    // Find all select elements - the first is the role selector
    const selects = screen.getAllByRole('combobox');
    const roleSelector = selects[0];
    await user.selectOptions(roleSelector, 'source');

    expect(mockOnChange).toHaveBeenCalledWith([
      {
        ...selectedPersons[0],
        role: 'source'
      }
    ]);
  });

  it('respects max persons limit', () => {
    const selectedPersons: PersonReference[] = Array(3).fill(null).map((_, i) => ({
      person_id: String(i),
      person_code: `PERSON_${i}`,
      person_name: `Person ${i}`,
      person_type: PersonType.HUMAN,
      role: 'target' as const
    }));

    render(
      <PersonMention
        value={selectedPersons}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
        maxPersons={3}
      />
    );

    // L'input ne devrait pas être affiché quand on atteint la limite
    expect(screen.queryByPlaceholderText('Mentionner une personne ou organisation...')).not.toBeInTheDocument();
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <PersonMention
        value={[]}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    const input = screen.getByPlaceholderText('Mentionner une personne ou organisation...');
    await user.type(input, 'test');

    await waitFor(() => {
      expect(screen.getByText('Jean Dupont')).toBeInTheDocument();
    });

    // Navigation avec flèches
    await user.keyboard('{ArrowDown}');
    await user.keyboard('{Enter}');

    // Devrait ajouter la deuxième personne (Mairie de Paris)
    expect(mockOnChange).toHaveBeenCalledWith([
      expect.objectContaining({
        person_name: 'Mairie de Paris',
        person_type: PersonType.MORAL
      })
    ]);
  });

  it('closes dropdown on escape key', async () => {
    const user = userEvent.setup();
    
    render(
      <PersonMention
        value={[]}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    const input = screen.getByPlaceholderText('Mentionner une personne ou organisation...');
    await user.type(input, 'test');

    await waitFor(() => {
      expect(screen.getByText('Jean Dupont')).toBeInTheDocument();
    });

    await user.keyboard('{Escape}');

    await waitFor(() => {
      expect(screen.queryByText('Jean Dupont')).not.toBeInTheDocument();
    });
  });

  it('shows resolution indicator for unresolved persons', () => {
    const unresolvedPersons: PersonReference[] = [
      {
        temp_id: 'temp_123',
        person_name: 'Nouvelle Organisation',
        person_type: PersonType.MORAL,
        role: 'target',
        needs_resolution: true
      }
    ];

    render(
      <PersonMention
        value={unresolvedPersons}
        onChange={mockOnChange}
        onSearch={mockOnSearch}
      />
    );

    expect(screen.getByText('Nouvelle Organisation')).toBeInTheDocument();
    expect(screen.getByTitle('Sera résolu après publication')).toBeInTheDocument();
    expect(screen.getByText('⏳')).toBeInTheDocument();
  });
});