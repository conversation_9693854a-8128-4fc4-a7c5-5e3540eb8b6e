import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ExpressionCard } from '@/components/pillarscan/ExpressionCard';
import { PillarScanExpression } from '@/lib/types/pillarscan';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    svg: ({ children, ...props }: any) => <svg {...props}>{children}</svg>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock the formatRelativeTime utility
jest.mock('@/lib/utils', () => ({
  ...jest.requireActual('@/lib/utils'),
  formatRelativeTime: jest.fn(() => 'il y a 5 minutes'),
}));

// Mock PILLARS data
jest.mock('@/lib/types/pillarscan', () => ({
  ...jest.requireActual('@/lib/types/pillarscan'),
  PILLARS: [
    { id: 'education', name_fr: 'Éducation', emoji: '🎓' },
    { id: 'health', name_fr: 'Santé', emoji: '❤️' },
  ],
}));

describe('ExpressionCard', () => {
  const mockExpression: PillarScanExpression = {
    expression_id: 'test-123',
    text: 'This is a test expression',
    mood: 'happy',
    user_nickname: 'TestUser',
    user_avatar: {
      emoji: '😎',
      color: '#3B82F6',
    },
    created_at: '2024-01-20T10:00:00Z',
    relate_count: 42,
    user_has_related: false,
    visibility_level: 'public',
    location_display_name: 'Paris, France',
    suggested_pillar: 'education',
    earned_badges: ['first_expression', 'mood_master'],
  };

  const mockOnRelate = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders expression text correctly', () => {
      render(<ExpressionCard expression={mockExpression} />);
      expect(screen.getByText('This is a test expression')).toBeInTheDocument();
    });

    it('renders user nickname', () => {
      render(<ExpressionCard expression={mockExpression} />);
      expect(screen.getByText('TestUser')).toBeInTheDocument();
    });

    it('renders anonymous when no nickname provided', () => {
      const anonymousExpression = { ...mockExpression, user_nickname: null };
      render(<ExpressionCard expression={anonymousExpression} />);
      expect(screen.getByText('Anonyme')).toBeInTheDocument();
    });

    it('renders relative time', () => {
      render(<ExpressionCard expression={mockExpression} />);
      expect(screen.getByText('il y a 5 minutes')).toBeInTheDocument();
    });

    it('renders location when provided', () => {
      render(<ExpressionCard expression={mockExpression} />);
      expect(screen.getByText('Paris, France')).toBeInTheDocument();
    });

    it('does not render location when not provided', () => {
      const noLocationExpression = { ...mockExpression, location_display_name: null };
      render(<ExpressionCard expression={noLocationExpression} />);
      expect(screen.queryByText('📍')).not.toBeInTheDocument();
    });

    it('renders mood badge', () => {
      render(<ExpressionCard expression={mockExpression} />);
      expect(screen.getByText('Heureux')).toBeInTheDocument();
    });

    it('renders relate count', () => {
      render(<ExpressionCard expression={mockExpression} />);
      expect(screen.getByText('42')).toBeInTheDocument();
    });

    it('renders earned badges', () => {
      render(<ExpressionCard expression={mockExpression} />);
      const trophies = screen.getAllByText('🏆');
      expect(trophies).toHaveLength(2);
    });

    it('renders visibility level correctly', () => {
      render(<ExpressionCard expression={mockExpression} />);
      expect(screen.getByText('🌍 Public')).toBeInTheDocument();
    });
  });

  describe('Variants', () => {
    it('renders default variant with all elements', () => {
      render(<ExpressionCard expression={mockExpression} variant="default" />);
      expect(screen.getByText('Commenter')).toBeInTheDocument();
      expect(screen.getByText('Partager')).toBeInTheDocument();
    });

    it('renders compact variant with limited elements', () => {
      render(<ExpressionCard expression={mockExpression} variant="compact" />);
      expect(screen.queryByText('Commenter')).not.toBeInTheDocument();
      expect(screen.queryByText('Partager')).not.toBeInTheDocument();
      expect(screen.getByText('This is a test expression')).toBeInTheDocument();
    });

    it('renders detailed variant (defaults to default)', () => {
      render(<ExpressionCard expression={mockExpression} variant="detailed" />);
      expect(screen.getByText('Commenter')).toBeInTheDocument();
    });
  });

  describe('Props validation', () => {
    it('hides actions when showActions is false', () => {
      render(<ExpressionCard expression={mockExpression} showActions={false} />);
      expect(screen.queryByText('Commenter')).not.toBeInTheDocument();
      expect(screen.queryByText('Partager')).not.toBeInTheDocument();
    });

    it('renders without onRelate callback', () => {
      render(<ExpressionCard expression={mockExpression} />);
      expect(screen.getByText('This is a test expression')).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('calls onRelate when relate button is clicked', async () => {
      render(<ExpressionCard expression={mockExpression} onRelate={mockOnRelate} />);
      
      const relateButton = screen.getAllByRole('button').find(btn => 
        btn.textContent?.includes('42')
      );
      
      fireEvent.click(relateButton!);
      
      await waitFor(() => {
        expect(mockOnRelate).toHaveBeenCalledTimes(1);
      });
    });

    it('shows loading state when relating', async () => {
      let resolveRelate: () => void;
      const slowRelate = jest.fn(() => new Promise<void>(resolve => {
        resolveRelate = resolve;
      }));
      
      render(<ExpressionCard expression={mockExpression} onRelate={slowRelate} />);
      
      const relateButton = screen.getAllByRole('button').find(btn => 
        btn.textContent?.includes('42')
      );
      
      fireEvent.click(relateButton!);
      
      // Should show loading state
      await waitFor(() => {
        expect(slowRelate).toHaveBeenCalled();
      });
      
      // Resolve the promise
      resolveRelate!();
    });

    it('displays correct relate button text based on user_has_related', () => {
      const relatedExpression = { ...mockExpression, user_has_related: true };
      const { rerender } = render(<ExpressionCard expression={mockExpression} />);
      
      expect(screen.getByText('Soutenir')).toBeInTheDocument();
      
      rerender(<ExpressionCard expression={relatedExpression} />);
      expect(screen.getByText('Soutenu')).toBeInTheDocument();
    });
  });

  describe('Edge cases', () => {
    it('handles expression with minimal data', () => {
      const minimalExpression: PillarScanExpression = {
        expression_id: 'min-123',
        text: 'Minimal expression',
        mood: 'frustrated',
        user_nickname: null,
        user_avatar: null,
        created_at: '2024-01-20T10:00:00Z',
        relate_count: 0,
        user_has_related: false,
        visibility_level: 'anonymous',
        location_display_name: null,
        suggested_pillar: null,
        earned_badges: null,
      };
      
      render(<ExpressionCard expression={minimalExpression} />);
      expect(screen.getByText('Minimal expression')).toBeInTheDocument();
      expect(screen.getByText('Anonyme')).toBeInTheDocument();
      expect(screen.getByText('👤 Anonyme')).toBeInTheDocument();
    });

    it('handles very long expression text', () => {
      const longTextExpression = {
        ...mockExpression,
        text: 'A'.repeat(500),
      };
      
      render(<ExpressionCard expression={longTextExpression} />);
      const textElement = screen.getByText('A'.repeat(500));
      expect(textElement).toHaveClass('break-words');
    });

    it('handles all mood types correctly', () => {
      const moods: Array<'frustrated' | 'happy' | 'idea' | 'question'> = [
        'frustrated', 'happy', 'idea', 'question'
      ];
      
      moods.forEach(mood => {
        const { unmount } = render(
          <ExpressionCard expression={{ ...mockExpression, mood }} />
        );
        
        const expectedLabels = {
          frustrated: 'Frustré',
          happy: 'Heureux',
          idea: 'Idée',
          question: 'Question',
        };
        
        expect(screen.getByText(expectedLabels[mood])).toBeInTheDocument();
        unmount();
      });
    });

    it('handles all visibility levels correctly', () => {
      const visibilityLevels: Array<'public' | 'anonymous' | 'private'> = [
        'public', 'anonymous', 'private'
      ];
      
      visibilityLevels.forEach(visibility_level => {
        const { unmount } = render(
          <ExpressionCard expression={{ ...mockExpression, visibility_level }} />
        );
        
        const expectedIcons = {
          public: '🌍 Public',
          anonymous: '👤 Anonyme',
          private: '🔒 Privé',
        };
        
        expect(screen.getByText(expectedIcons[visibility_level])).toBeInTheDocument();
        unmount();
      });
    });

    it('renders suggested pillar when available', () => {
      render(<ExpressionCard expression={mockExpression} />);
      // Should show education pillar
      expect(screen.getByText('Éducation')).toBeInTheDocument();
    });

    it('does not render suggested pillar when not available', () => {
      const noPillarExpression = { ...mockExpression, suggested_pillar: null };
      render(<ExpressionCard expression={noPillarExpression} />);
      expect(screen.queryByText('Éducation')).not.toBeInTheDocument();
    });
  });
});