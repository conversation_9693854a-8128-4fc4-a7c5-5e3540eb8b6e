import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ExpressionCard } from '@/components/pillarscan/ExpressionCard';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { PillarScanExpression } from '@/lib/types/pillarscan';

// Wrapper pour les tests avec ThemeProvider
const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  );
};

describe('ExpressionCard - Media Display', () => {
  const baseExpression: PillarScanExpression = {
    expression_id: 'test-123',
    user_id: 'user-456',
    user_nickname: 'TestUser',
    user_avatar: 'minimalist_01',
    text: 'Expression avec images',
    mood: 'happy',
    suggested_pillar: 7,
    visibility_level: 'public',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    country_code: 'FR',
  };

  describe('Format attendu des media_urls', () => {
    it('devrait afficher les images avec URLs présignées MinIO', () => {
      const expressionWithMedia = {
        ...baseExpression,
        has_media: true,
        media_urls: {
          image_0: {
            id: 'media-uuid-1',
            url: 'https://minio.example.com/pillarscan/media-uuid-1.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=...',
            thumbnail_url: 'https://minio.example.com/pillarscan/media-uuid-1-thumb.jpg?X-Amz-Algorithm=...'
          },
          image_1: {
            id: 'media-uuid-2',
            url: 'https://minio.example.com/pillarscan/media-uuid-2.jpg?X-Amz-Algorithm=...',
            thumbnail_url: 'https://minio.example.com/pillarscan/media-uuid-2-thumb.jpg?X-Amz-Algorithm=...'
          }
        }
      };

      renderWithTheme(<ExpressionCard expression={expressionWithMedia} />);
      
      // Vérifier que les images sont rendues
      const images = screen.getAllByRole('img');
      const mediaImages = images.filter(img => 
        img.getAttribute('alt')?.includes('Image image_')
      );
      
      expect(mediaImages).toHaveLength(2);
      expect(mediaImages[0]).toHaveAttribute('src', expect.stringContaining('minio.example.com'));
      expect(mediaImages[0]).toHaveAttribute('src', expect.stringContaining('X-Amz-Algorithm'));
    });

    it('devrait gérer le cas où media_urls est un tableau vide (bug actuel)', () => {
      const expressionWithEmptyArray = {
        ...baseExpression,
        has_media: true,
        media_urls: [] as PillarScanExpression['media_urls'] // Type incorrect mais c'est ce que le backend retourne actuellement
      };

      renderWithTheme(<ExpressionCard expression={expressionWithEmptyArray} />);
      
      // Ne devrait pas crasher
      expect(screen.getByText('Expression avec images')).toBeInTheDocument();
      
      // Pas d'images affichées
      const images = screen.queryAllByRole('img');
      const mediaImages = images.filter(img => 
        img.getAttribute('alt')?.includes('Image image_')
      );
      
      expect(mediaImages).toHaveLength(0);
    });

    it('devrait afficher un placeholder si media_urls absent mais has_media true', () => {
      const expressionWithoutUrls = {
        ...baseExpression,
        has_media: true,
        media_refs: ['media-1', 'media-2'],
        media_urls: undefined
      };

      renderWithTheme(<ExpressionCard expression={expressionWithoutUrls} />);
      
      // Vérifier qu'il n'y a pas d'images mais pas de crash
      const images = screen.queryAllByRole('img');
      const mediaImages = images.filter(img => 
        img.getAttribute('alt')?.includes('Image image_')
      );
      
      expect(mediaImages).toHaveLength(0);
    });
  });

  describe('Fallback pour media_refs', () => {
    it('TODO: devrait construire des URLs temporaires à partir de media_refs', () => {
      // Ce test documente le comportement souhaité pour un fallback
      const expressionWithRefs = {
        ...baseExpression,
        has_media: true,
        media_refs: {
          image_0: 'media-uuid-1',
          image_1: 'media-uuid-2'
        },
        media_urls: undefined
      };

      // Comportement souhaité :
      // Si media_urls est absent, le composant pourrait :
      // 1. Détecter media_refs
      // 2. Construire des URLs temporaires : /api/v2/media/{id}
      // 3. Ou afficher un placeholder "Image en cours de chargement"
      
      // Pour l'instant, ce n'est pas implémenté
      expect(true).toBe(true);
    });
  });

  describe('Gestion des erreurs', () => {
    it('devrait gérer les URLs expirées gracieusement', () => {
      const expressionWithExpiredUrls = {
        ...baseExpression,
        has_media: true,
        media_urls: {
          image_0: {
            id: 'media-uuid-1',
            url: 'https://minio.example.com/expired-url',
            thumbnail_url: 'https://minio.example.com/expired-thumb'
          }
        }
      };

      renderWithTheme(<ExpressionCard expression={expressionWithExpiredUrls} />);
      
      // L'image devrait être rendue même si l'URL est expirée
      // Le navigateur gérera l'erreur 403
      const images = screen.getAllByRole('img');
      const mediaImage = images.find(img => 
        img.getAttribute('alt')?.includes('Image image_')
      );
      
      expect(mediaImage).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('devrait utiliser les thumbnail_url pour les previews', () => {
      const expressionWithThumbnails = {
        ...baseExpression,
        has_media: true,
        media_urls: {
          image_0: {
            id: 'media-uuid-1',
            url: 'https://minio.example.com/full-size.jpg',
            thumbnail_url: 'https://minio.example.com/thumbnail.jpg'
          }
        }
      };

      renderWithTheme(<ExpressionCard expression={expressionWithThumbnails} />);
      
      // Pour l'instant, le composant utilise l'URL complète
      // TODO: Implémenter l'utilisation de thumbnail_url pour les cards
      const images = screen.getAllByRole('img');
      const mediaImage = images.find(img => 
        img.getAttribute('alt')?.includes('Image image_')
      );
      
      expect(mediaImage).toHaveAttribute('src', expect.stringContaining('full-size.jpg'));
      // Idéalement : expect(mediaImage).toHaveAttribute('src', expect.stringContaining('thumbnail.jpg'));
    });
  });
});