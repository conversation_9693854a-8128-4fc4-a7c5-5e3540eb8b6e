import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { StepText } from '@/components/expressions/create/steps/StepText';
import '@testing-library/jest-dom';

describe('StepText - Regression Tests', () => {
  const mockOnTextChange = jest.fn();
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should have proper dark mode contrast for textarea', () => {
    document.documentElement.classList.add('dark');
    
    render(
      <StepText
        text=""
        onTextChange={mockOnTextChange}
        selectedMood="happy"
      />
    );

    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    
    // Check critical dark mode classes
    expect(textarea).toHaveClass('dark:bg-gray-900'); // High contrast background
    expect(textarea).toHaveClass('dark:text-white'); // White text
    expect(textarea).toHaveClass('dark:border-gray-700'); // Visible border
    expect(textarea).toHaveClass('dark:placeholder:text-gray-500'); // Readable placeholder
    
    document.documentElement.classList.remove('dark');
  });

  it('should show minimum character requirement when text is too short', async () => {
    render(
      <StepText
        text=""
        onTextChange={mockOnTextChange}
        selectedMood="happy"
      />
    );

    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    
    // Type less than 10 characters
    await user.type(textarea, 'Hello');
    
    // Should show minimum character hint
    expect(screen.getByText(/Minimum 10 caractères/)).toBeInTheDocument();
    expect(screen.getByText('(5 restants)')).toBeInTheDocument();
  });

  it('should not show minimum character hint when text is valid', async () => {
    render(
      <StepText
        text=""
        onTextChange={mockOnTextChange}
        selectedMood="happy"
      />
    );

    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    
    // Type exactly 10 characters
    await user.type(textarea, 'Hello World');
    
    // Should NOT show minimum character hint
    expect(screen.queryByText(/Minimum 10 caractères/)).not.toBeInTheDocument();
  });

  it('should call onTextChange with correct value', async () => {
    render(
      <StepText
        text=""
        onTextChange={mockOnTextChange}
        selectedMood="happy"
      />
    );

    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    
    // Type text
    await user.type(textarea, 'Test message');
    
    // onTextChange should be called for each character
    expect(mockOnTextChange).toHaveBeenCalledTimes(12);
    expect(mockOnTextChange).toHaveBeenLastCalledWith('Test message');
  });

  it('should show character count correctly', async () => {
    render(
      <StepText
        text="Hello World"
        onTextChange={mockOnTextChange}
        selectedMood="happy"
        maxLength={280}
      />
    );

    // Should show remaining characters (280 - 11 = 269)
    expect(screen.getByText('269')).toBeInTheDocument();
  });

  it('should show warning color when near character limit', () => {
    const longText = 'a'.repeat(265); // 15 chars remaining
    
    render(
      <StepText
        text={longText}
        onTextChange={mockOnTextChange}
        selectedMood="happy"
        maxLength={280}
      />
    );

    // Character counter should have warning color
    const counter = screen.getByText('15');
    expect(counter).toHaveClass('text-yellow-600');
    expect(counter).toHaveClass('dark:text-yellow-400');
  });

  it('should show error color when over character limit', () => {
    const longText = 'a'.repeat(281); // 1 char over limit
    
    render(
      <StepText
        text={longText}
        onTextChange={mockOnTextChange}
        selectedMood="happy"
        maxLength={280}
      />
    );

    // Character counter should show negative and have error color
    const counter = screen.getByText('-1');
    expect(counter).toHaveClass('text-red-600');
    expect(counter).toHaveClass('dark:text-red-400');
    
    // Textarea should have error border
    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    expect(textarea).toHaveClass('border-red-500');
  });

  it('should auto-focus textarea on mount', () => {
    render(
      <StepText
        text=""
        onTextChange={mockOnTextChange}
        selectedMood="happy"
      />
    );

    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    expect(textarea).toHaveFocus();
  });
});