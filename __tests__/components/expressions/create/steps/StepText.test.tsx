import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { StepText } from '@/components/expressions/create/steps/StepText';
import '@testing-library/jest-dom';

describe('StepText', () => {
  const defaultProps = {
    text: '',
    onTextChange: jest.fn(),
    selectedMood: null,
    maxLength: 280,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render with correct initial state', () => {
      render(<StepText {...defaultProps} />);
      
      expect(screen.getByText('Exprimez-vous')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
      expect(screen.getByText('280')).toBeInTheDocument(); // Character counter
    });

    it('should display mood emoji and color', () => {
      render(<StepText {...defaultProps} selectedMood="happy" />);
      
      expect(screen.getByText('😊')).toBeInTheDocument();
      expect(screen.getByText('😊').className).toContain('text-green-500');
    });

    it('should display writing tips when text is empty', () => {
      render(<StepText {...defaultProps} />);
      
      expect(screen.getByText(/Soyez précis et authentique/)).toBeInTheDocument();
    });

    it('should display mood-specific feedback when text length > 50', async () => {
      const { rerender } = render(<StepText {...defaultProps} selectedMood="idea" />);
      
      const longText = 'This is a long text that should trigger the mood-specific feedback message';
      rerender(<StepText {...defaultProps} selectedMood="idea" text={longText} />);
      
      await waitFor(() => {
        expect(screen.getByText(/Les grandes innovations commencent/)).toBeInTheDocument();
      });
    });
  });

  describe('Text Input', () => {
    it('should call onTextChange when typing', async () => {
      const onTextChange = jest.fn();
      render(<StepText {...defaultProps} onTextChange={onTextChange} />);
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      await userEvent.type(textarea, 'Hello world');
      
      expect(onTextChange).toHaveBeenCalledTimes(11); // Once for each character
      expect(onTextChange).toHaveBeenLastCalledWith('Hello world');
    });

    it('should update character counter as user types', async () => {
      const { rerender } = render(<StepText {...defaultProps} />);
      
      expect(screen.getByText('280')).toBeInTheDocument();
      
      rerender(<StepText {...defaultProps} text="Hello" />);
      expect(screen.getByText('275')).toBeInTheDocument();
      
      rerender(<StepText {...defaultProps} text="This is a test message" />);
      expect(screen.getByText('258')).toBeInTheDocument();
    });

    it('should show warning color when near character limit', () => {
      const nearLimitText = 'a'.repeat(265); // 15 chars remaining
      render(<StepText {...defaultProps} text={nearLimitText} />);
      
      const counter = screen.getByText('15');
      expect(counter.className).toContain('text-yellow-600');
    });

    it('should show error color when over character limit', () => {
      const overLimitText = 'a'.repeat(285); // 5 chars over
      render(<StepText {...defaultProps} text={overLimitText} />);
      
      const counter = screen.getByText('-5');
      expect(counter.className).toContain('text-red-600');
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      expect(textarea.className).toContain('border-red-500');
    });

    it('should allow typing beyond limit for better UX', async () => {
      const onTextChange = jest.fn();
      render(<StepText {...defaultProps} onTextChange={onTextChange} />);
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      const longText = 'a'.repeat(300); // Beyond the 280 limit
      
      await userEvent.clear(textarea);
      await userEvent.type(textarea, longText);
      
      // Should still call onTextChange even beyond limit
      expect(onTextChange).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<StepText {...defaultProps} />);
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      expect(textarea).toHaveAttribute('maxLength', '330'); // 280 + 50 overflow
    });

    it('should focus textarea on mount', () => {
      render(<StepText {...defaultProps} />);
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      expect(document.activeElement).toBe(textarea);
    });
  });

  describe('Dark Mode', () => {
    it('should have proper contrast in dark mode', () => {
      // Mock dark mode by checking for dark mode classes
      render(<StepText {...defaultProps} />);
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      expect(textarea.className).toContain('dark:bg-gray-800');
      expect(textarea.className).toContain('dark:text-white');
      expect(textarea.className).toContain('dark:placeholder:text-gray-400');
      expect(textarea.className).toContain('dark:border-gray-600');
    });
  });

  describe('Auto-resize', () => {
    it('should auto-resize textarea based on content', async () => {
      const { rerender } = render(<StepText {...defaultProps} />);
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?') as HTMLTextAreaElement;
      const initialHeight = textarea.style.height;
      
      // Add multi-line text
      const multiLineText = 'Line 1\nLine 2\nLine 3\nLine 4\nLine 5';
      rerender(<StepText {...defaultProps} text={multiLineText} />);
      
      await waitFor(() => {
        // Height should have changed (exact value depends on font/browser)
        expect(textarea.style.height).not.toBe(initialHeight);
      });
    });
  });
});