import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CreateExpressionForm } from '@/components/expressions/create/CreateExpressionForm';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { pillarScanAPI } from '@/lib/api/client';
import '@testing-library/jest-dom';

// Mocks
jest.mock('@/contexts/AuthContext');
jest.mock('next/navigation');
jest.mock('@/lib/api/client');
jest.mock('@/hooks/useStreak', () => ({
  useStreak: () => ({
    recordExpression: jest.fn(),
    currentStreak: 1,
    longestStreak: 1,
    totalExpressions: 5,
  }),
}));
jest.mock('@/hooks/useBadges', () => ({
  useBadges: () => ({
    checkBadges: jest.fn(),
    totalXP: 100,
    level: 2,
    badges: {},
  }),
}));
jest.mock('@/hooks/useGeolocation', () => ({
  useGeolocation: () => ({
    getCurrentPosition: jest.fn(),
    loading: false,
    error: null,
  }),
}));

const mockPush = jest.fn();
const mockAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockAPI = pillarScanAPI as jest.Mocked<typeof pillarScanAPI>;

describe('CreateExpressionForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as ReturnType<typeof useRouter>);

    mockAuth.mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        display_name: 'Test User',
        avatar_url: null,
        country: 'FR',
      },
      isAuthenticated: true,
      isLoading: false,
      login: jest.fn(),
      logout: jest.fn(),
      checkAuth: jest.fn(),
    } as ReturnType<typeof useAuth>);

    mockAPI.createExpression = jest.fn().mockResolvedValue({
      id: '123',
      text: 'Test expression',
      mood: 'happy',
    });
    mockAPI.createExpressionWithMedia = jest.fn().mockResolvedValue({
      id: '123',
      text: 'Test expression',
      mood: 'happy',
    });
  });

  describe('Step Navigation', () => {
    it('should start at step 1 (Mood selection)', () => {
      render(<CreateExpressionForm />);
      
      expect(screen.getByText('Comment vous sentez-vous ?')).toBeInTheDocument();
      expect(screen.getByText('Étape 1 sur 5')).toBeInTheDocument();
    });

    it('should disable next button when step requirements are not met', () => {
      render(<CreateExpressionForm />);
      
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      expect(nextButton).toBeDisabled();
    });

    it('should enable next button after mood selection and advance manually', async () => {
      render(<CreateExpressionForm />);
      
      const happyMood = screen.getByRole('button', { name: /heureux/i });
      await userEvent.click(happyMood);
      
      // In test environment, auto-advance is disabled, so we need to click next
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      expect(nextButton).toBeEnabled();
      
      await userEvent.click(nextButton);
      
      // Should now be on step 2
      await waitFor(() => {
        expect(screen.getByText('Exprimez-vous')).toBeInTheDocument();
        expect(screen.getByText('Étape 2 sur 5')).toBeInTheDocument();
      });
    });

    it('should navigate back with previous button', async () => {
      render(<CreateExpressionForm />);
      
      // Go to step 2
      const happyMood = screen.getByRole('button', { name: /heureux/i });
      await userEvent.click(happyMood);
      
      // Click next to go to step 2 (auto-advance disabled in tests)
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      await userEvent.click(nextButton);
      
      await waitFor(() => {
        expect(screen.getByText('Étape 2 sur 5')).toBeInTheDocument();
      });
      
      // Go back to step 1
      const prevButton = screen.getByRole('button', { name: /précédent/i });
      await userEvent.click(prevButton);
      
      expect(screen.getByText('Étape 1 sur 5')).toBeInTheDocument();
    });
  });

  describe('Step 1 - Mood Selection', () => {
    it('should display all mood options', () => {
      render(<CreateExpressionForm />);
      
      expect(screen.getByRole('button', { name: /frustré/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /heureux/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /idée/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /question/i })).toBeInTheDocument();
    });

    it('should highlight selected mood', async () => {
      render(<CreateExpressionForm />);
      
      const ideaMood = screen.getByRole('button', { name: /idée/i });
      await userEvent.click(ideaMood);
      
      // Check for visual indicator (checkmark)
      const checkmark = ideaMood.querySelector('svg');
      expect(checkmark).toBeInTheDocument();
    });
  });

  describe('Step 2 - Text Input', () => {
    it('should validate text length', async () => {
      render(<CreateExpressionForm />);
      
      // Navigate to step 2
      const happyMood = screen.getByRole('button', { name: /heureux/i });
      await userEvent.click(happyMood);
      
      // Click next to go to step 2
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      await userEvent.click(nextButton);
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
      });
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      
      // Too short
      await userEvent.type(textarea, 'Short');
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /suivant/i })).toBeDisabled();
      });
      
      // Valid length
      await userEvent.clear(textarea);
      await userEvent.type(textarea, 'This is a valid expression with enough characters');
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /suivant/i })).toBeEnabled();
      });
    });

    it('should show character counter', async () => {
      render(<CreateExpressionForm />);
      
      // Navigate to step 2
      const happyMood = screen.getByRole('button', { name: /heureux/i });
      await userEvent.click(happyMood);
      
      // Click next to go to step 2
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      await userEvent.click(nextButton);
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
      });
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      await userEvent.type(textarea, 'Test text');
      
      // Character counter should show remaining characters (280 - 9)
      await waitFor(() => {
        expect(screen.getByText('271')).toBeInTheDocument();
      });
    });
  });

  describe('Step 5 - Submission', () => {
    it('should submit expression with all data', async () => {
      render(<CreateExpressionForm />);
      
      // Complete all steps
      // Step 1: Mood
      await userEvent.click(screen.getByRole('button', { name: /heureux/i }));
      await userEvent.click(screen.getByRole('button', { name: /suivant/i }));
      
      // Step 2: Text
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
      });
      await userEvent.type(
        screen.getByPlaceholderText('Que voulez-vous exprimer ?'),
        'This is my test expression for the community'
      );
      await userEvent.click(screen.getByRole('button', { name: /suivant/i }));
      
      // Step 3: Persons & Pillars (keep defaults)
      await waitFor(() => {
        expect(screen.getByText('Contexte et Classification')).toBeInTheDocument();
      });
      await userEvent.click(screen.getByRole('button', { name: /suivant/i }));
      
      // Step 4: Media & Location (skip)
      await waitFor(() => {
        expect(screen.getByText('Médias et Localisation')).toBeInTheDocument();
      });
      await userEvent.click(screen.getByRole('button', { name: /suivant/i }));
      
      // Step 5: Visibility & Confirm
      await waitFor(() => {
        expect(screen.getByText('Visibilité et Confirmation')).toBeInTheDocument();
      });
      
      // Submit
      const submitButton = screen.getByRole('button', { name: /publier/i });
      await userEvent.click(submitButton);
      
      await waitFor(() => {
        expect(mockAPI.createExpression).toHaveBeenCalledWith({
          text: 'This is my test expression for the community',
          mood: 'happy',
          suggested_pillar: 7, // "Autre" pillar
          visibility_level: 'public',
          person_references: [],
          location: undefined,
        });
      });
      
      // Should show success and redirect
      await waitFor(() => {
        expect(screen.getByText('Expression créée !')).toBeInTheDocument();
      });
      
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/');
      }, { timeout: 3000 });
    });

    it('should handle submission errors', async () => {
      mockAPI.createExpression = jest.fn().mockRejectedValueOnce(new Error('Network error'));
      
      render(<CreateExpressionForm />);
      
      // Quick navigation to last step
      // ... (similar to above but abbreviated)
      
      // Navigate to step 5 and submit
      // The actual implementation would show an error toast
    });
  });

  describe('Cancel Action', () => {
    it('should navigate back to home when cancel is clicked', async () => {
      render(<CreateExpressionForm />);
      
      const cancelButton = screen.getByRole('button', { name: /annuler/i });
      await userEvent.click(cancelButton);
      
      expect(mockPush).toHaveBeenCalledWith('/');
    });
  });
});