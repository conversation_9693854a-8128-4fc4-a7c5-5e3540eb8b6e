import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CreateExpressionForm } from '@/components/expressions/create/CreateExpressionForm';
import { AuthProvider } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import '@testing-library/jest-dom';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock API client
jest.mock('@/lib/api/client', () => ({
  pillarScanAPI: {
    createExpression: jest.fn().mockResolvedValue({ id: 1 }),
    createExpressionWithMedia: jest.fn().mockResolvedValue({ id: 1 }),
  },
}));

// Mock toast
jest.mock('react-hot-toast', () => ({
  __esModule: true,
  default: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock hooks
jest.mock('@/hooks/useStreak', () => ({
  useStreak: () => ({
    recordExpression: jest.fn(),
  }),
}));

jest.mock('@/hooks/useBadges', () => ({
  useBadges: () => ({
    checkBadges: jest.fn(),
    totalXP: 100,
    level: 1,
    badges: {},
  }),
}));

const mockUser = {
  id: 1,
  email: '<EMAIL>',
};

const mockProfile = {
  nickname: 'TestUser',
};

describe('CreateExpressionForm - Integration Tests (Non-Regression)', () => {
  const mockPush = jest.fn();
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });
  });

  const renderWithAuth = () => {
    return render(
      <AuthProvider>
        <CreateExpressionForm />
      </AuthProvider>
    );
  };

  describe('Navigation Flow', () => {
    it('should navigate through all steps with proper button states', async () => {
      renderWithAuth();

      // Step 1: Mood Selection
      expect(screen.getByText('Étape 1 sur 5')).toBeInTheDocument();
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      expect(nextButton).toBeDisabled();

      // Select mood
      const happyMood = screen.getByText('😊');
      await user.click(happyMood);
      
      // Wait for next button to be enabled
      await waitFor(() => {
        expect(nextButton).not.toBeDisabled();
      });

      // Go to step 2
      await user.click(nextButton);

      // Step 2: Text Input
      await waitFor(() => {
        expect(screen.getByText('Étape 2 sur 5')).toBeInTheDocument();
      });
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      expect(textarea).toBeInTheDocument();
      expect(nextButton).toBeDisabled(); // Should be disabled with empty text

      // Type less than 10 characters
      await user.type(textarea, 'Test');
      expect(screen.getByText(/Minimum 10 caractères/)).toBeInTheDocument();
      expect(nextButton).toBeDisabled();

      // Type valid text
      await user.clear(textarea);
      await user.type(textarea, 'Ceci est un test d\'expression valide');
      
      await waitFor(() => {
        expect(nextButton).not.toBeDisabled();
      });

      // Continue through remaining steps
      await user.click(nextButton);

      // Step 3: Persons & Pillars
      await waitFor(() => {
        expect(screen.getByText('Étape 3 sur 5')).toBeInTheDocument();
      });
      expect(nextButton).not.toBeDisabled(); // Should have default pillar

      await user.click(nextButton);

      // Step 4: Media & Location
      await waitFor(() => {
        expect(screen.getByText('Étape 4 sur 5')).toBeInTheDocument();
      });
      expect(nextButton).not.toBeDisabled(); // Optional step

      await user.click(nextButton);

      // Step 5: Visibility & Confirm
      await waitFor(() => {
        expect(screen.getByText('Étape 5 sur 5')).toBeInTheDocument();
      });
      
      const publishButton = screen.getByRole('button', { name: /publier/i });
      expect(publishButton).toBeInTheDocument();
      expect(publishButton).not.toBeDisabled();
    });

    it('should allow going back to previous steps', async () => {
      renderWithAuth();

      // Go to step 2
      const happyMood = screen.getByText('😊');
      await user.click(happyMood);
      
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      await waitFor(() => expect(nextButton).not.toBeDisabled());
      await user.click(nextButton);

      // Should be on step 2
      await waitFor(() => {
        expect(screen.getByText('Étape 2 sur 5')).toBeInTheDocument();
      });

      // Go back to step 1
      const prevButton = screen.getByRole('button', { name: /précédent/i });
      await user.click(prevButton);

      // Should be back on step 1
      await waitFor(() => {
        expect(screen.getByText('Étape 1 sur 5')).toBeInTheDocument();
      });

      // Previous button should be disabled on first step
      expect(prevButton).toBeDisabled();
    });
  });

  describe('Dark Mode Contrast', () => {
    it('should have proper contrast for text input in dark mode', async () => {
      // Mock dark mode
      document.documentElement.classList.add('dark');
      
      renderWithAuth();

      // Navigate to text step
      const happyMood = screen.getByText('😊');
      await user.click(happyMood);
      
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      await waitFor(() => expect(nextButton).not.toBeDisabled());
      await user.click(nextButton);

      // Check textarea has proper dark mode classes
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      expect(textarea).toHaveClass('dark:bg-gray-900');
      expect(textarea).toHaveClass('dark:text-white');
      expect(textarea).toHaveClass('dark:border-gray-700');
      
      // Cleanup
      document.documentElement.classList.remove('dark');
    });
  });

  describe('Form Validation', () => {
    it('should validate text length requirements', async () => {
      renderWithAuth();

      // Go to text step
      const happyMood = screen.getByText('😊');
      await user.click(happyMood);
      
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      await waitFor(() => expect(nextButton).not.toBeDisabled());
      await user.click(nextButton);

      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');

      // Test minimum length
      await user.type(textarea, '123456789'); // 9 chars
      expect(screen.getByText(/Minimum 10 caractères/)).toBeInTheDocument();
      expect(nextButton).toBeDisabled();

      // Test valid length
      await user.type(textarea, '0'); // Now 10 chars
      await waitFor(() => {
        expect(screen.queryByText(/Minimum 10 caractères/)).not.toBeInTheDocument();
        expect(nextButton).not.toBeDisabled();
      });

      // Test maximum length
      const longText = 'a'.repeat(281);
      await user.clear(textarea);
      await user.type(textarea, longText);
      
      // Should show negative character count
      expect(screen.getByText('-1')).toBeInTheDocument();
      expect(nextButton).toBeDisabled();
    });
  });

  describe('Progress Bar', () => {
    it('should update progress bar as user advances through steps', async () => {
      renderWithAuth();

      // Initial progress should be 20% (1/5)
      const progressBar = screen.getByRole('progressbar', { hidden: true });
      expect(progressBar).toHaveStyle({ width: '20%' });

      // Select mood and go to step 2
      const happyMood = screen.getByText('😊');
      await user.click(happyMood);
      
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      await waitFor(() => expect(nextButton).not.toBeDisabled());
      await user.click(nextButton);

      // Progress should be 40% (2/5)
      await waitFor(() => {
        expect(progressBar).toHaveStyle({ width: '40%' });
      });
    });
  });
});