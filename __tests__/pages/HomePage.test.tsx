import React from 'react';
import { render, screen } from '@testing-library/react';
import HomePage from '../../app/page';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

// Mock dependencies
jest.mock('@/contexts/AuthContext');
jest.mock('next/navigation');
jest.mock('next/link', () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  );
});

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock components
jest.mock('@/components/ui/Button', () => ({
  Button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
}));

jest.mock('@/components/feed/ExpressionFeed', () => ({
  ExpressionFeed: () => <div data-testid="expression-feed">Expression Feed</div>,
}));

jest.mock('@/components/gamification/StreakDisplay', () => ({
  StreakDisplay: ({ variant }: { variant: string }) => (
    <div data-testid="streak-display" data-variant={variant}>Streak Display</div>
  ),
  StreakAlert: () => <div data-testid="streak-alert">Streak Alert</div>,
}));

jest.mock('@/components/gamification/BadgeDisplay', () => ({
  BadgeNotification: () => <div data-testid="badge-notification">Badge Notification</div>,
}));

jest.mock('@/components/illustrations/EmptyState', () => ({
  HeroIllustration: () => <div data-testid="hero-illustration">Hero Illustration</div>,
}));

// Mock Button component
jest.mock('@/components/ui/Button', () => ({
  Button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
}));

// Mock Card component
jest.mock('@/components/ui/Card', () => ({
  Card: ({ children, ...props }: any) => <div {...props}>{children}</div>,
}));

describe('HomePage', () => {
  const mockRouter = {
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  describe('when user is not authenticated', () => {
    beforeEach(() => {
      (useAuth as jest.Mock).mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        profile: null,
      });
    });

    it('renders the homepage with hero section', () => {
      render(<HomePage />);

      expect(screen.getByText('Exprimez vos')).toBeInTheDocument();
      expect(screen.getByText('besoins citoyens')).toBeInTheDocument();
      expect(screen.getByText(/Rejoignez une communauté engagée/)).toBeInTheDocument();
    });

    it('shows authentication buttons for non-authenticated users', () => {
      render(<HomePage />);

      expect(screen.getByText('Commencer gratuitement')).toBeInTheDocument();
      expect(screen.getByText('Se connecter')).toBeInTheDocument();
      expect(screen.queryByText('Nouvelle expression')).not.toBeInTheDocument();
    });

    it('displays statistics cards', () => {
      render(<HomePage />);

      expect(screen.getByText('10K+')).toBeInTheDocument();
      expect(screen.getByText('Expressions')).toBeInTheDocument();
      expect(screen.getByText('2.5K+')).toBeInTheDocument();
      expect(screen.getByText('Citoyens actifs')).toBeInTheDocument();
      expect(screen.getByText('150+')).toBeInTheDocument();
      expect(screen.getByText('Villes connectées')).toBeInTheDocument();
      expect(screen.getByText('95%')).toBeInTheDocument();
      expect(screen.getByText('Impact positif')).toBeInTheDocument();
    });

    it('shows CTA section for non-authenticated users', () => {
      render(<HomePage />);

      expect(screen.getByText('Prêt à faire entendre votre voix ?')).toBeInTheDocument();
      expect(screen.getByText(/Rejoignez des milliers de citoyens/)).toBeInTheDocument();
      expect(screen.getByText('Créer mon compte gratuitement')).toBeInTheDocument();
    });

    it('does not show streak display for non-authenticated users', () => {
      render(<HomePage />);

      expect(screen.queryByTestId('streak-display')).not.toBeInTheDocument();
      expect(screen.queryByText('Voir mon profil complet →')).not.toBeInTheDocument();
    });

    it('does not show floating action button', () => {
      render(<HomePage />);

      const floatingButton = screen.queryByRole('link', { name: '' });
      expect(floatingButton).not.toBeInTheDocument();
    });
  });

  describe('when user is authenticated', () => {
    beforeEach(() => {
      (useAuth as jest.Mock).mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        user: { email: '<EMAIL>', id: '123' },
        profile: { nickname: 'TestUser' },
      });
    });

    it('shows create expression button instead of auth buttons', () => {
      render(<HomePage />);

      expect(screen.getByText('Nouvelle expression')).toBeInTheDocument();
      expect(screen.queryByText('Commencer gratuitement')).not.toBeInTheDocument();
      expect(screen.queryByText('Se connecter')).not.toBeInTheDocument();
    });

    it('displays streak information for authenticated users', () => {
      render(<HomePage />);

      expect(screen.getByTestId('streak-display')).toBeInTheDocument();
      expect(screen.getByTestId('streak-display')).toHaveAttribute('data-variant', 'full');
      expect(screen.getByText('Voir mon profil complet →')).toBeInTheDocument();
    });

    it('does not show CTA section for authenticated users', () => {
      render(<HomePage />);

      expect(screen.queryByText('Prêt à faire entendre votre voix ?')).not.toBeInTheDocument();
      expect(screen.queryByText('Créer mon compte gratuitement')).not.toBeInTheDocument();
    });

    it('shows floating action button on mobile for authenticated users', () => {
      render(<HomePage />);

      // The floating button is rendered conditionally on mobile
      // Since jsdom doesn't have actual viewport size, we'll just check
      // that the create button exists
      const createButtons = screen.getAllByRole('link');
      const createButton = createButtons.find(link => 
        link.getAttribute('href') === '/create'
      );
      
      expect(createButton).toBeInTheDocument();
    });
  });

  it('renders common components regardless of auth state', () => {
    (useAuth as jest.Mock).mockReturnValue({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      profile: null,
    });

    render(<HomePage />);

    // Check for common components
    expect(screen.getByTestId('streak-alert')).toBeInTheDocument();
    expect(screen.getByTestId('badge-notification')).toBeInTheDocument();
    expect(screen.getByTestId('hero-illustration')).toBeInTheDocument();
    expect(screen.getByTestId('expression-feed')).toBeInTheDocument();
    expect(screen.getByText('Expressions récentes de votre communauté')).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    (useAuth as jest.Mock).mockReturnValue({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      profile: null,
    });

    render(<HomePage />);

    // Check for proper heading hierarchy
    const mainHeading = screen.getByRole('heading', { level: 1 });
    expect(mainHeading).toHaveTextContent('Exprimez vos');

    const subHeadings = screen.getAllByRole('heading', { level: 2 });
    expect(subHeadings.length).toBeGreaterThan(0);
  });

  it('renders SVG wave decoration', () => {
    render(<HomePage />);

    const svgElements = document.querySelectorAll('svg');
    const waveSvg = Array.from(svgElements).find(svg => 
      svg.getAttribute('viewBox') === '0 0 1440 120'
    );
    
    expect(waveSvg).toBeInTheDocument();
  });
});