import { describe, it, expect } from '@jest/globals';
import { 
  NetworkError, 
  AuthenticationError, 
  CountrySelectionError, 
  ServerError, 
  ValidationError,
  handleAPIError
} from '@/lib/api/errors';

describe('API Error Handling', () => {
  describe('NetworkError', () => {
    it('should create network error with user-friendly message', () => {
      const originalError = new Error('Failed to fetch');
      const error = new NetworkError(originalError);
      
      expect(error.message).toBe('Impossible de se connecter au serveur. Veuillez vérifier votre connexion internet ou réessayer plus tard.');
      expect(error.name).toBe('NetworkError');
      expect(error.originalError).toBe(originalError);
    });
  });

  describe('AuthenticationError', () => {
    it('should create auth error with session expired message', () => {
      const error = new AuthenticationError('Invalid token');
      
      expect(error.message).toBe('Votre session a expiré. Veuillez vous reconnecter.');
      expect(error.status).toBe(401);
      expect(error.detail).toBe('Invalid token');
    });
  });

  describe('ServerError', () => {
    it('should create server error with generic message', () => {
      const error = new ServerError(500, 'Internal server error');
      
      expect(error.message).toBe('Une erreur serveur est survenue. Veuillez réessayer plus tard.');
      expect(error.status).toBe(500);
      expect(error.detail).toBe('Internal server error');
    });
  });

  describe('handleAPIError', () => {
    it('should transform TypeError Failed to fetch to NetworkError', () => {
      const typeError = new TypeError('Failed to fetch');
      const result = handleAPIError(typeError);
      
      expect(result).toBeInstanceOf(NetworkError);
      expect(result.message).toContain('connexion internet');
    });

    it('should pass through APIError instances', () => {
      const apiError = new ValidationError('Invalid input');
      const result = handleAPIError(apiError);
      
      expect(result).toBe(apiError);
    });

    it('should handle unknown errors', () => {
      const result = handleAPIError('Some string error');
      
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('Une erreur inattendue est survenue.');
    });
  });
});