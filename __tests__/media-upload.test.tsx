import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { pillarScanAPI } from '@/lib/api/client';

// Mock fetch
global.fetch = jest.fn() as jest.MockedFunction<typeof fetch>;

// Helper to create mock response
const createMockResponse = (ok: boolean, status: number, data: unknown, isJson = true) => ({
  ok,
  status,
  statusText: ok ? 'OK' : 'Error',
  headers: {
    entries: () => [],
    get: (_key: string) => null
  },
  json: isJson ? async () => data : undefined,
  text: !isJson ? async () => data : async () => JSON.stringify(data)
} as unknown as Response);

describe('Media Upload Pipeline', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set auth token
    localStorage.setItem('auth_token', 'test-token');
    localStorage.setItem('country_code', 'FR');
  });

  describe('createExpressionWithMedia', () => {
    it('should create expression without media when no files provided', async () => {
      const mockExpression = {
        expression_id: 'test-123',
        mood: 'happy',
        text: 'Test expression',
        media_refs: [],
        media_urls: []
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce(
        createMockResponse(true, 201, mockExpression)
      );

      const result = await pillarScanAPI.createExpressionWithMedia(
        {
          mood: 'happy',
          text: 'Test expression',
          visibility_level: 'public'
        },
        []
      );

      expect(result).toEqual(mockExpression);
      expect(global.fetch).toHaveBeenCalledTimes(1);
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v2/pillarscan/expressions/'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token',
            'X-Country-Code': 'FR'
          })
        })
      );
    });

    it('should create expression and upload media in two steps', async () => {
      const mockExpression = {
        expression_id: 'test-123',
        mood: 'happy',
        text: 'Test with image',
        media_refs: [],
        media_urls: []
      };

      const mockUploadResult = {
        media_id: 'media-456',
        url: 'https://cdn.example.com/test.png',
        size: 1234,
        content_type: 'image/png'
      };

      const mockUpdatedExpression = {
        ...mockExpression,
        media_refs: ['media-456'],
        media_urls: [{
          id: 'media-456',
          url: 'https://cdn.example.com/test.png?signature=xxx'
        }]
      };

      // Mock create expression
      (global.fetch as jest.Mock).mockResolvedValueOnce(
        createMockResponse(true, 201, mockExpression)
      );

      // Mock upload media
      (global.fetch as jest.Mock).mockResolvedValueOnce(
        createMockResponse(true, 201, mockUploadResult)
      );

      // Mock get updated expression
      (global.fetch as jest.Mock).mockResolvedValueOnce(
        createMockResponse(true, 200, mockUpdatedExpression)
      );

      const testFile = new File(['test'], 'test.png', { type: 'image/png' });
      const result = await pillarScanAPI.createExpressionWithMedia(
        {
          mood: 'happy',
          text: 'Test with image',
          visibility_level: 'public'
        },
        [testFile]
      );

      expect(result).toEqual(mockUpdatedExpression);
      expect(global.fetch).toHaveBeenCalledTimes(3);

      // Verify step 1: create expression
      expect(global.fetch).toHaveBeenNthCalledWith(1,
        expect.stringContaining('/api/v2/pillarscan/expressions/'),
        expect.objectContaining({
          method: 'POST'
        })
      );

      // Verify step 2: upload media
      expect(global.fetch).toHaveBeenNthCalledWith(2,
        expect.stringContaining(`/api/v2/pillarscan/expressions/${mockExpression.expression_id}/upload_media/`),
        expect.objectContaining({
          method: 'POST',
          body: expect.any(FormData)
        })
      );

      // Verify step 3: get updated expression
      expect(global.fetch).toHaveBeenNthCalledWith(3,
        expect.stringContaining(`/api/v2/pillarscan/expressions/${mockExpression.expression_id}/`),
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('should handle upload failure gracefully', async () => {
      const mockExpression = {
        expression_id: 'test-123',
        mood: 'happy',
        text: 'Test expression',
        media_refs: [],
        media_urls: []
      };

      // Mock create expression success
      (global.fetch as jest.Mock).mockResolvedValueOnce(
        createMockResponse(true, 201, mockExpression)
      );

      // Mock upload media failure
      (global.fetch as jest.Mock).mockResolvedValueOnce(
        createMockResponse(false, 500, 'Internal server error', false)
      );

      const testFile = new File(['test'], 'test.png', { type: 'image/png' });
      const result = await pillarScanAPI.createExpressionWithMedia(
        {
          mood: 'happy',
          text: 'Test expression',
          visibility_level: 'public'
        },
        [testFile]
      );

      // Should return expression even if upload fails
      expect(result).toEqual(mockExpression);
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });

    it('should include X-Country-Code header in all requests', async () => {
      const mockExpression = {
        expression_id: 'test-123',
        mood: 'happy',
        text: 'Test expression',
        media_refs: [],
        media_urls: []
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce(
        createMockResponse(true, 201, mockExpression)
      );

      await pillarScanAPI.createExpressionWithMedia(
        {
          mood: 'happy',
          text: 'Test expression',
          visibility_level: 'public'
        },
        []
      );

      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-Country-Code': 'FR'
          })
        })
      );
    });
  });
});

// Test de non-régression pour s'assurer que les anciennes fonctionnalités marchent toujours
describe('Non-regression tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.setItem('auth_token', 'test-token');
    localStorage.setItem('country_code', 'FR');
  });

  it('should still support getExpressions', async () => {
    const mockResponse = {
      results: [],
      count: 0
    };

    (global.fetch as jest.Mock).mockResolvedValueOnce(
      createMockResponse(true, 200, mockResponse)
    );

    const result = await pillarScanAPI.getExpressions();
    expect(result).toEqual(mockResponse);
  });

  it('should still support createExpression without media', async () => {
    const mockExpression = {
      expression_id: 'test-123',
      mood: 'happy',
      text: 'Test expression'
    };

    (global.fetch as jest.Mock).mockResolvedValueOnce(
      createMockResponse(true, 201, mockExpression)
    );

    const result = await pillarScanAPI.createExpression({
      mood: 'happy',
      text: 'Test expression',
      visibility_level: 'public'
    });

    expect(result).toEqual(mockExpression);
  });
});