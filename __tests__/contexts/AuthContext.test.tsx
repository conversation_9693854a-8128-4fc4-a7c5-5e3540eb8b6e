import React from 'react';
import { render, screen, waitFor, act, renderHook } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider, useAuth, withAuth, useRequireProfile } from '@/contexts/AuthContext';
import { pillarScanAPI } from '@/lib/api/client';
import { useRouter } from 'next/navigation';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock the API client
jest.mock('@/lib/api/client', () => ({
  pillarScanAPI: {
    getToken: jest.fn(),
    clearToken: jest.fn(),
    getMyProfile: jest.fn(),
    createProfile: jest.fn(),
    login: jest.fn(),
    logout: jest.fn(),
    refreshToken: jest.fn(),
  },
}));

// Helper component to test the hook
const TestComponent = () => {
  const auth = useAuth();
  return (
    <div>
      <div data-testid="is-authenticated">{auth.isAuthenticated.toString()}</div>
      <div data-testid="is-loading">{auth.isLoading.toString()}</div>
      <div data-testid="user-email">{auth.user?.email || 'no-user'}</div>
      <div data-testid="user-id">{auth.user?.id || 'no-id'}</div>
      <div data-testid="user-person-id">{auth.user?.person_id || 'no-person-id'}</div>
      <div data-testid="profile-nickname">{auth.profile?.nickname || 'no-profile'}</div>
      <button onClick={() => auth.login('<EMAIL>', 'password')}>Login</button>
      <button onClick={() => auth.logout()}>Logout</button>
      <button onClick={() => auth.refreshAuth()}>Refresh</button>
      <button onClick={() => auth.updateProfile({ ...auth.profile!, nickname: 'UpdatedNick' })}>Update Profile</button>
    </div>
  );
};

// Test component for protected routes
const ProtectedComponent = () => {
  return <div data-testid="protected-content">Protected Content</div>;
};

const ProtectedWithAuth = withAuth(ProtectedComponent);

// Test component for useRequireProfile hook
const ProfileRequiredComponent = () => {
  const profile = useRequireProfile();
  return <div data-testid="profile-required">{profile?.nickname || 'no-profile'}</div>;
};

describe('AuthContext', () => {
  const mockRouter = {
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    // Clear localStorage
    localStorage.clear();
    sessionStorage.clear();
  });

  it('provides auth context to children', () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    expect(screen.getByTestId('is-authenticated')).toBeInTheDocument();
    expect(screen.getByTestId('is-loading')).toBeInTheDocument();
  });

  it('throws error when useAuth is used outside AuthProvider', () => {
    // Suppress console.error for this test
    const originalError = console.error;
    console.error = jest.fn();

    expect(() => render(<TestComponent />)).toThrow(
      'useAuth doit être utilisé dans un AuthProvider'
    );

    console.error = originalError;
  });

  it('initializes with loading state', async () => {
    (pillarScanAPI.getToken as jest.Mock).mockReturnValue(null);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Since there's no token, loading should quickly become false
    await waitFor(() => {
      expect(screen.getByTestId('is-loading')).toHaveTextContent('false');
    });
    
    expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false');
  });

  it('checks authentication on mount when token exists', async () => {
    const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
    const mockProfile = {
      person_id: '123',
      nickname: 'TestUser',
      bio: 'Test bio',
      avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
      preferred_language: 'fr',
    };

    (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
    (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('is-authenticated')).toHaveTextContent('true');
      expect(screen.getByTestId('is-loading')).toHaveTextContent('false');
      expect(screen.getByTestId('profile-nickname')).toHaveTextContent('TestUser');
    });
  });

  it('handles expired token correctly', async () => {
    // Create an expired token
    const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.fR0Z4RYjdF7qV7XGkK6O4qVHyaG8yW0wIfBZbVkLKDI';
    
    (pillarScanAPI.getToken as jest.Mock).mockReturnValue(expiredToken);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(pillarScanAPI.clearToken).toHaveBeenCalled();
      expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false');
      expect(screen.getByTestId('is-loading')).toHaveTextContent('false');
    });
  });

  it('creates profile automatically when 404 error occurs', async () => {
    const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
    const newProfile = {
      person_id: '123',
      nickname: 'User1234',
      bio: '',
      avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
      preferred_language: 'fr',
    };

    (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
    (pillarScanAPI.getMyProfile as jest.Mock).mockRejectedValueOnce(
      new Error('Profil PillarScan non trouvé')
    );
    (pillarScanAPI.createProfile as jest.Mock).mockResolvedValue(newProfile);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(pillarScanAPI.createProfile).toHaveBeenCalled();
      expect(sessionStorage.getItem('needsOnboarding')).toBe('true');
      expect(screen.getByTestId('profile-nickname')).toHaveTextContent('User1234');
    });
  });

  it('handles login successfully', async () => {
    const user = userEvent.setup();
    const mockLoginResponse = {
      access: 'new-access-token',
      refresh: 'new-refresh-token',
    };
    const mockProfile = {
      person_id: '123',
      nickname: 'TestUser',
      bio: 'Test bio',
      avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
      preferred_language: 'fr',
    };

    (pillarScanAPI.login as jest.Mock).mockResolvedValue(mockLoginResponse);
    (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await act(async () => {
      await user.click(screen.getByText('Login'));
    });

    await waitFor(() => {
      expect(pillarScanAPI.login).toHaveBeenCalledWith('<EMAIL>', 'password');
      expect(localStorage.getItem('pillarscan_refresh_token')).toBe('new-refresh-token');
      expect(screen.getByTestId('is-authenticated')).toHaveTextContent('true');
      expect(mockRouter.push).toHaveBeenCalledWith('/');
    });
  });

  it('redirects to onboarding after login if needed', async () => {
    const user = userEvent.setup();
    sessionStorage.setItem('needsOnboarding', 'true');

    const mockLoginResponse = {
      access: 'new-access-token',
      refresh: 'new-refresh-token',
    };
    const mockProfile = {
      person_id: '123',
      nickname: 'TestUser',
      bio: '',
      avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
      preferred_language: 'fr',
    };

    (pillarScanAPI.login as jest.Mock).mockResolvedValue(mockLoginResponse);
    (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await act(async () => {
      await user.click(screen.getByText('Login'));
    });

    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('/onboarding');
      expect(sessionStorage.getItem('needsOnboarding')).toBeNull();
    });
  });

  it('handles logout successfully', async () => {
    const user = userEvent.setup();
    
    // Setup initial authenticated state
    const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
    const mockProfile = {
      person_id: '123',
      nickname: 'TestUser',
      bio: 'Test bio',
      avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
      preferred_language: 'fr',
    };

    (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
    (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);
    (pillarScanAPI.logout as jest.Mock).mockResolvedValue(undefined);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Wait for initial auth check
    await waitFor(() => {
      expect(screen.getByTestId('is-authenticated')).toHaveTextContent('true');
    });

    // Perform logout
    await act(async () => {
      await user.click(screen.getByText('Logout'));
    });

    await waitFor(() => {
      expect(pillarScanAPI.logout).toHaveBeenCalled();
      expect(pillarScanAPI.clearToken).toHaveBeenCalled();
      expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false');
      expect(screen.getByTestId('profile-nickname')).toHaveTextContent('no-profile');
      expect(mockRouter.push).toHaveBeenCalledWith('/');
    });
  });

  it('handles refresh token successfully', async () => {
    const user = userEvent.setup();
    localStorage.setItem('pillarscan_refresh_token', 'valid-refresh-token');

    const mockProfile = {
      person_id: '123',
      nickname: 'RefreshedUser',
      bio: 'Refreshed bio',
      avatar_style: { color: '#4ECDC4', emoji: '🌊', pattern: 'lines' },
      preferred_language: 'en',
    };

    (pillarScanAPI.refreshToken as jest.Mock).mockResolvedValue(undefined);
    (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await act(async () => {
      await user.click(screen.getByText('Refresh'));
    });

    await waitFor(() => {
      expect(pillarScanAPI.refreshToken).toHaveBeenCalledWith('valid-refresh-token');
      expect(screen.getByTestId('is-authenticated')).toHaveTextContent('true');
      expect(screen.getByTestId('profile-nickname')).toHaveTextContent('RefreshedUser');
    });
  });

  it('logs out when refresh token fails', async () => {
    const user = userEvent.setup();
    localStorage.setItem('pillarscan_refresh_token', 'invalid-refresh-token');

    (pillarScanAPI.refreshToken as jest.Mock).mockRejectedValue(new Error('Invalid token'));
    (pillarScanAPI.logout as jest.Mock).mockResolvedValue(undefined);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await act(async () => {
      await user.click(screen.getByText('Refresh'));
    });

    await waitFor(() => {
      expect(pillarScanAPI.logout).toHaveBeenCalled();
      expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false');
      expect(mockRouter.push).toHaveBeenCalledWith('/');
    });
  });

  // Additional tests for comprehensive coverage
  
  describe('updateProfile', () => {
    it('updates profile correctly', async () => {
      const user = userEvent.setup();
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
      const mockProfile = {
        person_id: '123',
        nickname: 'TestUser',
        bio: 'Test bio',
        avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
        preferred_language: 'fr',
      };

      (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
      (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('profile-nickname')).toHaveTextContent('TestUser');
      });

      await act(async () => {
        await user.click(screen.getByText('Update Profile'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('profile-nickname')).toHaveTextContent('UpdatedNick');
      });
    });
  });

  describe('error handling', () => {
    it('handles login errors correctly', async () => {
      const user = userEvent.setup();
      const loginError = new Error('Invalid credentials');
      
      (pillarScanAPI.login as jest.Mock).mockRejectedValue(loginError);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await expect(async () => {
        await act(async () => {
          await user.click(screen.getByText('Login'));
        });
      }).rejects.toThrow('Invalid credentials');

      await waitFor(() => {
        expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false');
        expect(screen.getByTestId('is-loading')).toHaveTextContent('false');
      });
    });

    it('handles profile loading errors (non-404)', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
      
      (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
      (pillarScanAPI.getMyProfile as jest.Mock).mockRejectedValue(new Error('Network error'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false');
        expect(screen.getByTestId('profile-nickname')).toHaveTextContent('no-profile');
      });
    });

    it('handles profile creation errors', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
      
      (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
      (pillarScanAPI.getMyProfile as jest.Mock).mockRejectedValue(
        new Error('Profil PillarScan non trouvé')
      );
      (pillarScanAPI.createProfile as jest.Mock).mockRejectedValue(
        new Error('Failed to create profile')
      );

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(pillarScanAPI.createProfile).toHaveBeenCalled();
        expect(screen.getByTestId('profile-nickname')).toHaveTextContent('no-profile');
      });
    });

    it('handles logout errors gracefully', async () => {
      const user = userEvent.setup();
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
      const mockProfile = {
        person_id: '123',
        nickname: 'TestUser',
        bio: 'Test bio',
        avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
        preferred_language: 'fr',
      };

      (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
      (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);
      (pillarScanAPI.logout as jest.Mock).mockRejectedValue(new Error('Logout failed'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('is-authenticated')).toHaveTextContent('true');
      });

      await act(async () => {
        await user.click(screen.getByText('Logout'));
      });

      // Even if logout fails, should still clear local state
      await waitFor(() => {
        expect(pillarScanAPI.clearToken).toHaveBeenCalled();
        expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false');
        expect(mockRouter.push).toHaveBeenCalledWith('/');
      });
    });
  });

  describe('token validation', () => {
    it('handles invalid token format', async () => {
      const invalidToken = 'invalid-token-format';
      
      (pillarScanAPI.getToken as jest.Mock).mockReturnValue(invalidToken);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(pillarScanAPI.clearToken).toHaveBeenCalled();
        expect(localStorage.getItem('access_token')).toBeNull();
        expect(localStorage.getItem('refresh_token')).toBeNull();
        expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false');
      });
    });

    it('clears tokens on 401 error', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
      
      (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
      (pillarScanAPI.getMyProfile as jest.Mock).mockRejectedValue(new Error('401 Unauthorized'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(pillarScanAPI.clearToken).toHaveBeenCalled();
        expect(localStorage.getItem('access_token')).toBeNull();
        expect(localStorage.getItem('refresh_token')).toBeNull();
      });
    });
  });

  describe('user state management', () => {
    it('sets user data from profile correctly', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
      const mockProfile = {
        person_id: '123',
        nickname: 'TestUser',
        bio: 'Test bio',
        avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
        preferred_language: 'fr',
      };

      (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
      (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('user-id')).toHaveTextContent('123');
        expect(screen.getByTestId('user-person-id')).toHaveTextContent('123');
        expect(screen.getByTestId('user-email')).toHaveTextContent('no-user'); // Email is empty string
      });
    });
  });

  describe('redirect handling', () => {
    it('saves and uses redirect path after login', async () => {
      const user = userEvent.setup();
      sessionStorage.setItem('redirectAfterLogin', '/profile');

      const mockLoginResponse = {
        access: 'new-access-token',
        refresh: 'new-refresh-token',
      };
      const mockProfile = {
        person_id: '123',
        nickname: 'TestUser',
        bio: 'Test bio',
        avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
        preferred_language: 'fr',
      };

      (pillarScanAPI.login as jest.Mock).mockResolvedValue(mockLoginResponse);
      (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        await user.click(screen.getByText('Login'));
      });

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/profile');
        expect(sessionStorage.getItem('redirectAfterLogin')).toBeNull();
      });
    });
  });

  describe('withAuth HOC', () => {
    it('shows loading state while checking auth', () => {
      (pillarScanAPI.getToken as jest.Mock).mockReturnValue('token');
      
      render(
        <AuthProvider>
          <ProtectedWithAuth />
        </AuthProvider>
      );

      expect(screen.getByTestId('is-loading')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('renders protected component when authenticated', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
      const mockProfile = {
        person_id: '123',
        nickname: 'TestUser',
        bio: 'Test bio',
        avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
        preferred_language: 'fr',
      };

      (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
      (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);

      render(
        <AuthProvider>
          <ProtectedWithAuth />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      });
    });

    it('redirects to login when not authenticated', async () => {
      (pillarScanAPI.getToken as jest.Mock).mockReturnValue(null);
      
      // Mock window.location.pathname
      Object.defineProperty(window, 'location', {
        value: { pathname: '/protected-route' },
        writable: true,
      });

      render(
        <AuthProvider>
          <ProtectedWithAuth />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(sessionStorage.getItem('redirectAfterLogin')).toBe('/protected-route');
        expect(mockRouter.push).toHaveBeenCalledWith('/auth/login');
        expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      });
    });
  });

  describe('useRequireProfile hook', () => {
    it('redirects to onboarding when authenticated but no profile', async () => {
      // Create a wrapper that provides both AuthProvider and renders ProfileRequiredComponent
      const TestWrapper = () => (
        <AuthProvider>
          <ProfileRequiredComponent />
        </AuthProvider>
      );

      // Mock authenticated state with no profile
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
      (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
      (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(null);

      // Manually set auth state for this test
      const { rerender } = render(<TestWrapper />);

      // Force authenticated state with no profile
      await act(async () => {
        // Trigger re-render with authenticated but no profile state
        (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
        (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(null);
        rerender(<TestWrapper />);
      });

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/onboarding');
      });
    });

    it('returns profile when available', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************.K_lUwtGbvjCHP3CU33pRVgVYfiPkP7DOsHQLzYLtQqU';
      const mockProfile = {
        person_id: '123',
        nickname: 'TestUser',
        bio: 'Test bio',
        avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
        preferred_language: 'fr',
      };

      (pillarScanAPI.getToken as jest.Mock).mockReturnValue(mockToken);
      (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);

      render(
        <AuthProvider>
          <ProfileRequiredComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('profile-required')).toHaveTextContent('TestUser');
      });
    });
  });

  describe('refresh token scenarios', () => {
    it('handles missing refresh token', async () => {
      const user = userEvent.setup();
      localStorage.removeItem('pillarscan_refresh_token');
      (pillarScanAPI.logout as jest.Mock).mockResolvedValue(undefined);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        await user.click(screen.getByText('Refresh'));
      });

      await waitFor(() => {
        expect(pillarScanAPI.logout).toHaveBeenCalled();
        expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false');
      });
    });
  });

  describe('login without refresh token', () => {
    it('handles login response without refresh token', async () => {
      const user = userEvent.setup();
      const mockLoginResponse = {
        access: 'new-access-token',
        // No refresh token
      };
      const mockProfile = {
        person_id: '123',
        nickname: 'TestUser',
        bio: 'Test bio',
        avatar_style: { color: '#FF6B6B', emoji: '🌟', pattern: 'dots' },
        preferred_language: 'fr',
      };

      (pillarScanAPI.login as jest.Mock).mockResolvedValue(mockLoginResponse);
      (pillarScanAPI.getMyProfile as jest.Mock).mockResolvedValue(mockProfile);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await act(async () => {
        await user.click(screen.getByText('Login'));
      });

      await waitFor(() => {
        expect(localStorage.getItem('pillarscan_refresh_token')).toBeNull();
        expect(screen.getByTestId('is-authenticated')).toHaveTextContent('true');
      });
    });
  });
});