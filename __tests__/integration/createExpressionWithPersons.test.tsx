import { renderHook, act } from '@testing-library/react';
import { useCreateExpression } from '@/hooks/useCreateExpression';
import { pillarScanAPI } from '@/lib/api/client';
import { PersonType } from '@/types/person';
import toast from 'react-hot-toast';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn() }),
}));

jest.mock('@/lib/api/client', () => ({
  pillarScanAPI: {
    createExpression: jest.fn(),
    createExpressionWithMedia: jest.fn(),
  },
}));

jest.mock('react-hot-toast', () => ({
  __esModule: true,
  default: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('@/hooks/useStreak', () => ({
  useStreak: () => ({
    recordExpression: jest.fn(),
  }),
}));

jest.mock('@/hooks/useBadges', () => ({
  useBadges: () => ({
    checkBadges: jest.fn(),
    totalXP: 100,
    level: 1,
    badges: {},
  }),
}));

describe('CreateExpression with PersonReferences', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Spy on console.log to capture debug output
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should properly format person references when submitting', async () => {
    const mockCreateExpression = jest.fn().mockResolvedValue({ 
      expression_id: 'test-123',
      person_references: []
    });
    (pillarScanAPI.createExpression as jest.Mock) = mockCreateExpression;

    const { result } = renderHook(() => useCreateExpression());

    // Préparer les données
    act(() => {
      result.current.setMood('happy');
      result.current.setText('Test avec mentions de personnes');
      result.current.setSuggestedPillar(3);
      
      // Ajouter des références de personnes
      result.current.setPersonReferences([
        {
          person_name: 'Emmanuel Macron',
          person_type: PersonType.HUMAN, // Enum value
          role: 'target' as const,
          temp_id: 'temp-1'
        },
        {
          person_name: 'Smatflow',
          person_type: PersonType.MORAL,
          role: 'source' as const,
          temp_id: 'temp-2'
        }
      ]);
    });

    // Soumettre l'expression
    await act(async () => {
      await result.current.submitExpression();
    });

    // Vérifier que l'API a été appelée
    expect(mockCreateExpression).toHaveBeenCalledTimes(1);

    // Vérifier le format des données envoyées
    const sentData = mockCreateExpression.mock.calls[0][0];
    
    expect(sentData).toMatchObject({
      text: 'Test avec mentions de personnes',
      mood: 'happy',
      suggested_pillar: 3,
      visibility_level: 'public',
      person_references: [
        {
          person_name: 'Emmanuel Macron',
          person_type: 'physical', // String value, not enum
          role: 'target',
          temp_id: 'temp-1'
        },
        {
          person_name: 'Smatflow',
          person_type: 'moral', // String value, not enum
          role: 'source',
          temp_id: 'temp-2'
        }
      ]
    });

    // Vérifier que les types sont bien des strings
    sentData.person_references.forEach((ref: any) => {
      expect(typeof ref.person_type).toBe('string');
      expect(['physical', 'moral', 'group']).toContain(ref.person_type);
    });
  });

  it('should handle API error with person references', async () => {
    const mockError = {
      response: {
        status: 406,
        data: {
          detail: "None <class 'bool'> is not a string"
        }
      }
    };
    
    (pillarScanAPI.createExpression as jest.Mock).mockRejectedValue(mockError);

    const { result } = renderHook(() => useCreateExpression());

    // Préparer les données avec erreur potentielle
    act(() => {
      result.current.setMood('happy');
      result.current.setText('Test erreur');
      result.current.setPersonReferences([{
        person_name: 'Test',
        person_type: 'invalid' as any, // Type invalide
        role: 'target' as const
      }]);
    });

    // Soumettre
    await act(async () => {
      await result.current.submitExpression();
    });

    // Vérifier que l'erreur est gérée
    expect(toast.error).toHaveBeenCalledWith(
      'Impossible de créer l\'expression. Réessayez.'
    );
    expect(console.error).toHaveBeenCalled();
  });

  it('should send empty array when no person references', async () => {
    const mockCreateExpression = jest.fn().mockResolvedValue({ 
      expression_id: 'test-456' 
    });
    (pillarScanAPI.createExpression as jest.Mock) = mockCreateExpression;

    const { result } = renderHook(() => useCreateExpression());

    // Préparer sans personnes
    act(() => {
      result.current.setMood('idea');
      result.current.setText('Expression sans mentions');
      result.current.setSuggestedPillar(7);
      // Pas de setPersonReferences
    });

    await act(async () => {
      await result.current.submitExpression();
    });

    const sentData = mockCreateExpression.mock.calls[0][0];
    expect(sentData.person_references).toEqual([]);
  });

  it('should validate pillar ID is a number', async () => {
    const mockCreateExpression = jest.fn().mockResolvedValue({ 
      expression_id: 'test-789' 
    });
    (pillarScanAPI.createExpression as jest.Mock) = mockCreateExpression;

    const { result } = renderHook(() => useCreateExpression());

    act(() => {
      result.current.setMood('question');
      result.current.setText('Test pillar validation');
      result.current.setSuggestedPillar(5);
    });

    await act(async () => {
      await result.current.submitExpression();
    });

    const sentData = mockCreateExpression.mock.calls[0][0];
    expect(typeof sentData.suggested_pillar).toBe('number');
    expect(sentData.suggested_pillar).toBe(5);
  });
});