import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CreateExpressionForm } from '@/components/expressions/create/CreateExpressionForm';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import '@testing-library/jest-dom';

// Mocks
jest.mock('@/contexts/AuthContext');
jest.mock('next/navigation');
jest.mock('@/lib/api/client');
jest.mock('@/hooks/useStreak', () => ({
  useStreak: () => ({
    recordExpression: jest.fn(),
    currentStreak: 1,
    longestStreak: 1,
    totalExpressions: 5,
  }),
}));
jest.mock('@/hooks/useBadges', () => ({
  useBadges: () => ({
    checkBadges: jest.fn(),
    totalXP: 100,
    level: 2,
    badges: {},
  }),
}));
jest.mock('@/hooks/useGeolocation', () => ({
  useGeolocation: () => ({
    getCurrentPosition: jest.fn(),
    loading: false,
    error: null,
  }),
}));

const mockPush = jest.fn();
const mockAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockRouter = useRouter as jest.MockedFunction<typeof useRouter>;

describe('CreateExpressionForm Navigation Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as ReturnType<typeof useRouter>);

    mockAuth.mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        display_name: 'Test User',
        avatar_url: null,
        country: 'FR',
      },
      profile: {
        nickname: 'Test User',
        bio: null,
        location: null,
        avatar_url: null,
      },
      isAuthenticated: true,
      isLoading: false,
      login: jest.fn(),
      logout: jest.fn(),
      checkAuth: jest.fn(),
    } as ReturnType<typeof useAuth>);
  });

  describe('Step 2 Navigation - Text Input', () => {
    it('should enable Next button only when text length is between 10-280 characters', async () => {
      render(<CreateExpressionForm />);
      
      // Step 1: Select mood
      await userEvent.click(screen.getByRole('button', { name: /heureux/i }));
      await userEvent.click(screen.getByRole('button', { name: /suivant/i }));
      
      // Step 2: Text input
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
      });
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      
      // Initially disabled (empty text)
      expect(nextButton).toBeDisabled();
      
      // Still disabled with short text (< 10 chars)
      await userEvent.type(textarea, 'Short');
      expect(nextButton).toBeDisabled();
      
      // Enabled with valid text (>= 10 chars)
      await userEvent.clear(textarea);
      await userEvent.type(textarea, 'This is a valid expression');
      expect(nextButton).toBeEnabled();
      
      // Still enabled with long text (but < 280 chars)
      await userEvent.clear(textarea);
      await userEvent.type(textarea, 'a'.repeat(200));
      expect(nextButton).toBeEnabled();
      
      // Disabled with text over 280 chars
      await userEvent.clear(textarea);
      await userEvent.type(textarea, 'a'.repeat(281));
      expect(nextButton).toBeDisabled();
    });

    it('should navigate to step 3 when Next is clicked with valid text', async () => {
      render(<CreateExpressionForm />);
      
      // Step 1: Select mood
      await userEvent.click(screen.getByRole('button', { name: /idée/i }));
      await userEvent.click(screen.getByRole('button', { name: /suivant/i }));
      
      // Step 2: Enter valid text
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
      });
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      await userEvent.type(textarea, 'This is my brilliant idea for the community');
      
      // Click Next
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      expect(nextButton).toBeEnabled();
      await userEvent.click(nextButton);
      
      // Should be on step 3
      await waitFor(() => {
        expect(screen.getByText('Contexte et Classification')).toBeInTheDocument();
        expect(screen.getByText('Étape 3 sur 5')).toBeInTheDocument();
      });
    });

    it('should preserve text when navigating back and forth', async () => {
      render(<CreateExpressionForm />);
      
      // Navigate to step 2
      await userEvent.click(screen.getByRole('button', { name: /frustré/i }));
      await userEvent.click(screen.getByRole('button', { name: /suivant/i }));
      
      // Enter text
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
      });
      
      const testText = 'This system needs improvement in many ways';
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      await userEvent.type(textarea, testText);
      
      // Go to step 3
      await userEvent.click(screen.getByRole('button', { name: /suivant/i }));
      
      await waitFor(() => {
        expect(screen.getByText('Étape 3 sur 5')).toBeInTheDocument();
      });
      
      // Go back to step 2
      await userEvent.click(screen.getByRole('button', { name: /précédent/i }));
      
      // Text should be preserved
      await waitFor(() => {
        const textareaAgain = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
        expect(textareaAgain).toHaveValue(testText);
      });
    });

    it('should show dynamic feedback based on text length and mood', async () => {
      render(<CreateExpressionForm />);
      
      // Navigate to step 2 with "question" mood
      await userEvent.click(screen.getByRole('button', { name: /question/i }));
      await userEvent.click(screen.getByRole('button', { name: /suivant/i }));
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
      });
      
      // Initially shows tips
      expect(screen.getByText(/Soyez précis et authentique/)).toBeInTheDocument();
      
      // Type more than 50 characters
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      await userEvent.type(textarea, 'How can we improve the participation system to make it more inclusive for everyone?');
      
      // Should show mood-specific feedback
      await waitFor(() => {
        expect(screen.getByText(/Poser des questions/)).toBeInTheDocument();
      });
    });

    it('should handle rapid text input without losing characters', async () => {
      render(<CreateExpressionForm />);
      
      // Navigate to step 2
      await userEvent.click(screen.getByRole('button', { name: /heureux/i }));
      await userEvent.click(screen.getByRole('button', { name: /suivant/i }));
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
      });
      
      // Type rapidly
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      const rapidText = 'This is typed very quickly to test the input handling!';
      
      // Use paste to simulate rapid input
      await userEvent.click(textarea);
      await userEvent.paste(rapidText);
      
      expect(textarea).toHaveValue(rapidText);
      
      // Next button should be enabled
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      expect(nextButton).toBeEnabled();
    });
  });

  describe('Dark Mode Contrast', () => {
    it('should maintain readable contrast in dark mode', async () => {
      // Add dark class to document for testing
      document.documentElement.classList.add('dark');
      
      render(<CreateExpressionForm />);
      
      // Navigate to step 2
      await userEvent.click(screen.getByRole('button', { name: /heureux/i }));
      await userEvent.click(screen.getByRole('button', { name: /suivant/i }));
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
      });
      
      const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
      
      // Check for dark mode classes
      expect(textarea.className).toContain('dark:bg-gray-800');
      expect(textarea.className).toContain('dark:text-white');
      
      // Character counter should be visible
      const counter = screen.getByText('280');
      expect(counter.className).toContain('dark:text-gray-400');
      
      // Clean up
      document.documentElement.classList.remove('dark');
    });
  });
});