import { renderHook, act } from '@testing-library/react';
import { useCreateExpression } from '@/hooks/useCreateExpression';
import { pillarScanAPI } from '@/lib/api/client';
import toast from 'react-hot-toast';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn() }),
}));

jest.mock('@/lib/api/client', () => ({
  pillarScanAPI: {
    createExpression: jest.fn(),
    createExpressionWithMedia: jest.fn(),
  },
}));

jest.mock('react-hot-toast', () => ({
  __esModule: true,
  default: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('@/hooks/useStreak', () => ({
  useStreak: () => ({
    recordExpression: jest.fn(),
  }),
}));

jest.mock('@/hooks/useBadges', () => ({
  useBadges: () => ({
    checkBadges: jest.fn(),
    totalXP: 100,
    level: 1,
    badges: {},
  }),
}));

describe('CreateExpression with Media', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const createMockFile = (name: string, type: string = 'image/jpeg'): File => {
    return new File(['test image data'], name, { type });
  };

  it('should call createExpressionWithMedia when files are present', async () => {
    const mockExpression = { 
      expression_id: 'test-123',
      media_urls: ['http://example.com/image1.jpg']
    };
    
    const mockCreateWithMedia = jest.fn().mockResolvedValue(mockExpression);
    (pillarScanAPI.createExpressionWithMedia as jest.Mock) = mockCreateWithMedia;

    const { result } = renderHook(() => useCreateExpression());

    // Prepare data with media
    const imageFile = createMockFile('test.jpg');
    
    act(() => {
      result.current.setMood('happy');
      result.current.setText('Test expression with image');
      result.current.setSuggestedPillar(5);
      result.current.setMedia([imageFile]);
    });

    // Submit
    await act(async () => {
      await result.current.submitExpression();
    });

    // Should call createExpressionWithMedia, not createExpression
    expect(mockCreateWithMedia).toHaveBeenCalledTimes(1);
    expect(pillarScanAPI.createExpression).not.toHaveBeenCalled();

    // Check the data passed
    const [expressionData, files] = mockCreateWithMedia.mock.calls[0];
    
    expect(expressionData).toMatchObject({
      text: 'Test expression with image',
      mood: 'happy',
      suggested_pillar: 5,
      visibility_level: 'public'
    });
    
    expect(files).toHaveLength(1);
    expect(files[0]).toBe(imageFile);
  });

  it('should call createExpression when no files are present', async () => {
    const mockExpression = { expression_id: 'test-456' };
    const mockCreate = jest.fn().mockResolvedValue(mockExpression);
    (pillarScanAPI.createExpression as jest.Mock) = mockCreate;

    const { result } = renderHook(() => useCreateExpression());

    // Prepare data without media
    act(() => {
      result.current.setMood('idea');
      result.current.setText('Test expression without image');
      result.current.setSuggestedPillar(7);
      // No setMedia call
    });

    // Submit
    await act(async () => {
      await result.current.submitExpression();
    });

    // Should call createExpression, not createExpressionWithMedia
    expect(mockCreate).toHaveBeenCalledTimes(1);
    expect(pillarScanAPI.createExpressionWithMedia).not.toHaveBeenCalled();
  });

  it('should handle multiple files', async () => {
    const mockExpression = { 
      expression_id: 'test-789',
      media_urls: [
        'http://example.com/image1.jpg',
        'http://example.com/image2.jpg',
        'http://example.com/image3.jpg'
      ]
    };
    
    const mockCreateWithMedia = jest.fn().mockResolvedValue(mockExpression);
    (pillarScanAPI.createExpressionWithMedia as jest.Mock) = mockCreateWithMedia;

    const { result } = renderHook(() => useCreateExpression());

    // Create multiple files
    const files = [
      createMockFile('image1.jpg'),
      createMockFile('image2.png', 'image/png'),
      createMockFile('image3.jpg')
    ];
    
    act(() => {
      result.current.setMood('frustrated');
      result.current.setText('Multiple images test');
      result.current.setMedia(files);
    });

    await act(async () => {
      await result.current.submitExpression();
    });

    const [, submittedFiles] = mockCreateWithMedia.mock.calls[0];
    expect(submittedFiles).toHaveLength(3);
    expect(submittedFiles).toEqual(files);
  });

  it('should handle media upload errors gracefully', async () => {
    const mockError = new Error('Upload failed');
    (pillarScanAPI.createExpressionWithMedia as jest.Mock).mockRejectedValue(mockError);

    const { result } = renderHook(() => useCreateExpression());

    act(() => {
      result.current.setMood('question');
      result.current.setText('Test with upload error');
      result.current.setMedia([createMockFile('error.jpg')]);
    });

    await act(async () => {
      await result.current.submitExpression();
    });

    expect(toast.error).toHaveBeenCalledWith(
      'Impossible de créer l\'expression. Réessayez.'
    );
    expect(result.current.isSubmitting).toBe(false);
  });

  it('should validate media files are actual File objects', async () => {
    const mockCreateWithMedia = jest.fn().mockResolvedValue({ 
      expression_id: 'test-validation' 
    });
    (pillarScanAPI.createExpressionWithMedia as jest.Mock) = mockCreateWithMedia;

    const { result } = renderHook(() => useCreateExpression());

    const validFile = createMockFile('valid.jpg');
    
    act(() => {
      result.current.setMood('happy');
      result.current.setText('Validation test');
      result.current.setMedia([validFile]);
    });

    // Verify media state
    expect(result.current.media).toHaveLength(1);
    expect(result.current.media[0]).toBeInstanceOf(File);
    expect(result.current.media[0].name).toBe('valid.jpg');
    expect(result.current.media[0].type).toBe('image/jpeg');

    await act(async () => {
      await result.current.submitExpression();
    });

    expect(mockCreateWithMedia).toHaveBeenCalled();
  });

  it('should log media information during submission', async () => {
    const mockCreateWithMedia = jest.fn().mockResolvedValue({ 
      expression_id: 'test-logs' 
    });
    (pillarScanAPI.createExpressionWithMedia as jest.Mock) = mockCreateWithMedia;

    const { result } = renderHook(() => useCreateExpression());

    const file = createMockFile('logged.jpg');
    Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB
    
    act(() => {
      result.current.setMood('happy');
      result.current.setText('Log test');
      result.current.setMedia([file]);
    });

    await act(async () => {
      await result.current.submitExpression();
    });

    // Check that media info was logged
    expect(console.log).toHaveBeenCalledWith(
      expect.stringContaining('Données à envoyer:'),
      expect.objectContaining({
        media: expect.arrayContaining([
          expect.objectContaining({
            name: 'logged.jpg',
            size: 1024 * 1024,
            type: 'image/jpeg'
          })
        ])
      })
    );
  });
});