import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { ImageUpload } from '@/components/upload/ImageUpload';
import { AuthProvider } from '@/contexts/AuthContext';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Configuration du serveur MSW pour les tests
const server = setupServer(
  // Mock de l'authentification
  rest.post('http://localhost:8000/api/token/', (req, res, ctx) => {
    return res(
      ctx.json({
        access: 'mock-jwt-token',
        refresh: 'mock-refresh-token'
      })
    );
  }),
  
  // Mock de la création d'expression
  rest.post('http://localhost:8000/api/v2/pillarscan/expressions/', async (req, res, ctx) => {
    const body = await req.json();
    return res(
      ctx.status(201),
      ctx.json({
        id: 'expr-123',
        person_id: 'person-456',
        created_at: new Date().toISOString(),
        mood: body.mood,
        text: body.text,
        visibility_level: body.visibility_level,
        media: [],
        relate_count: 0
      })
    );
  }),
  
  // Mock de l'upload d'image pour une expression
  rest.post('http://localhost:8000/api/v2/pillarscan/expressions/:id/upload_media/', async (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        media_id: 'media-789',
        url: 'https://minio.example.com/pillarscan/expr-123/expression_image.jpg',
        size: 123456,
        content_type: 'image/jpeg',
        filename: 'test.jpg'
      })
    );
  }),
  
  // Mock de l'upload générique
  rest.post('http://localhost:8000/api/v2/media/upload/', async (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        media_id: 'media-generic-123',
        url: 'https://minio.example.com/generic/test.jpg',
        size: 123456,
        content_type: 'image/jpeg',
        media_type: 'image',
        filename: 'test.jpg',
        bucket_name: 'generic-medias',
        file_path: 'test-entity/test.jpg'
      })
    );
  }),
  
  // Mock SSE pour les notifications
  rest.get('http://localhost:8000/api/notifications/api/notifications/stream/', (req, res, ctx) => {
    const stream = new ReadableStream({
      start(controller) {
        // Envoyer un message de connexion
        controller.enqueue('data: {"type": "connection", "message": "Connected to notification stream"}\n\n');
        
        // Envoyer une notification après 1 seconde
        setTimeout(() => {
          controller.enqueue('data: {"type": "notification", "data": {"id": "notif-123", "title": "Expression créée", "message": "Votre expression a été publiée"}}\n\n');
        }, 1000);
      }
    });
    
    return res(
      ctx.status(200),
      ctx.set('Content-Type', 'text/event-stream'),
      ctx.body(stream)
    );
  })
);

// Configuration globale des tests
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: { [key: string]: unknown }) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { fill, alt = '', ...rest } = props;
    // eslint-disable-next-line @next/next/no-img-element
    return <img alt={alt as string} {...rest} />;
  },
}));

// Helper pour wrapper les composants avec les providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });
  
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {children}
      </AuthProvider>
    </QueryClientProvider>
  );
};

describe('Pipeline complet d\'upload d\'images', () => {
  it('teste le flux complet: création expression + upload image', async () => {
    const user = userEvent.setup();
    
    // Mock du composant de formulaire d'expression avec upload
    const ExpressionWithUpload = () => {
      const [expressionId, setExpressionId] = React.useState<string | null>(null);
      const [uploadedMedia, setUploadedMedia] = React.useState<Array<{
        media_id: string;
        url: string;
        filename: string;
      }>>([]);
      
      const handleExpressionCreate = async (data: {
        mood: string;
        text: string;
        visibility_level: string;
      }) => {
        const response = await fetch('http://localhost:8000/api/v2/pillarscan/expressions/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-jwt-token'
          },
          body: JSON.stringify(data)
        });
        
        if (response.ok) {
          const expr = await response.json();
          setExpressionId(expr.id);
          return expr;
        }
        throw new Error('Failed to create expression');
      };
      
      const handleImageUpload = async (file: File) => {
        if (!expressionId) return;
        
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch(
          `http://localhost:8000/api/v2/pillarscan/expressions/${expressionId}/upload_media/`,
          {
            method: 'POST',
            headers: {
              'Authorization': 'Bearer mock-jwt-token'
            },
            body: formData
          }
        );
        
        if (response.ok) {
          const media = await response.json();
          setUploadedMedia(prev => [...prev, media]);
          return media;
        }
        throw new Error('Failed to upload image');
      };
      
      return (
        <div>
          <h1>Créer une expression</h1>
          
          {!expressionId ? (
            <form onSubmit={async (e) => {
              e.preventDefault();
              await handleExpressionCreate({
                mood: 'happy',
                text: 'Test expression avec image',
                visibility_level: 'public'
              });
            }}>
              <button type="submit">Créer l&apos;expression</button>
            </form>
          ) : (
            <div>
              <p>Expression créée: {expressionId}</p>
              <ImageUpload onUpload={handleImageUpload} />
              {uploadedMedia.length > 0 && (
                <div>
                  <h3>Médias uploadés:</h3>
                  <ul>
                    {uploadedMedia.map(media => (
                      <li key={media.media_id}>
                        {media.filename} - {media.url}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      );
    };
    
    render(
      <AllTheProviders>
        <ExpressionWithUpload />
      </AllTheProviders>
    );
    
    // 1. Créer l'expression
    expect(screen.getByText('Créer une expression')).toBeInTheDocument();
    const createButton = screen.getByText('Créer l\'expression');
    
    await user.click(createButton);
    
    // Attendre que l'expression soit créée
    await waitFor(() => {
      expect(screen.getByText(/Expression créée: expr-123/)).toBeInTheDocument();
    });
    
    // 2. Upload une image
    const file = new File(['test image content'], 'test.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByLabelText(/glissez vos images/i).parentElement?.querySelector('input[type="file"]') as HTMLInputElement;
    
    await user.upload(fileInput, file);
    
    // Attendre que l'upload soit terminé
    await waitFor(() => {
      expect(screen.getByText('Médias uploadés:')).toBeInTheDocument();
      expect(screen.getByText(/test.jpg/)).toBeInTheDocument();
    });
  });
  
  it('teste l\'upload générique de média', async () => {
    const user = userEvent.setup();
    
    const GenericUpload = () => {
      const [uploadResult, setUploadResult] = React.useState<{
        media_id: string;
        bucket_name: string;
        file_path: string;
      } | null>(null);
      
      const handleUpload = async (file: File) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('entity_type', 'test');
        formData.append('title', 'Test Upload');
        
        const response = await fetch('http://localhost:8000/api/v2/media/upload/', {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer mock-jwt-token'
          },
          body: formData
        });
        
        if (response.ok) {
          const result = await response.json();
          setUploadResult(result);
          return result;
        }
        throw new Error('Upload failed');
      };
      
      return (
        <div>
          <h2>Upload Générique</h2>
          <ImageUpload onUpload={handleUpload} />
          {uploadResult && (
            <div>
              <p>Upload réussi!</p>
              <p>Media ID: {uploadResult.media_id}</p>
              <p>Bucket: {uploadResult.bucket_name}</p>
              <p>Path: {uploadResult.file_path}</p>
            </div>
          )}
        </div>
      );
    };
    
    render(
      <AllTheProviders>
        <GenericUpload />
      </AllTheProviders>
    );
    
    const file = new File(['generic content'], 'generic.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByLabelText(/glissez vos images/i).parentElement?.querySelector('input[type="file"]') as HTMLInputElement;
    
    await user.upload(fileInput, file);
    
    await waitFor(() => {
      expect(screen.getByText('Upload réussi!')).toBeInTheDocument();
      expect(screen.getByText(/Media ID: media-generic-123/)).toBeInTheDocument();
      expect(screen.getByText(/Bucket: generic-medias/)).toBeInTheDocument();
    });
  });
  
  it('teste la réception de notifications SSE', async () => {
    const NotificationListener = () => {
      const [notifications, setNotifications] = React.useState<Array<{
        id: string;
        title: string;
        message: string;
      }>>([]);
      const [connected, setConnected] = React.useState(false);
      
      React.useEffect(() => {
        const eventSource = new EventSource(
          'http://localhost:8000/api/notifications/api/notifications/stream/?token=mock-jwt-token'
        );
        
        eventSource.onmessage = (event) => {
          const data = JSON.parse(event.data);
          
          if (data.type === 'connection') {
            setConnected(true);
          } else if (data.type === 'notification') {
            setNotifications(prev => [...prev, data.data]);
          }
        };
        
        return () => eventSource.close();
      }, []);
      
      return (
        <div>
          <h2>Notifications SSE</h2>
          <p>Status: {connected ? 'Connecté' : 'Déconnecté'}</p>
          <ul>
            {notifications.map(notif => (
              <li key={notif.id}>
                {notif.title}: {notif.message}
              </li>
            ))}
          </ul>
        </div>
      );
    };
    
    render(
      <AllTheProviders>
        <NotificationListener />
      </AllTheProviders>
    );
    
    // Attendre la connexion
    await waitFor(() => {
      expect(screen.getByText('Status: Connecté')).toBeInTheDocument();
    });
    
    // Attendre la notification
    await waitFor(() => {
      expect(screen.getByText(/Expression créée: Votre expression a été publiée/)).toBeInTheDocument();
    }, { timeout: 2000 });
  });
  
  it('gère les erreurs d\'upload correctement', async () => {
    // Override le handler pour simuler une erreur
    server.use(
      rest.post('http://localhost:8000/api/v2/media/upload/', (req, res, ctx) => {
        return res(
          ctx.status(413),
          ctx.json({
            detail: 'Le fichier est trop volumineux (max 10MB)'
          })
        );
      })
    );
    
    const user = userEvent.setup();
    let uploadError: Error | null = null;
    
    const ErrorHandlingUpload = () => {
      const handleUpload = async (file: File) => {
        try {
          const formData = new FormData();
          formData.append('file', file);
          
          const response = await fetch('http://localhost:8000/api/v2/media/upload/', {
            method: 'POST',
            headers: {
              'Authorization': 'Bearer mock-jwt-token'
            },
            body: formData
          });
          
          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail);
          }
        } catch (error) {
          uploadError = error as Error;
          throw error;
        }
      };
      
      return <ImageUpload onUpload={handleUpload} />;
    };
    
    render(
      <AllTheProviders>
        <ErrorHandlingUpload />
      </AllTheProviders>
    );
    
    const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' });
    const fileInput = screen.getByLabelText(/glissez vos images/i).parentElement?.querySelector('input[type="file"]') as HTMLInputElement;
    
    await user.upload(fileInput, largeFile);
    
    await waitFor(() => {
      expect(uploadError?.message).toContain('trop volumineux');
    });
  });
});