import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ExpressionCard } from '@/components/pillarscan/ExpressionCard';
import { ExpressionFeed } from '@/components/feed/ExpressionFeed';
import { pillarScanAPI } from '@/lib/api/client';
import { PillarScanExpression } from '@/lib/types/pillarscan';

// Mock des dépendances
jest.mock('@/lib/api/client');
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({ isAuthenticated: true })
}));
jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn() })
}));

describe('Media Upload and Display', () => {
  const mockExpression: PillarScanExpression = {
    expression_id: 'test-123',
    user_id: 'user-456',
    user_nickname: 'TestUser',
    user_avatar: 'minimalist_01',
    text: 'Test expression with media',
    mood: 'happy',
    suggested_pillar: 7,
    visibility_level: 'public',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    country_code: 'FR',
    has_media: true,
    // Cas 1: media_urls présent (comportement attendu)
    media_urls: {
      image_0: {
        id: 'media-123',
        url: 'https://minio.example.com/media/test-image.jpg',
        thumbnail_url: 'https://minio.example.com/media/test-image-thumb.jpg'
      }
    },
    media_refs: ['media-123']
  };

  const mockExpressionWithoutUrls: PillarScanExpression = {
    ...mockExpression,
    // Cas 2: seulement media_refs (comportement actuel)
    media_urls: undefined,
    media_refs: ['media-123', 'media-456']
  };

  describe('ExpressionCard', () => {
    it('devrait afficher les images quand media_urls est présent', () => {
      render(<ExpressionCard expression={mockExpression} />);
      
      // Vérifier que l'image est rendue
      const images = screen.getAllByRole('img');
      const mediaImage = images.find(img => 
        img.getAttribute('alt')?.includes('Image')
      );
      
      expect(mediaImage).toBeInTheDocument();
      expect(mediaImage).toHaveAttribute('src', expect.stringContaining('test-image.jpg'));
    });

    it('ne devrait pas afficher d\'images quand media_urls est absent', () => {
      render(<ExpressionCard expression={mockExpressionWithoutUrls} />);
      
      // Vérifier qu'aucune image média n'est rendue
      const images = screen.queryAllByRole('img');
      const mediaImages = images.filter(img => 
        img.getAttribute('alt')?.includes('Image image_')
      );
      
      expect(mediaImages).toHaveLength(0);
    });

    it('devrait gérer le cas où media_urls est un objet vide', () => {
      const expressionEmptyUrls = {
        ...mockExpression,
        media_urls: {}
      };
      
      render(<ExpressionCard expression={expressionEmptyUrls} />);
      
      const images = screen.queryAllByRole('img');
      const mediaImages = images.filter(img => 
        img.getAttribute('alt')?.includes('Image image_')
      );
      
      expect(mediaImages).toHaveLength(0);
    });
  });

  describe('ExpressionFeed avec with_media', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('devrait demander with_media=true lors du chargement', async () => {
      const mockGetExpressions = jest.fn().mockResolvedValue({
        results: [mockExpression],
        count: 1
      });
      
      (pillarScanAPI.getExpressions as jest.Mock) = mockGetExpressions;
      
      render(<ExpressionFeed />);
      
      await waitFor(() => {
        expect(mockGetExpressions).toHaveBeenCalledWith(
          expect.objectContaining({
            with_media: true
          })
        );
      });
    });

    it('devrait afficher les expressions avec médias', async () => {
      (pillarScanAPI.getExpressions as jest.Mock).mockResolvedValue({
        results: [mockExpression],
        count: 1
      });
      
      render(<ExpressionFeed />);
      
      await waitFor(() => {
        expect(screen.getByText('Test expression with media')).toBeInTheDocument();
      });
      
      // Vérifier que l'image est affichée
      const images = screen.getAllByRole('img');
      const mediaImage = images.find(img => 
        img.getAttribute('alt')?.includes('Image')
      );
      
      expect(mediaImage).toBeInTheDocument();
    });
  });

  describe('Upload flow integration', () => {
    it('devrait uploader et récupérer les médias', async () => {
      const mockCreateWithMedia = jest.fn().mockResolvedValue({
        ...mockExpression,
        media_refs: { image_0: 'media-123' }
      });
      
      (pillarScanAPI.createExpressionWithMedia as jest.Mock) = mockCreateWithMedia;
      
      // Simuler l'upload
      const testFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const expressionData = {
        text: 'Test',
        mood: 'happy' as const,
        visibility_level: 'public' as const,
        suggested_pillar: 7
      };
      
      const result = await pillarScanAPI.createExpressionWithMedia(
        expressionData,
        [testFile]
      );
      
      expect(mockCreateWithMedia).toHaveBeenCalledWith(expressionData, [testFile]);
      expect(result.media_refs).toEqual({ image_0: 'media-123' });
    });
  });

  describe('Fallback strategies', () => {
    it('TODO: devrait implémenter un fallback pour afficher media_refs', () => {
      // Ce test documente le comportement souhaité
      // mais n'est pas encore implémenté
      
      // Idée : Si media_urls est absent mais media_refs présent,
      // le composant pourrait :
      // 1. Afficher un placeholder
      // 2. Faire une requête séparée pour obtenir les URLs
      // 3. Construire les URLs à partir du pattern connu
      
      expect(true).toBe(true); // Placeholder
    });
  });
});