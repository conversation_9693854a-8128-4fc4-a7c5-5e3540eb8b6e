import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CreateExpressionForm } from '@/components/expressions/create/CreateExpressionForm';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import '@testing-library/jest-dom';

// Mocks
jest.mock('@/contexts/AuthContext');
jest.mock('next/navigation');
jest.mock('@/lib/api/client');
jest.mock('@/hooks/useStreak', () => ({
  useStreak: () => ({
    recordExpression: jest.fn(),
    currentStreak: 1,
    longestStreak: 1,
    totalExpressions: 5,
  }),
}));
jest.mock('@/hooks/useBadges', () => ({
  useBadges: () => ({
    checkBadges: jest.fn(),
    totalXP: 100,
    level: 2,
    badges: {},
  }),
}));
jest.mock('@/hooks/useGeolocation', () => ({
  useGeolocation: () => ({
    getCurrentPosition: jest.fn(),
    loading: false,
    error: null,
  }),
}));

const mockPush = jest.fn();
const mockAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockRouter = useRouter as jest.MockedFunction<typeof useRouter>;

describe('Step 2 Text Input - Button Functionality Regression Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as ReturnType<typeof useRouter>);

    mockAuth.mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        display_name: 'Test User',
        avatar_url: null,
        country: 'FR',
      },
      profile: {
        nickname: 'Test User',
        bio: null,
        location: null,
        avatar_url: null,
      },
      isAuthenticated: true,
      isLoading: false,
      login: jest.fn(),
      logout: jest.fn(),
      checkAuth: jest.fn(),
    } as ReturnType<typeof useAuth>);
  });

  it('should ensure Next button responds to valid text input', async () => {
    const user = userEvent.setup();
    
    render(<CreateExpressionForm />);
    
    // Step 1: Select mood
    const happyButton = await screen.findByRole('button', { name: /heureux/i });
    await user.click(happyButton);
    
    // Wait for button to be enabled and click
    await waitFor(() => {
      const nextButton = screen.getByRole('button', { name: /suivant/i });
      expect(nextButton).toBeEnabled();
    });
    
    await user.click(screen.getByRole('button', { name: /suivant/i }));
    
    // Step 2: Should be on text input
    await waitFor(() => {
      expect(screen.getByText('Exprimez-vous')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
    });
    
    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    const nextButton = screen.getByRole('button', { name: /suivant/i });
    
    // Button should be disabled initially
    expect(nextButton).toBeDisabled();
    expect(nextButton).toHaveAttribute('disabled');
    
    // Type valid text
    await user.type(textarea, 'This is my expression to test the button functionality');
    
    // Button should become enabled after valid text
    await waitFor(() => {
      expect(nextButton).toBeEnabled();
      expect(nextButton).not.toHaveAttribute('disabled');
    });
    
    // Button should be clickable
    await user.click(nextButton);
    
    // Should navigate to step 3
    await waitFor(() => {
      expect(screen.getByText('Contexte et Classification')).toBeInTheDocument();
      expect(screen.getByText('Étape 3 sur 5')).toBeInTheDocument();
    });
  });

  it('should maintain button state during text editing', async () => {
    const user = userEvent.setup();
    
    render(<CreateExpressionForm />);
    
    // Navigate to step 2
    await user.click(await screen.findByRole('button', { name: /idée/i }));
    await user.click(screen.getByRole('button', { name: /suivant/i }));
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
    });
    
    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    const nextButton = screen.getByRole('button', { name: /suivant/i });
    
    // Type text that's too short
    await user.type(textarea, 'Short');
    expect(nextButton).toBeDisabled();
    
    // Add more text to make it valid
    await user.type(textarea, ' but now it is long enough');
    
    await waitFor(() => {
      expect(nextButton).toBeEnabled();
    });
    
    // Delete text to make it too short again
    await user.clear(textarea);
    await user.type(textarea, 'Too short');
    
    await waitFor(() => {
      expect(nextButton).toBeDisabled();
    });
    
    // Type valid text again
    await user.clear(textarea);
    await user.type(textarea, 'This is definitely long enough to be valid');
    
    await waitFor(() => {
      expect(nextButton).toBeEnabled();
    });
  });

  it('should handle edge cases for text length validation', async () => {
    const user = userEvent.setup();
    
    render(<CreateExpressionForm />);
    
    // Navigate to step 2
    await user.click(await screen.findByRole('button', { name: /frustré/i }));
    await user.click(screen.getByRole('button', { name: /suivant/i }));
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
    });
    
    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    const nextButton = screen.getByRole('button', { name: /suivant/i });
    
    // Test exactly 10 characters (minimum)
    await user.type(textarea, '1234567890');
    await waitFor(() => {
      expect(nextButton).toBeEnabled();
    });
    
    // Test exactly 9 characters (below minimum)
    await user.clear(textarea);
    await user.type(textarea, '123456789');
    await waitFor(() => {
      expect(nextButton).toBeDisabled();
    });
    
    // Test exactly 280 characters (maximum)
    await user.clear(textarea);
    await user.type(textarea, 'a'.repeat(280));
    await waitFor(() => {
      expect(nextButton).toBeEnabled();
    });
    
    // Test exactly 281 characters (above maximum)
    await user.clear(textarea);
    await user.type(textarea, 'a'.repeat(281));
    await waitFor(() => {
      expect(nextButton).toBeDisabled();
    });
  });

  it('should maintain button functionality with whitespace handling', async () => {
    const user = userEvent.setup();
    
    render(<CreateExpressionForm />);
    
    // Navigate to step 2
    await user.click(await screen.findByRole('button', { name: /question/i }));
    await user.click(screen.getByRole('button', { name: /suivant/i }));
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
    });
    
    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    const nextButton = screen.getByRole('button', { name: /suivant/i });
    
    // Test with only spaces (should be disabled after trim)
    await user.type(textarea, '          ');
    expect(nextButton).toBeDisabled();
    
    // Test with text padded with spaces (should check trimmed length)
    await user.clear(textarea);
    await user.type(textarea, '   Valid text with spaces   ');
    await waitFor(() => {
      expect(nextButton).toBeEnabled();
    });
    
    // Test with text that would be too short after trimming
    await user.clear(textarea);
    await user.type(textarea, '   Short   ');
    await waitFor(() => {
      expect(nextButton).toBeDisabled();
    });
  });

  it('should ensure button click handler is properly attached', async () => {
    const user = userEvent.setup();
    
    render(<CreateExpressionForm />);
    
    // Navigate to step 2
    await user.click(await screen.findByRole('button', { name: /heureux/i }));
    await user.click(screen.getByRole('button', { name: /suivant/i }));
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
    });
    
    // Type valid text
    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    await user.type(textarea, 'Testing that the button click handler works correctly');
    
    // Get the button and verify it's enabled
    const nextButton = screen.getByRole('button', { name: /suivant/i });
    await waitFor(() => {
      expect(nextButton).toBeEnabled();
    });
    
    // Create a click event and verify it's handled
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
    });
    
    // Use act to ensure all updates are processed
    await act(async () => {
      nextButton.dispatchEvent(clickEvent);
    });
    
    // Verify navigation occurred
    await waitFor(() => {
      expect(screen.getByText('Étape 3 sur 5')).toBeInTheDocument();
    });
  });

  it('should handle rapid button clicks without issues', async () => {
    const user = userEvent.setup();
    
    render(<CreateExpressionForm />);
    
    // Navigate to step 2
    await user.click(await screen.findByRole('button', { name: /idée/i }));
    await user.click(screen.getByRole('button', { name: /suivant/i }));
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Que voulez-vous exprimer ?')).toBeInTheDocument();
    });
    
    // Type valid text
    const textarea = screen.getByPlaceholderText('Que voulez-vous exprimer ?');
    await user.type(textarea, 'Testing rapid clicks on the next button functionality');
    
    const nextButton = screen.getByRole('button', { name: /suivant/i });
    await waitFor(() => {
      expect(nextButton).toBeEnabled();
    });
    
    // Try multiple rapid clicks
    await user.click(nextButton);
    await user.click(nextButton);
    await user.click(nextButton);
    
    // Should still navigate to step 3 only once
    await waitFor(() => {
      expect(screen.getByText('Étape 3 sur 5')).toBeInTheDocument();
    });
    
    // Verify we're still on step 3 and didn't skip ahead
    expect(screen.queryByText('Étape 4 sur 5')).not.toBeInTheDocument();
  });
});