import { renderHook, waitFor } from '@testing-library/react';
import { act } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useExpressionSearch, useSearchSuggestions, useCountryStatistics } from '@/hooks/useExpressionSearch';
import React from 'react';

// Mock fetch
global.fetch = jest.fn();

// Mock du contexte Country
jest.mock('@/contexts/CountryContext', () => ({
  ...jest.requireActual('@/contexts/CountryContext'),
  useCountry: () => ({ country: 'FR' })
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
    },
  });
  
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
  Wrapper.displayName = 'TestWrapper';
  return Wrapper;
};

describe('useExpressionSearch', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useExpressionSearch(), {
      wrapper: createWrapper(),
    });

    expect(result.current.searchTerm).toBe('');
    expect(result.current.filters).toEqual({});
    expect(result.current.page).toBe(1);
    expect(result.current.sortBy).toBe('relevance');
    expect(result.current.results).toEqual([]);
    expect(result.current.total).toBe(0);
  });

  it('should search expressions with country header', async () => {
    const mockResponse = {
      total: 2,
      country: 'FR',
      page: 1,
      page_size: 20,
      total_pages: 1,
      has_next: false,
      has_previous: false,
      results: [
        {
          id: '1',
          content: 'Test expression 1',
          mood: 'happy',
          country: 'FR',
          created_at: '2024-01-24',
          location: { city: 'Paris' },
          engagement: { relates: 10, views: 50, impact_score: 4.5 },
        },
        {
          id: '2',
          content: 'Test expression 2',
          mood: 'neutral',
          country: 'FR',
          created_at: '2024-01-24',
          location: { city: 'Lyon' },
          engagement: { relates: 5, views: 20, impact_score: 3.0 },
        },
      ],
      aggregations: {
        moods: [
          { key: 'happy', count: 1 },
          { key: 'neutral', count: 1 },
        ],
        pillars: [],
        cities: [
          { key: 'Paris', count: 1 },
          { key: 'Lyon', count: 1 },
        ],
      },
    };

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    });

    const { result } = renderHook(() => useExpressionSearch(), {
      wrapper: createWrapper(),
    });

    // Mettre à jour le terme de recherche
    act(() => {
      result.current.updateSearchTerm('test');
    });

    // Attendre que la recherche se termine
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Vérifier les résultats
    expect(result.current.results).toHaveLength(2);
    expect(result.current.total).toBe(2);
    expect(result.current.aggregations).toBeDefined();
    expect(result.current.aggregations?.moods).toHaveLength(2);

    // Vérifier que l'appel API inclut le header X-Country-Code
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/v2/pillarscan/expressions/search/'),
      expect.objectContaining({
        headers: expect.objectContaining({
          'X-Country-Code': 'FR',
        }),
      })
    );
  });

  it('should handle filters', async () => {
    const { result } = renderHook(() => useExpressionSearch(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.updateFilters({
        mood: 'happy',
        pillar: 'SANTE_PHYSIQUE',
      });
    });

    expect(result.current.filters).toEqual({
      mood: 'happy',
      pillar: 'SANTE_PHYSIQUE',
    });
    expect(result.current.page).toBe(1); // Page reset après changement de filtres
  });

  it('should handle pagination', () => {
    const { result } = renderHook(() => useExpressionSearch(), {
      wrapper: createWrapper(),
    });

    // Simuler des résultats avec pagination
    act(() => {
      // @ts-expect-error - accès direct pour le test
      result.current.results = new Array(20).fill({});
      // @ts-expect-error - accès direct pour le test
      result.current.totalPages = 5;
      // @ts-expect-error - accès direct pour le test
      result.current.hasNextPage = true;
    });

    act(() => {
      result.current.nextPage();
    });

    expect(result.current.page).toBe(2);
  });

  it('should clear search and filters', () => {
    const { result } = renderHook(() => useExpressionSearch({
      initialQuery: 'test',
      initialFilters: { mood: 'happy' },
    }), {
      wrapper: createWrapper(),
    });

    expect(result.current.searchTerm).toBe('test');
    expect(result.current.filters.mood).toBe('happy');

    act(() => {
      result.current.clearSearch();
    });

    expect(result.current.searchTerm).toBe('');
    expect(result.current.filters).toEqual({});
    expect(result.current.page).toBe(1);
  });

  it('should change sort order', () => {
    const { result } = renderHook(() => useExpressionSearch(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.changeSortBy('date');
    });

    expect(result.current.sortBy).toBe('date');
    expect(result.current.page).toBe(1); // Reset à la page 1
  });
});

describe('useSearchSuggestions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should not fetch suggestions for short queries', () => {
    const { result } = renderHook(() => useSearchSuggestions('a'), {
      wrapper: createWrapper(),
    });

    expect(global.fetch).not.toHaveBeenCalled();
    expect(result.current.data).toBeUndefined();
  });

  it('should fetch suggestions for valid queries', async () => {
    const mockSuggestions = {
      suggestions: ['test suggestion 1', 'test suggestion 2'],
    };

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockSuggestions,
    });

    const { result } = renderHook(() => useSearchSuggestions('test'), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.data).toBeDefined();
    });

    expect(result.current.data?.suggestions).toHaveLength(2);
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/expressions/search/suggestions/?q=test'),
      expect.objectContaining({
        headers: expect.objectContaining({
          'X-Country-Code': 'FR',
        }),
      })
    );
  });
});

describe('useCountryStatistics', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch country statistics', async () => {
    const mockStats = {
      country: 'FR',
      total_expressions: 1000,
      unique_users: 250,
      mood_distribution: {
        happy: 400,
        neutral: 300,
        sad: 200,
        angry: 100,
      },
      top_cities: [
        { city: 'Paris', count: 150 },
        { city: 'Lyon', count: 100 },
      ],
      average_impact_score: 3.5,
    };

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockStats,
    });

    const { result } = renderHook(() => useCountryStatistics(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockStats);
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/statistics/'),
      expect.objectContaining({
        headers: expect.objectContaining({
          'X-Country-Code': 'FR',
        }),
      })
    );
  });
});