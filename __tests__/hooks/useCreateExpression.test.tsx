import { renderHook, act } from '@testing-library/react';
import { useCreateExpression } from '@/hooks/useCreateExpression';
import { useRouter } from 'next/navigation';
import { pillarScanAPI } from '@/lib/api/client';
import toast from 'react-hot-toast';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/lib/api/client', () => ({
  pillarScanAPI: {
    createExpression: jest.fn(),
    createExpressionWithMedia: jest.fn(),
  },
}));

jest.mock('react-hot-toast', () => ({
  __esModule: true,
  default: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('@/hooks/useStreak', () => ({
  useStreak: () => ({
    recordExpression: jest.fn(),
  }),
}));

jest.mock('@/hooks/useBadges', () => ({
  useBadges: () => ({
    checkBadges: jest.fn(),
    totalXP: 100,
    level: 1,
    badges: {},
  }),
}));

describe('useCreateExpression - Non-regression tests', () => {
  const mockPush = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });
  });

  describe('canGoNext validation', () => {
    it('should not allow next on step 1 without mood', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      expect(result.current.currentStep).toBe(1);
      expect(result.current.canGoNext()).toBe(false);
    });

    it('should allow next on step 1 with mood selected', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      act(() => {
        result.current.setMood('happy');
      });
      
      expect(result.current.canGoNext()).toBe(true);
    });

    it('should not allow next on step 2 with text too short', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Go to step 2
      act(() => {
        result.current.setMood('happy');
        result.current.goToNextStep();
        result.current.setText('Short'); // Only 5 characters
      });
      
      expect(result.current.currentStep).toBe(2);
      expect(result.current.canGoNext()).toBe(false);
    });

    it('should allow next on step 2 with valid text (10-280 chars)', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Go to step 2
      act(() => {
        result.current.setMood('happy');
        result.current.goToNextStep();
        result.current.setText('This is a valid expression text'); // > 10 chars
      });
      
      expect(result.current.canGoNext()).toBe(true);
    });

    it('should not allow next on step 2 with text too long', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Go to step 2
      act(() => {
        result.current.setMood('happy');
        result.current.goToNextStep();
        result.current.setText('a'.repeat(281)); // 281 chars, over limit
      });
      
      expect(result.current.canGoNext()).toBe(false);
    });

    it('should trim whitespace when validating text length', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Go to step 2
      act(() => {
        result.current.setMood('happy');
        result.current.goToNextStep();
        result.current.setText('   Valid text   '); // Should count as 10 chars after trim
      });
      
      expect(result.current.canGoNext()).toBe(true);
    });
  });

  describe('Step navigation', () => {
    it('should navigate forward through steps correctly', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Step 1 → 2
      act(() => {
        result.current.setMood('happy');
        result.current.goToNextStep();
      });
      expect(result.current.currentStep).toBe(2);
      
      // Step 2 → 3
      act(() => {
        result.current.setText('Valid expression text');
        result.current.goToNextStep();
      });
      expect(result.current.currentStep).toBe(3);
      
      // Step 3 → 4
      act(() => {
        result.current.goToNextStep();
      });
      expect(result.current.currentStep).toBe(4);
      
      // Step 4 → 5
      act(() => {
        result.current.goToNextStep();
      });
      expect(result.current.currentStep).toBe(5);
      
      // Should not go past step 5
      act(() => {
        result.current.goToNextStep();
      });
      expect(result.current.currentStep).toBe(5);
    });

    it('should navigate backward through steps correctly', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Go to step 3
      act(() => {
        result.current.goToStep(3);
      });
      
      // Go back to step 2
      act(() => {
        result.current.goToPreviousStep();
      });
      expect(result.current.currentStep).toBe(2);
      
      // Go back to step 1
      act(() => {
        result.current.goToPreviousStep();
      });
      expect(result.current.currentStep).toBe(1);
      
      // Should not go before step 1
      act(() => {
        result.current.goToPreviousStep();
      });
      expect(result.current.currentStep).toBe(1);
    });
  });

  describe('Form submission', () => {
    it('should submit expression with correct data', async () => {
      const mockCreateExpression = jest.fn().mockResolvedValue({ id: 1 });
      (pillarScanAPI.createExpression as jest.Mock) = mockCreateExpression;
      
      const { result } = renderHook(() => useCreateExpression());
      
      // Fill form data
      act(() => {
        result.current.setMood('happy');
        result.current.setText('This is my test expression');
        result.current.setSuggestedPillar(3);
        result.current.setVisibility('public');
      });
      
      // Submit
      await act(async () => {
        await result.current.submitExpression();
      });
      
      // Check API was called with correct data
      expect(mockCreateExpression).toHaveBeenCalledWith({
        text: 'This is my test expression',
        mood: 'happy',
        suggested_pillar: 3,
        visibility_level: 'public',
        person_references: [],
        location: undefined,
      });
      
      // Check success state
      expect(result.current.showSuccess).toBe(true);
    });

    it('should show error toast on submission failure', async () => {
      const mockError = new Error('API Error');
      (pillarScanAPI.createExpression as jest.Mock).mockRejectedValue(mockError);
      
      const { result } = renderHook(() => useCreateExpression());
      
      // Fill minimum required data
      act(() => {
        result.current.setMood('happy');
        result.current.setText('This is my test expression');
      });
      
      // Submit
      await act(async () => {
        await result.current.submitExpression();
      });
      
      // Check error toast was called
      expect(toast.error).toHaveBeenCalledWith('Impossible de créer l\'expression. Réessayez.');
      expect(result.current.isSubmitting).toBe(false);
      expect(result.current.showSuccess).toBe(false);
    });
  });

  describe('Reset functionality', () => {
    it('should reset all form data to initial state', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Fill form with data
      act(() => {
        result.current.setMood('frustrated');
        result.current.setText('Test expression');
        result.current.setSuggestedPillar(5);
        result.current.setVisibility('private');
        result.current.goToStep(4);
      });
      
      // Reset
      act(() => {
        result.current.resetForm();
      });
      
      // Check all values are reset
      expect(result.current.currentStep).toBe(1);
      expect(result.current.mood).toBeNull();
      expect(result.current.text).toBe('');
      expect(result.current.suggestedPillar).toBe(7); // Default "Autre"
      expect(result.current.visibility).toBe('public');
      expect(result.current.personReferences).toEqual([]);
      expect(result.current.media).toEqual([]);
      expect(result.current.location).toBeNull();
      expect(result.current.locationName).toBe('');
      expect(result.current.isSubmitting).toBe(false);
      expect(result.current.showSuccess).toBe(false);
    });
  });
});