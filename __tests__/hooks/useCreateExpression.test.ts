import { renderHook, act } from '@testing-library/react';
import { useCreateExpression } from '@/hooks/useCreateExpression';
import { pillarScanAPI } from '@/lib/api/client';
import { useRouter } from 'next/navigation';
import { PersonType } from '@/types/person';
import toast from 'react-hot-toast';

// Mocks
jest.mock('@/lib/api/client');
jest.mock('next/navigation');
jest.mock('react-hot-toast');
jest.mock('@/hooks/useStreak', () => ({
  useStreak: () => ({
    recordExpression: jest.fn(),
    currentStreak: 1,
    longestStreak: 1,
    totalExpressions: 5,
  }),
}));
jest.mock('@/hooks/useBadges', () => ({
  useBadges: () => ({
    checkBadges: jest.fn(),
    totalXP: 100,
    level: 2,
    badges: {},
  }),
}));

const mockPush = jest.fn();
const mockRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockAPI = pillarScanAPI as jest.Mocked<typeof pillarScanAPI>;
const mockToast = toast as jest.Mocked<typeof toast>;

describe('useCreateExpression', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as ReturnType<typeof useRouter>);

    mockAPI.createExpression = jest.fn().mockResolvedValue({
      expression_id: '123',
      expression_text: 'Test expression',
      mood_color: 'happy',
    });
    mockAPI.createExpressionWithMedia = jest.fn().mockResolvedValue({
      expression_id: '123',
      expression_text: 'Test expression',
      mood_color: 'happy',
    });
  });

  describe('Step Management', () => {
    it('should start at step 1', () => {
      const { result } = renderHook(() => useCreateExpression());
      expect(result.current.currentStep).toBe(1);
    });

    it('should navigate to next step when allowed', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Set mood to enable next
      act(() => {
        result.current.setMood('happy');
      });
      
      expect(result.current.canGoNext()).toBe(true);
      
      act(() => {
        result.current.goToNextStep();
      });
      
      expect(result.current.currentStep).toBe(2);
    });

    it('should not navigate forward when requirements not met', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // No mood selected
      expect(result.current.canGoNext()).toBe(false);
      
      act(() => {
        result.current.goToNextStep();
      });
      
      // Should stay on step 1
      expect(result.current.currentStep).toBe(1);
    });

    it('should navigate to previous step', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Manually go to step 2 (since auto-advance is disabled in tests)
      act(() => {
        result.current.setMood('happy');
      });
      
      act(() => {
        result.current.goToNextStep();
      });
      
      expect(result.current.currentStep).toBe(2);
      
      // Go back
      act(() => {
        result.current.goToPreviousStep();
      });
      
      expect(result.current.currentStep).toBe(1);
    });

    it('should not go before step 1', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      act(() => {
        result.current.goToPreviousStep();
      });
      
      expect(result.current.currentStep).toBe(1);
    });
  });

  describe('Form Validation', () => {
    it('should validate step 1 - mood required', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      expect(result.current.canGoNext()).toBe(false);
      
      act(() => {
        result.current.setMood('frustrated');
      });
      
      expect(result.current.canGoNext()).toBe(true);
    });

    it('should validate step 2 - text length', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Navigate to step 2
      act(() => {
        result.current.setMood('happy');
      });
      
      act(() => {
        result.current.goToNextStep();
      });
      
      // Verify we're on step 2
      expect(result.current.currentStep).toBe(2);
      
      // Initially, without text, should not be able to go next
      expect(result.current.canGoNext()).toBe(false);
      
      // Too short
      act(() => {
        result.current.setText('Short');
      });
      expect(result.current.canGoNext()).toBe(false);
      
      // Valid
      act(() => {
        result.current.setText('This is a valid expression text');
      });
      expect(result.current.canGoNext()).toBe(true);
      
      // Too long
      act(() => {
        result.current.setText('x'.repeat(281));
      });
      expect(result.current.canGoNext()).toBe(false);
    });

    it('should validate step 3 - pillar required', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Navigate to step 3
      act(() => {
        result.current.setMood('idea');
      });
      
      act(() => {
        result.current.goToNextStep();
      });
      
      act(() => {
        result.current.setText('Valid expression text');
      });
      
      act(() => {
        result.current.goToNextStep();
      });
      
      // Verify we're on step 3
      expect(result.current.currentStep).toBe(3);
      
      // Default pillar should be set
      expect(result.current.suggestedPillar).toBe(7); // "Autre" pillar
      expect(result.current.canGoNext()).toBe(true);
      
      // Setting pillar to 0 should disable next
      act(() => {
        result.current.setSuggestedPillar(0);
      });
      expect(result.current.canGoNext()).toBe(false);
      
      // Setting a valid pillar should enable next
      act(() => {
        result.current.setSuggestedPillar(3); // Work pillar
      });
      expect(result.current.canGoNext()).toBe(true);
    });
  });

  describe('Form Submission', () => {
    it('should submit expression with all data', async () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Fill form
      act(() => {
        result.current.setMood('happy');
        result.current.setText('This is my test expression');
        result.current.setSuggestedPillar(2); // Finance pillar
        result.current.setVisibility('public');
        result.current.setPersonReferences([
          {
            person_type: PersonType.MORAL,
            person_name: 'Test Company',
            role: 'target',
          },
        ]);
      });
      
      // Submit
      await act(async () => {
        await result.current.submitExpression();
      });
      
      expect(mockAPI.createExpression).toHaveBeenCalledWith({
        expression_text: 'This is my test expression',
        mood_color: 'happy',
        suggested_pillar: 2, // Finance pillar
        visibility_level: 'public',
        person_references: [
          {
            person_type: PersonType.MORAL,
            person_name: 'Test Company',
            role: 'target',
          },
        ],
        location: undefined,
        location_name: undefined,
      });
      
      expect(result.current.showSuccess).toBe(true);
      
      // Wait for redirect
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 2100));
      });
      
      expect(mockPush).toHaveBeenCalledWith('/');
    });

    it('should upload media after expression creation', async () => {
      const { result } = renderHook(() => useCreateExpression());
      
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      
      // Fill form with media
      act(() => {
        result.current.setMood('happy');
        result.current.setText('Expression with image');
        result.current.setMedia([mockFile]);
      });
      
      // Submit
      await act(async () => {
        await result.current.submitExpression();
      });
      
      expect(mockAPI.createExpressionWithMedia).toHaveBeenCalledWith(
        expect.objectContaining({
          expression_text: 'Expression with image',
          mood_color: 'happy',
        }),
        [mockFile]
      );
    });

    it('should handle submission errors', async () => {
      mockAPI.createExpression = jest.fn().mockRejectedValueOnce(new Error('Network error'));
      
      const { result } = renderHook(() => useCreateExpression());
      
      // Fill minimum required
      act(() => {
        result.current.setMood('frustrated');
        result.current.setText('This should fail');
      });
      
      // Submit
      await act(async () => {
        await result.current.submitExpression();
      });
      
      expect(mockToast.error).toHaveBeenCalledWith('Impossible de créer l\'expression. Réessayez.');
      expect(result.current.isSubmitting).toBe(false);
      expect(result.current.showSuccess).toBe(false);
    });
  });

  describe('Form Reset', () => {
    it('should reset all form data', () => {
      const { result } = renderHook(() => useCreateExpression());
      
      // Fill form
      act(() => {
        result.current.setMood('idea');
        result.current.setText('Some text');
        result.current.goToNextStep();
        result.current.goToNextStep();
      });
      
      // Reset
      act(() => {
        result.current.resetForm();
      });
      
      expect(result.current.currentStep).toBe(1);
      expect(result.current.mood).toBe(null);
      expect(result.current.text).toBe('');
      expect(result.current.personReferences).toEqual([]);
      expect(result.current.suggestedPillar).toBe(7); // "Autre"
      expect(result.current.media).toEqual([]);
      expect(result.current.visibility).toBe('public');
    });
  });
});