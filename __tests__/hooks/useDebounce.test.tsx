import { renderHook, act, waitFor } from '@testing-library/react';
import { useDebounce, useDebouncedCallback, useDebouncedSearch, useDebouncedInput } from '@/hooks/useDebounce';

// Mock timers
jest.useFakeTimers();

describe('useDebounce', () => {
  afterEach(() => {
    jest.clearAllTimers();
  });

  it('returns initial value immediately', () => {
    const { result } = renderHook(() => useDebounce('initial', 500));
    expect(result.current).toBe('initial');
  });

  it('debounces value changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 500 },
      }
    );

    expect(result.current).toBe('initial');

    // Change value
    rerender({ value: 'updated', delay: 500 });
    
    // Value should not change immediately
    expect(result.current).toBe('initial');

    // Fast-forward time
    act(() => {
      jest.advanceTimersByTime(500);
    });

    // Now value should be updated
    expect(result.current).toBe('updated');
  });

  it('cancels previous timeout on rapid changes', () => {
    const { result, rerender } = renderHook(
      ({ value }) => useDebounce(value, 500),
      {
        initialProps: { value: 'initial' },
      }
    );

    // Make multiple rapid changes
    rerender({ value: 'change1' });
    act(() => {
      jest.advanceTimersByTime(200);
    });
    
    rerender({ value: 'change2' });
    act(() => {
      jest.advanceTimersByTime(200);
    });
    
    rerender({ value: 'final' });
    
    // Value should still be initial
    expect(result.current).toBe('initial');

    // Complete the debounce delay
    act(() => {
      jest.advanceTimersByTime(500);
    });

    // Should have the final value, not intermediate ones
    expect(result.current).toBe('final');
  });

  it('handles different delay values', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 1000 },
      }
    );

    rerender({ value: 'updated', delay: 1000 });

    // Advance less than delay
    act(() => {
      jest.advanceTimersByTime(500);
    });
    expect(result.current).toBe('initial');

    // Complete the delay
    act(() => {
      jest.advanceTimersByTime(500);
    });
    expect(result.current).toBe('updated');
  });
});

describe('useDebouncedCallback', () => {
  afterEach(() => {
    jest.clearAllTimers();
  });

  it('debounces callback execution', () => {
    const callback = jest.fn();
    const { result } = renderHook(() => useDebouncedCallback(callback, 500));

    // Call the debounced function multiple times
    act(() => {
      result.current('arg1');
      result.current('arg2');
      result.current('arg3');
    });

    // Callback should not be called yet
    expect(callback).not.toHaveBeenCalled();

    // Fast-forward time
    act(() => {
      jest.advanceTimersByTime(500);
    });

    // Callback should be called once with last arguments
    expect(callback).toHaveBeenCalledTimes(1);
    expect(callback).toHaveBeenCalledWith('arg3');
  });

  it('cancels previous timeouts', () => {
    const callback = jest.fn();
    const { result } = renderHook(() => useDebouncedCallback(callback, 500));

    act(() => {
      result.current('first');
    });

    act(() => {
      jest.advanceTimersByTime(300);
    });

    act(() => {
      result.current('second');
    });

    act(() => {
      jest.advanceTimersByTime(300);
    });

    // Callback should not be called yet
    expect(callback).not.toHaveBeenCalled();

    act(() => {
      jest.advanceTimersByTime(200);
    });

    // Only the second call should execute
    expect(callback).toHaveBeenCalledTimes(1);
    expect(callback).toHaveBeenCalledWith('second');
  });

  it('handles callback updates', () => {
    const callback1 = jest.fn();
    const callback2 = jest.fn();
    
    const { result, rerender } = renderHook(
      ({ cb }) => useDebouncedCallback(cb, 500),
      {
        initialProps: { cb: callback1 },
      }
    );

    act(() => {
      result.current('test');
    });

    // Update callback before timeout
    rerender({ cb: callback2 });

    act(() => {
      jest.advanceTimersByTime(500);
    });

    // New callback should be called
    expect(callback1).not.toHaveBeenCalled();
    expect(callback2).toHaveBeenCalledWith('test');
  });

  it('cleans up on unmount', () => {
    const callback = jest.fn();
    const { result, unmount } = renderHook(() => useDebouncedCallback(callback, 500));

    act(() => {
      result.current('test');
    });

    // Unmount before timeout
    unmount();

    act(() => {
      jest.advanceTimersByTime(500);
    });

    // Callback should not be called
    expect(callback).not.toHaveBeenCalled();
  });
});

describe('useDebouncedSearch', () => {
  afterEach(() => {
    jest.clearAllTimers();
  });

  it('initializes with correct default state', () => {
    const searchFn = jest.fn();
    const { result } = renderHook(() => useDebouncedSearch(searchFn));

    expect(result.current.query).toBe('');
    expect(result.current.results).toBeNull();
    expect(result.current.isSearching).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('performs search after debounce delay', async () => {
    const searchResults = { items: ['result1', 'result2'] };
    const searchFn = jest.fn().mockResolvedValue(searchResults);
    
    const { result } = renderHook(() => useDebouncedSearch(searchFn, 300));

    // Set query
    act(() => {
      result.current.setQuery('test query');
    });

    // Search should not start immediately
    expect(searchFn).not.toHaveBeenCalled();
    expect(result.current.isSearching).toBe(false);

    // Fast-forward debounce delay
    await act(async () => {
      jest.advanceTimersByTime(300);
    });

    // Search should be triggered
    expect(searchFn).toHaveBeenCalledWith('test query');
    
    // Wait for search to complete
    await waitFor(() => {
      expect(result.current.results).toEqual(searchResults);
      expect(result.current.isSearching).toBe(false);
    });
  });

  it('cancels search on rapid query changes', async () => {
    const searchFn = jest.fn().mockImplementation((query) => 
      new Promise(resolve => setTimeout(() => resolve({ query }), 100))
    );
    
    const { result } = renderHook(() => useDebouncedSearch(searchFn, 300));

    // Make rapid query changes
    act(() => {
      result.current.setQuery('q1');
    });
    
    act(() => {
      jest.advanceTimersByTime(100);
    });
    
    act(() => {
      result.current.setQuery('q2');
    });
    
    act(() => {
      jest.advanceTimersByTime(100);
    });
    
    act(() => {
      result.current.setQuery('final');
    });

    // Fast-forward to trigger search
    await act(async () => {
      jest.advanceTimersByTime(300);
    });

    // Only the final query should be searched
    expect(searchFn).toHaveBeenCalledTimes(1);
    expect(searchFn).toHaveBeenCalledWith('final');
  });

  it('handles search errors', async () => {
    const error = new Error('Search failed');
    const searchFn = jest.fn().mockRejectedValue(error);
    
    const { result } = renderHook(() => useDebouncedSearch(searchFn));

    act(() => {
      result.current.setQuery('error query');
    });

    await act(async () => {
      jest.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(result.current.error).toEqual(error);
      expect(result.current.isSearching).toBe(false);
      expect(result.current.results).toBeNull();
    });
  });

  it('clears results when query is empty', async () => {
    const searchFn = jest.fn().mockResolvedValue({ items: ['result'] });
    const { result } = renderHook(() => useDebouncedSearch(searchFn));

    // Set query and get results
    act(() => {
      result.current.setQuery('test');
    });

    await act(async () => {
      jest.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(result.current.results).toBeTruthy();
    });

    // Clear query
    act(() => {
      result.current.setQuery('');
    });

    await act(async () => {
      jest.advanceTimersByTime(300);
    });

    expect(result.current.results).toBeNull();
    expect(searchFn).toHaveBeenCalledTimes(1); // Not called for empty query
  });

  it('provides clearResults function', () => {
    const searchFn = jest.fn();
    const { result } = renderHook(() => useDebouncedSearch(searchFn));

    // Set some mock results
    act(() => {
      result.current.setQuery('test');
    });

    // Manually clear results
    act(() => {
      result.current.clearResults();
    });

    expect(result.current.results).toBeNull();
  });
});

describe('useDebouncedInput', () => {
  afterEach(() => {
    jest.clearAllTimers();
  });

  it('initializes with provided value', () => {
    const onChange = jest.fn();
    const { result } = renderHook(() => useDebouncedInput('initial', onChange));

    expect(result.current.value).toBe('initial');
    expect(result.current.debouncedValue).toBe('initial');
  });

  it('updates value immediately but debounces callback', () => {
    const onChange = jest.fn();
    const { result } = renderHook(() => useDebouncedInput('', onChange, 500));

    // Simulate input change
    act(() => {
      result.current.onChange({
        target: { value: 'new value' }
      } as React.ChangeEvent<HTMLInputElement>);
    });

    // Value updates immediately
    expect(result.current.value).toBe('new value');
    
    // Callback not called yet
    expect(onChange).not.toHaveBeenCalled();

    // Fast-forward
    act(() => {
      jest.advanceTimersByTime(500);
    });

    // Callback called with debounced value
    expect(onChange).toHaveBeenCalledWith('new value');
    expect(result.current.debouncedValue).toBe('new value');
  });

  it('supports setValue function', () => {
    const onChange = jest.fn();
    const { result } = renderHook(() => useDebouncedInput('', onChange, 300));

    act(() => {
      result.current.setValue('programmatic value');
    });

    expect(result.current.value).toBe('programmatic value');

    act(() => {
      jest.advanceTimersByTime(300);
    });

    expect(onChange).toHaveBeenCalledWith('programmatic value');
  });

  it('does not call onChange for initial value', () => {
    const onChange = jest.fn();
    const { result } = renderHook(() => useDebouncedInput('initial', onChange, 300));

    act(() => {
      jest.advanceTimersByTime(300);
    });

    // onChange should not be called when value equals initialValue
    expect(onChange).not.toHaveBeenCalled();
    expect(result.current.debouncedValue).toBe('initial');
  });

  it('handles textarea change events', () => {
    const onChange = jest.fn();
    const { result } = renderHook(() => useDebouncedInput('', onChange, 300));

    act(() => {
      result.current.onChange({
        target: { value: 'textarea content' }
      } as React.ChangeEvent<HTMLTextAreaElement>);
    });

    expect(result.current.value).toBe('textarea content');

    act(() => {
      jest.advanceTimersByTime(300);
    });

    expect(onChange).toHaveBeenCalledWith('textarea content');
  });
});