import React from 'react';
import { waitFor } from '@testing-library/react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, useTheme, ThemeMode } from '../ThemeContext';

// Helper component for testing hook
function TestComponent() {
  const { themeMode, setThemeMode, toggleTheme } = useTheme();
  
  // Labels are not part of theme object, they're defined separately in components
  const themeLabels: Record<ThemeMode, string> = {
    light: 'Clair',
    dark: 'Sombre',
    ocean: 'Océan',
    sunset: 'Coucher',
    aurora: 'Aurore',
  };
  
  return (
    <div>
      <div data-testid="theme-mode">{themeMode}</div>
      <div data-testid="theme-label">{themeLabels[themeMode]}</div>
      <button onClick={() => setThemeMode('dark')}>Set Dark</button>
      <button onClick={() => setThemeMode('ocean')}>Set Ocean</button>
      <button onClick={() => setThemeMode('sunset')}>Set Sunset</button>
      <button onClick={() => setThemeMode('aurora')}>Set Aurora</button>
      <button onClick={toggleTheme}>Toggle Theme</button>
    </div>
  );
}

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    clear: jest.fn(() => {
      store = {};
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
  };
})();

Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

describe('ThemeContext', () => {
  beforeEach(() => {
    mockLocalStorage.clear();
    jest.clearAllMocks();
  });

  describe('ThemeProvider', () => {
    it('should render children correctly', () => {
      render(
        <ThemeProvider>
          <div>Test Child</div>
        </ThemeProvider>
      );
      
      expect(screen.getByText('Test Child')).toBeInTheDocument();
    });

    it('should apply dark class for dark and aurora themes', async () => {
      const { container } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      // Wait for mounting
      await waitFor(() => {
        expect(screen.getByTestId('theme-mode')).toHaveTextContent('light');
      });

      // Test dark theme
      fireEvent.click(screen.getByText('Set Dark'));
      await waitFor(() => {
        const wrapper = container.firstChild as HTMLElement;
        expect(wrapper).toHaveClass('dark');
      });

      // Test aurora theme
      fireEvent.click(screen.getByText('Set Aurora'));
      await waitFor(() => {
        const wrapper = container.firstChild as HTMLElement;
        expect(wrapper).toHaveClass('dark');
      });
    });

    it('should not apply dark class for light, ocean, and sunset themes', async () => {
      const { container } = render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      // Test light (default)
      await waitFor(() => {
        const wrapper = container.firstChild as HTMLElement;
        expect(wrapper).not.toHaveClass('dark');
      });

      // Test ocean
      fireEvent.click(screen.getByText('Set Ocean'));
      await waitFor(() => {
        const wrapper = container.firstChild as HTMLElement;
        expect(wrapper).not.toHaveClass('dark');
      });

      // Test sunset
      fireEvent.click(screen.getByText('Set Sunset'));
      await waitFor(() => {
        const wrapper = container.firstChild as HTMLElement;
        expect(wrapper).not.toHaveClass('dark');
      });
    });
  });

  describe('useTheme hook', () => {
    it('should initialize with light theme by default', async () => {
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );
      
      await waitFor(() => {
        expect(screen.getByTestId('theme-mode')).toHaveTextContent('light');
        expect(screen.getByTestId('theme-label')).toHaveTextContent('Clair');
      });
    });

    it('should initialize with theme from localStorage', async () => {
      mockLocalStorage.setItem('theme-mode', JSON.stringify('dark'));
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );
      
      // Wait for the component to read from localStorage and update
      await waitFor(() => {
        expect(screen.getByTestId('theme-mode')).toHaveTextContent('dark');
        expect(screen.getByTestId('theme-label')).toHaveTextContent('Sombre');
      });
    });

    it('should throw error when used outside ThemeProvider', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => {
        render(<TestComponent />);
      }).toThrow('useTheme must be used within a ThemeProvider');
      
      consoleSpy.mockRestore();
    });
  });

  describe('Theme switching', () => {
    it('should switch between all theme modes correctly', async () => {
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );
      
      const themes: ThemeMode[] = ['dark', 'ocean', 'sunset', 'aurora'];
      
      for (const theme of themes) {
        const button = screen.getByText(`Set ${theme.charAt(0).toUpperCase() + theme.slice(1)}`);
        fireEvent.click(button);
        
        await waitFor(() => {
          expect(screen.getByTestId('theme-mode')).toHaveTextContent(theme);
        });
      }
    });

    it('should toggle through themes in correct order', async () => {
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );
      
      const expectedOrder: ThemeMode[] = ['light', 'dark', 'ocean', 'sunset', 'aurora', 'light'];
      const toggleButton = screen.getByText('Toggle Theme');
      
      for (let i = 0; i < expectedOrder.length - 1; i++) {
        if (i > 0) {
          fireEvent.click(toggleButton);
        }
        
        await waitFor(() => {
          expect(screen.getByTestId('theme-mode')).toHaveTextContent(expectedOrder[i]);
        });
      }
    });
  });

  describe('localStorage integration', () => {
    it('should persist theme changes to localStorage', async () => {
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );
      
      fireEvent.click(screen.getByText('Set Dark'));
      
      await waitFor(() => {
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          'theme-mode',
          JSON.stringify('dark')
        );
      });
    });

    it('should handle localStorage errors gracefully', () => {
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
      
      mockLocalStorage.setItem.mockImplementationOnce(() => {
        throw new Error('Storage full');
      });
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );
      
      // Should not throw
      fireEvent.click(screen.getByText('Set Dark'));
      
      expect(screen.getByTestId('theme-mode')).toHaveTextContent('dark');
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error setting localStorage key'),
        expect.any(Error)
      );
      
      consoleWarnSpy.mockRestore();
    });
  });
});