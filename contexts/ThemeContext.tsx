'use client';

import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { useLocalStorage } from '@/hooks/useLocalStorage';

export type ThemeMode = 'light' | 'dark' | 'ocean' | 'sunset' | 'aurora';

interface ThemeConfig {
  mode: ThemeMode;
  background: {
    primary: string;
    secondary: string;
    accent: string;
    pattern?: string;
  };
  text: {
    primary: string;
    secondary: string;
    muted: string;
  };
  ui: {
    card: string;
    cardHover: string;
    border: string;
    shadow: string;
  };
  animation: {
    blob1: string;
    blob2: string;
    blob3: string;
  };
}

const themes: Record<ThemeMode, ThemeConfig> = {
  light: {
    mode: 'light',
    background: {
      primary: 'from-blue-50 via-white to-purple-50',
      secondary: 'from-gray-50 to-white',
      accent: 'from-blue-100 to-purple-100',
    },
    text: {
      primary: 'text-gray-900',
      secondary: 'text-gray-700',
      muted: 'text-gray-800',
    },
    ui: {
      card: 'bg-white',
      cardHover: 'hover:bg-gray-50',
      border: 'border-gray-200',
      shadow: 'shadow-lg',
    },
    animation: {
      blob1: 'bg-purple-300',
      blob2: 'bg-yellow-300',
      blob3: 'bg-pink-300',
    },
  },
  dark: {
    mode: 'dark',
    background: {
      primary: 'from-slate-900 via-blue-900 to-slate-900',
      secondary: 'from-slate-800 to-slate-900',
      accent: 'from-blue-800 to-purple-800',
    },
    text: {
      primary: 'text-gray-100',
      secondary: 'text-gray-300',
      muted: 'text-gray-400',
    },
    ui: {
      card: 'bg-slate-800',
      cardHover: 'hover:bg-slate-700',
      border: 'border-slate-700',
      shadow: 'shadow-2xl shadow-blue-900/20',
    },
    animation: {
      blob1: 'bg-blue-600',
      blob2: 'bg-purple-600',
      blob3: 'bg-indigo-600',
    },
  },
  ocean: {
    mode: 'ocean',
    background: {
      primary: 'from-cyan-50 via-blue-100 to-teal-50',
      secondary: 'from-blue-50 to-cyan-50',
      accent: 'from-cyan-200 to-blue-200',
      pattern: 'bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))]',
    },
    text: {
      primary: 'text-blue-900',
      secondary: 'text-blue-700',
      muted: 'text-blue-500',
    },
    ui: {
      card: 'bg-white/90 backdrop-blur-sm',
      cardHover: 'hover:bg-blue-50/90',
      border: 'border-blue-200',
      shadow: 'shadow-xl shadow-blue-200/50',
    },
    animation: {
      blob1: 'bg-cyan-400',
      blob2: 'bg-blue-400',
      blob3: 'bg-teal-400',
    },
  },
  sunset: {
    mode: 'sunset',
    background: {
      primary: 'from-orange-100 via-pink-100 to-purple-100',
      secondary: 'from-orange-50 to-pink-50',
      accent: 'from-orange-200 to-pink-200',
    },
    text: {
      primary: 'text-gray-900',
      secondary: 'text-gray-700',
      muted: 'text-gray-800',
    },
    ui: {
      card: 'bg-white/95',
      cardHover: 'hover:bg-orange-50/95',
      border: 'border-orange-200',
      shadow: 'shadow-xl shadow-orange-200/50',
    },
    animation: {
      blob1: 'bg-orange-400',
      blob2: 'bg-pink-400',
      blob3: 'bg-purple-400',
    },
  },
  aurora: {
    mode: 'aurora',
    background: {
      primary: 'from-purple-900 via-green-800 to-blue-900',
      secondary: 'from-purple-800 to-blue-800',
      accent: 'from-green-600 to-blue-600',
    },
    text: {
      primary: 'text-gray-100',
      secondary: 'text-gray-300',
      muted: 'text-gray-400',
    },
    ui: {
      card: 'bg-slate-900 backdrop-blur-md',
      cardHover: 'hover:bg-slate-800',
      border: 'border-green-700/50',
      shadow: 'shadow-2xl shadow-green-900/30',
    },
    animation: {
      blob1: 'bg-green-500',
      blob2: 'bg-purple-500',
      blob3: 'bg-blue-500',
    },
  },
};

interface ThemeContextType {
  theme: ThemeConfig;
  themeMode: ThemeMode;
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [themeMode, setThemeMode] = useLocalStorage<ThemeMode>('theme-mode', 'light');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Utilise toujours 'light' côté serveur pour éviter l'hydratation mismatch
  const effectiveThemeMode = mounted ? themeMode : 'light';
  const theme = themes[effectiveThemeMode];

  const toggleTheme = React.useCallback(() => {
    const modes: ThemeMode[] = ['light', 'dark', 'ocean', 'sunset', 'aurora'];
    const currentIndex = modes.indexOf(themeMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setThemeMode(modes[nextIndex]);
  }, [themeMode, setThemeMode]);

  // Fournit une valeur par défaut pendant le montage
  const contextValue = useMemo(
    () => ({ theme, themeMode: effectiveThemeMode, setThemeMode, toggleTheme }),
    [theme, effectiveThemeMode, setThemeMode, toggleTheme]
  );

  return (
    <ThemeContext.Provider value={contextValue}>
      <div className={theme.mode === 'dark' || theme.mode === 'aurora' ? 'dark' : ''}>
        {children}
      </div>
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    // Return default theme during SSR
    if (typeof window === 'undefined') {
      return {
        theme: themes.light,
        themeMode: 'light' as ThemeMode,
        setThemeMode: () => {},
        toggleTheme: () => {},
      };
    }
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}