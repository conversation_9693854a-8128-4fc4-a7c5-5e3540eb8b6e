/**
 * Contexte d'authentification pour PillarScan
 * Gère l'état de connexion et les informations utilisateur
 */

'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { pillarScanAPI } from '@/lib/api/client';
import { PillarScanProfile } from '@/lib/types/pillarscan';

interface AuthContextType {
  // État
  isAuthenticated: boolean;
  isLoading: boolean;
  user: AuthUser | null;
  profile: PillarScanProfile | null;
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  updateProfile: (profile: PillarScanProfile) => void;
}

interface AuthUser {
  email: string;
  id: string;
  person_id?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<AuthUser | null>(null);
  const [profile, setProfile] = useState<PillarScanProfile | null>(null);
  const router = useRouter();

  // Vérifie le token au chargement
  useEffect(() => {
    checkAuth();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const checkAuth = async () => {
    try {
      const token = pillarScanAPI.getToken();
      if (!token) {
        setIsLoading(false);
        return;
      }

      // Vérifie d'abord si le token semble valide
      try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          const payload = JSON.parse(atob(tokenParts[1]));
          const now = Date.now() / 1000;
          
          // Si le token est expiré, on ne continue pas
          if (payload.exp && payload.exp < now) {
            throw new Error('Token expiré');
          }
        }
      } catch {
        // Token invalide, on nettoie et on arrête
        pillarScanAPI.clearToken();
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        setIsLoading(false);
        return;
      }

      // IMPORTANT: Récupérer d'abord le profil auth pour avoir le pays
      try {
        const authProfile = await pillarScanAPI.getAuthProfile();
        console.log('Pays utilisateur (checkAuth):', authProfile.country);
        
        // Sauvegarder aussi dans localStorage pour les composants
        if (authProfile.country) {
          localStorage.setItem('selectedCountry', authProfile.country);
        }
      } catch (profileError) {
        console.error('Erreur récupération profil auth:', profileError);
      }
      
      // Essaie de récupérer le profil PillarScan seulement si le token semble valide
      await loadProfile();
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Erreur vérification auth:', error);
      
      // Si c'est une erreur 401 ou token invalide, on nettoie silencieusement
      if (error instanceof Error && (error.message.includes('401') || error.message.includes('jeton'))) {
        pillarScanAPI.clearToken();
        pillarScanAPI.setCountryCode(''); // Nettoyer le pays
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('selectedCountry');
      }
      
      setIsAuthenticated(false);
      setProfile(null);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const loadProfile = async () => {
    try {
      const profileData = await pillarScanAPI.getMyProfile();
      setProfile(profileData);
      
      // Extrait les infos utilisateur du profil
      if (profileData.person_id) {
        setUser({
          id: profileData.person_id,
          person_id: profileData.person_id,
          email: '', // À récupérer depuis l'API user si nécessaire
        });
      }
    } catch (error) {
      console.error('Erreur chargement profil:', error);
      
      // Si le profil n'existe pas (404), on le crée automatiquement
      if (error instanceof Error && error.message.includes('Profil PillarScan non trouvé')) {
        try {
          console.log('Création automatique du profil PillarScan...');
          
          // Génère un avatar aléatoire
          const avatarStyle = {
            color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#DDA0DD', '#F7DC6F'][Math.floor(Math.random() * 6)],
            emoji: ['🌟', '🌊', '🌺', '🌈', '🦋', '🌸', '🎨', '🌿'][Math.floor(Math.random() * 8)],
            pattern: ['dots', 'lines', 'waves'][Math.floor(Math.random() * 3)],
          };
          
          // Crée le profil avec des données par défaut
          const newProfile = await pillarScanAPI.createProfile({
            nickname: `User${Math.floor(Math.random() * 10000)}`,
            avatar_style: avatarStyle,
            bio: '',
            preferred_language: 'fr',
          });
          
          setProfile(newProfile);
          
          if (newProfile.person_id) {
            setUser({
              id: newProfile.person_id,
              person_id: newProfile.person_id,
              email: '',
            });
          }
          
          // Marque qu'on doit rediriger vers l'onboarding
          if (typeof window !== 'undefined') {
            sessionStorage.setItem('needsOnboarding', 'true');
          }
        } catch (createError) {
          console.error('Erreur création profil:', createError);
        }
      }
    }
  };

  const login = useCallback(async (email: string, password: string) => {
    try {
      setIsLoading(true);
      
      // Connexion via l'API
      const response = await pillarScanAPI.login(email, password);
      
      // Sauvegarde du refresh token si nécessaire
      if (response.refresh && typeof window !== 'undefined') {
        localStorage.setItem('pillarscan_refresh_token', response.refresh);
      }
      
      // IMPORTANT: Récupérer le profil auth pour avoir le pays
      try {
        const authProfile = await pillarScanAPI.getAuthProfile();
        // Le pays est maintenant défini dans le client API
        console.log('Pays utilisateur:', authProfile.country);
        
        // Sauvegarder aussi dans localStorage pour les composants
        if (authProfile.country) {
          localStorage.setItem('selectedCountry', authProfile.country);
        }
      } catch (profileError) {
        console.error('Erreur récupération profil auth:', profileError);
      }
      
      // Charge le profil PillarScan
      await loadProfile();
      
      setIsAuthenticated(true);
      
      // Vérifie si on doit aller à l'onboarding
      const needsOnboarding = sessionStorage.getItem('needsOnboarding') === 'true';
      sessionStorage.removeItem('needsOnboarding');
      
      if (needsOnboarding) {
        router.push('/onboarding');
      } else {
        // Redirige vers la page appropriée
        const redirectTo = sessionStorage.getItem('redirectAfterLogin') || '/';
        sessionStorage.removeItem('redirectAfterLogin');
        router.push(redirectTo);
      }
      
    } catch (error) {
      console.error('Erreur connexion:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await pillarScanAPI.logout();
    } catch (error) {
      console.error('Erreur déconnexion:', error);
    } finally {
      pillarScanAPI.clearToken();
      pillarScanAPI.setCountryCode(''); // Nettoyer le pays
      if (typeof window !== 'undefined') {
        localStorage.removeItem('pillarscan_refresh_token');
        localStorage.removeItem('selectedCountry'); // Nettoyer aussi le localStorage
      }
      setIsAuthenticated(false);
      setUser(null);
      setProfile(null);
      setIsLoading(false);
      router.push('/');
    }
  }, [router]);

  const refreshAuth = useCallback(async () => {
    try {
      const refreshToken = typeof window !== 'undefined' 
        ? localStorage.getItem('pillarscan_refresh_token') 
        : null;
        
      if (!refreshToken) {
        throw new Error('Pas de refresh token');
      }
      
      await pillarScanAPI.refreshToken(refreshToken);
      await loadProfile();
      setIsAuthenticated(true);
      
    } catch (error) {
      console.error('Erreur refresh token:', error);
      await logout();
    }
  }, [logout]);

  const updateProfile = useCallback((newProfile: PillarScanProfile) => {
    setProfile(newProfile);
  }, []);

  const value: AuthContextType = {
    isAuthenticated,
    isLoading,
    user,
    profile,
    login,
    logout,
    refreshAuth,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook personnalisé pour utiliser le contexte
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth doit être utilisé dans un AuthProvider');
  }
  return context;
}

// HOC pour protéger les routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading && !isAuthenticated) {
        // Sauvegarde la page actuelle pour redirection après login
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('redirectAfterLogin', window.location.pathname);
        }
        router.push('/auth/login');
      }
    }, [isAuthenticated, isLoading, router]);

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (!isAuthenticated) {
      return null;
    }

    return <Component {...props} />;
  };
}

// Hook pour exiger un profil complet
export function useRequireProfile() {
  const { profile, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated && !profile) {
      router.push('/onboarding');
    }
  }, [isAuthenticated, profile, router]);

  return profile;
}