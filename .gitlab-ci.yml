default:
  tags:
    - shared-runner

stages:
  - build-images

.deploy:
  image: python:3.10
  rules:
    - if: $CI_COMMIT_TAG || ($CI_COMMIT_BRANCH == "main" && $CI_COMMIT_MESSAGE =~ /^Merge branch.*/)
    - if: $CI_PIPELINE_SOURCE == "web"
    # - if: $CI_PIPELINE_SOURCE == "web"  &&  $CI_COMMIT_BRANCH == "main"

  cache:
    key: pip-cache
    paths:
      - .cache/pip
  before_script:
    - bash cicd_vars.sh

    - pip install --upgrade pip

    - pip install ansible

    - export ANSIBLE_HOST_KEY_CHECKING=False

    - eval $(ssh-agent -s)

    - ssh-add <(echo "$ANSIBLE_DEPLOY_KEY")

  script:
    - ansible-playbook -u "$ANSIBLE_SERVER_USER" -i "$ANSIBLE_SERVER_HOST," .deploy/ansible/playbook.yml --extra-vars "repo_url=$CI_REPOSITORY_URL"

# deploy-dev:
#   extends: .deploy
#   stage: deploy-dev
#   variables:
#     PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
#   environment:
#     name: development
#   when: always

build-images:
  image: docker:28
  stage: build-images
  services:
    - docker:28-dind
  variables:
    # Enable BuildKit for better caching and performance
    DOCKER_BUILDKIT: 1
  rules:
    - if: $CI_COMMIT_TAG
      when: always

  before_script:
    - apk update && apk add make bash --no-cache
    - echo "user:$CI_REGISTRY_USER -- pwd:$CI_REGISTRY_PASSWORD -- registry:$CI_REGISTRY"
    - echo "$CI_REGISTRY_PASSWORD" | docker login --username $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    - echo "$CI_REGISTRY_IMAGE"
    - echo "$CI_COMMIT_TAG"
    - echo "$CI_COMMIT_TAG_MESSAGE"
  script:
    - echo "Build Images, and create release"
    - bash .deploy/docker/build_and_push.sh
