## Instructions pour Claude Code

### Instruction Complète à Copier-Coller

```
Configure un système de tests automatiques complet pour détecter les régressions dans mon projet Next.js avec les requirements suivants :

1. **Installation des dépendances de test**
   - Jest et React Testing Library pour les tests unitaires
   - Jest-html-reporter pour générer des rapports HTML visuels
   - Coverage avec jest pour la couverture de code
   - Husky et lint-staged pour les pre-commit hooks
   - Cypress pour les tests E2E (optionnel mais recommandé)

2. **Configuration des fichiers**
   - jest.config.js avec coverage activé
   - jest.setup.js pour la configuration globale
   - Créer un dossier __tests__ à la racine
   - Ajouter des tests exemples pour mes composants principaux

3. **Scripts NPM à ajouter**
   - "test": pour lancer les tests en mode watch
   - "test:ci": pour les tests avec coverage
   - "test:report": pour générer un rapport HTML visuel
   - "test:watch": pour le mode développement avec auto-refresh

4. **Rapports visuels**
   - Configure jest-html-reporter pour générer des rapports dans /test-reports/
   - Configure coverage pour générer un rapport dans /coverage/
   - Créer une page Next.js simple à /test-dashboard qui affiche les résultats

5. **Automatisation**
   - Configure husky pour lancer les tests avant chaque commit
   - Ajoute un fichier .github/workflows/tests.yml pour GitHub Actions
   - Configure le tout pour que je n'aie qu'à coder et regarder les résultats

6. **Interface de visualisation**
   - Crée un composant TestDashboard qui lit les résultats JSON et les affiche
   - Accessible via localhost:3000/test-dashboard
   - Affichage en temps réel des tests qui passent/échouent

Assure-toi que tout soit configuré pour que je puisse simplement lancer "npm run test:watch" et voir les résultats s'actualiser automatiquement.
```

## Où Trouver les Logs et Rapports

### 1. **Dans le Terminal** (Temps réel)
```bash
# Affichage direct lors de l'exécution
 PASS  src/components/Button.test.tsx (2.341 s)
 FAIL  src/pages/api/users.test.ts
   ● Users API › should return user list
```

### 2. **Fichiers Générés**
```
votre-projet/
├── coverage/
│   ├── lcov-report/
│   │   └── index.html      # 📊 Rapport visuel de couverture
│   └── coverage-final.json # Données brutes
├── test-reports/
│   ├── test-report.html    # 📋 Rapport HTML des tests
│   └── test-results.json   # Résultats en JSON
└── .test-logs/
    └── latest.log          # Logs détaillés
```

### 3. **Interfaces Visuelles**

#### A. **Rapport de Couverture** (coverage/lcov-report/index.html)
- Interface web interactive
- Montre ligne par ligne ce qui est testé
- Pourcentages par fichier/dossier
- Code coloré (vert = testé, rouge = non testé)

#### B. **Rapport de Tests** (test-reports/test-report.html)
```html
<!-- Aperçu du rapport HTML -->
<!DOCTYPE html>
<html>
  <head><title>Test Results</title></head>
  <body>
    <h1>✅ 48/50 Tests Réussis</h1>
    <div class="failures">
      <h2>❌ Échecs:</h2>
      <div class="test-failure">
        <h3>Button Component</h3>
        <pre>Expected: "primary"
Received: "secondary"</pre>
      </div>
    </div>
    <div class="coverage">
      <h2>📊 Couverture: 85%</h2>
      <progress value="85" max="100"></progress>
    </div>
  </body>
</html>
```

#### C. **Dashboard Live** (localhost:3000/test-dashboard)
Claude Code peut créer une page qui :
- Lit les résultats en temps réel
- Affiche des graphiques
- Historique des tests
- Notifications des échecs

### 4. **Exemples de Visualisation**

```typescript
// Exemple de ce que Claude Code peut créer
// pages/test-dashboard.tsx
import { useEffect, useState } from 'react'
import testResults from '../test-reports/test-results.json'

export default function TestDashboard() {
  const [results, setResults] = useState(null)
  
  // Auto-refresh toutes les 5 secondes
  useEffect(() => {
    const interval = setInterval(() => {
      fetch('/api/test-results')
        .then(res => res.json())
        .then(setResults)
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold">
        Tests: {results?.passed}/{results?.total} ✅
      </h1>
      {/* Graphiques, détails, etc. */}
    </div>
  )
}
```

## Workflow Final

1. **Vous codez** → Les tests tournent automatiquement
2. **Terminal** → Feedback immédiat
3. **Navigateur** → Ouvrez `/test-dashboard` pour vue d'ensemble
4. **Rapports HTML** → Pour analyse détaillée

Tout est automatique après la configuration initiale ! 🎉