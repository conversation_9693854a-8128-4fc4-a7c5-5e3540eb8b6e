/**
 * Service Worker pour PillarScan PWA
 * Gère le cache, l'offline et la synchronisation en arrière-plan
 */

const CACHE_NAME = 'pillarscan-v1';
const DYNAMIC_CACHE = 'pillarscan-dynamic-v1';
const DATA_CACHE = 'pillarscan-data-v1';

// Ressources essentielles à mettre en cache
const STATIC_ASSETS = [
  '/',
  '/offline',
  '/manifest.json',
  '/favicon.ico',
  '/logo-smatflow-identity.svg',
  // Les assets Next.js seront ajoutés dynamiquement
];

// Patterns d'URLs à ne jamais mettre en cache
const NO_CACHE_PATTERNS = [
  /\/api\/auth/,
  /\/api\/notifications\/stream/,
  /_next\/webpack/,
  /\.hot-update\./
];

// Patterns d'URLs pour le cache data (API calls)
const DATA_CACHE_PATTERNS = [
  /\/api\/pillarscan\/expressions/,
  /\/api\/pillarscan\/pillars/,
  /\/api\/pillarscan\/profile/
];

// Installation du Service Worker
self.addEventListener('install', (event) => {
  console.log('[SW] Installation...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('[SW] Mise en cache des ressources statiques');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => self.skipWaiting())
  );
});

// Activation et nettoyage des anciens caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activation...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames
            .filter(cacheName => {
              return cacheName.startsWith('pillarscan-') && 
                     cacheName !== CACHE_NAME &&
                     cacheName !== DYNAMIC_CACHE &&
                     cacheName !== DATA_CACHE;
            })
            .map(cacheName => {
              console.log('[SW] Suppression du cache:', cacheName);
              return caches.delete(cacheName);
            })
        );
      })
      .then(() => self.clients.claim())
  );
});

// Stratégies de cache
const cacheStrategies = {
  // Cache First - Pour les assets statiques
  cacheFirst: async (request) => {
    const cached = await caches.match(request);
    if (cached) {
      console.log('[SW] Depuis le cache:', request.url);
      return cached;
    }
    
    try {
      const response = await fetch(request);
      if (response.ok) {
        const cache = await caches.open(DYNAMIC_CACHE);
        cache.put(request, response.clone());
      }
      return response;
    } catch (error) {
      console.error('[SW] Erreur réseau:', error);
      return caches.match('/offline');
    }
  },

  // Network First - Pour les données API
  networkFirst: async (request) => {
    try {
      const response = await fetch(request);
      if (response.ok) {
        const cache = await caches.open(DATA_CACHE);
        cache.put(request, response.clone());
      }
      return response;
    } catch (error) {
      console.log('[SW] Fallback cache pour:', request.url);
      const cached = await caches.match(request);
      if (cached) return cached;
      
      // Retourner une réponse offline pour les API
      return new Response(
        JSON.stringify({ 
          error: 'Offline', 
          message: 'Contenu disponible hors ligne limité' 
        }),
        { 
          status: 503,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  },

  // Network Only - Pour auth et notifications
  networkOnly: async (request) => {
    try {
      return await fetch(request);
    } catch (error) {
      return new Response(
        JSON.stringify({ error: 'Network required' }),
        { 
          status: 503,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  }
};

// Interception des requêtes
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Ignorer les requêtes non-HTTP(S)
  if (!url.protocol.startsWith('http')) return;

  // Vérifier les patterns no-cache
  if (NO_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    event.respondWith(cacheStrategies.networkOnly(request));
    return;
  }

  // API calls - Network First
  if (DATA_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    event.respondWith(cacheStrategies.networkFirst(request));
    return;
  }

  // Assets statiques et pages - Cache First
  if (request.destination === 'document' || 
      request.destination === 'script' ||
      request.destination === 'style' ||
      request.destination === 'image' ||
      request.destination === 'font') {
    event.respondWith(cacheStrategies.cacheFirst(request));
    return;
  }

  // Par défaut - Network First
  event.respondWith(cacheStrategies.networkFirst(request));
});

// Synchronisation en arrière-plan
self.addEventListener('sync', (event) => {
  console.log('[SW] Synchronisation:', event.tag);
  
  if (event.tag === 'sync-expressions') {
    event.waitUntil(syncExpressions());
  }
});

// Synchroniser les expressions créées hors ligne
async function syncExpressions() {
  try {
    // Ouvrir IndexedDB
    const db = await openDB();
    const tx = db.transaction('pending_expressions', 'readonly');
    const store = tx.objectStore('pending_expressions');
    const pendingExpressions = await store.getAll();

    for (const expression of pendingExpressions) {
      try {
        // Envoyer l'expression au serveur
        const response = await fetch('/api/pillarscan/expressions/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${expression.token}`
          },
          body: JSON.stringify(expression.data)
        });

        if (response.ok) {
          // Supprimer de la file d'attente
          const deleteTx = db.transaction('pending_expressions', 'readwrite');
          await deleteTx.objectStore('pending_expressions').delete(expression.id);
          
          // Notifier le client
          const clients = await self.clients.matchAll();
          clients.forEach(client => {
            client.postMessage({
              type: 'SYNC_SUCCESS',
              expressionId: expression.id
            });
          });
        }
      } catch (error) {
        console.error('[SW] Erreur sync expression:', error);
      }
    }
  } catch (error) {
    console.error('[SW] Erreur sync:', error);
  }
}

// Helper pour ouvrir IndexedDB
function openDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('PillarScanDB', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      
      // Store pour les expressions en attente
      if (!db.objectStoreNames.contains('pending_expressions')) {
        db.createObjectStore('pending_expressions', { keyPath: 'id' });
      }
      
      // Store pour le cache des expressions
      if (!db.objectStoreNames.contains('cached_expressions')) {
        const store = db.createObjectStore('cached_expressions', { keyPath: 'id' });
        store.createIndex('pillar_id', 'pillar_id');
        store.createIndex('created_at', 'created_at');
      }
    };
  });
}

// Gestion des messages du client
self.addEventListener('message', (event) => {
  console.log('[SW] Message reçu:', event.data);
  
  if (event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data.type === 'CACHE_EXPRESSIONS') {
    cacheExpressions(event.data.expressions);
  }
});

// Mettre en cache des expressions pour l'offline
async function cacheExpressions(expressions) {
  try {
    const db = await openDB();
    const tx = db.transaction('cached_expressions', 'readwrite');
    const store = tx.objectStore('cached_expressions');
    
    for (const expression of expressions) {
      await store.put(expression);
    }
    
    console.log('[SW] Expressions mises en cache:', expressions.length);
  } catch (error) {
    console.error('[SW] Erreur cache expressions:', error);
  }
}

// Notifications push
self.addEventListener('push', (event) => {
  if (!event.data) return;
  
  const data = event.data.json();
  console.log('[SW] Push notification:', data);
  
  const options = {
    body: data.message,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      url: data.url || '/',
      ...data
    },
    actions: [
      {
        action: 'view',
        title: 'Voir',
        icon: '/icons/action-view.png'
      },
      {
        action: 'close',
        title: 'Fermer',
        icon: '/icons/action-close.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification(data.title || 'PillarScan', options)
  );
});

// Gestion des clics sur les notifications
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  if (event.action === 'close') return;
  
  const url = event.notification.data.url || '/';
  
  event.waitUntil(
    clients.openWindow(url)
  );
});

console.log('[SW] Service Worker chargé');