import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { Notification } from '@/lib/services/NotificationService';

interface NotificationStore {
  // État
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  addNotification: (notification: Notification) => void;
  setNotifications: (notifications: Notification[]) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  dismiss: (id: string) => void;
  clearAll: () => void;
  setConnectionStatus: (isConnected: boolean) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Sélecteurs
  getUnreadNotifications: () => Notification[];
  getNotificationsByType: (type: string) => Notification[];
}

export const useNotificationStore = create<NotificationStore>()(
  devtools(
    persist(
      (set, get) => ({
        // État initial
        notifications: [],
        unreadCount: 0,
        isConnected: false,
        isLoading: false,
        error: null,

        // Actions
        addNotification: (notification) => {
          set((state) => {
            // Éviter les doublons
            const notifId = notification.notification_id || notification.id;
            if (state.notifications.some(n => (n.notification_id || n.id) === notifId)) {
              return state;
            }

            const notifications = [notification, ...state.notifications];
            const unreadCount = notifications.filter(n => !n.is_read && !n.is_dismissed).length;

            return { 
              notifications,
              unreadCount,
              error: null
            };
          });
        },

        setNotifications: (notifications) => {
          set({
            notifications,
            unreadCount: notifications.filter(n => !n.is_read && !n.is_dismissed).length,
            error: null
          });
        },

        markAsRead: (id) => {
          set((state) => {
            const notifications = state.notifications.map(n =>
              ((n.notification_id || n.id) === id) ? { ...n, is_read: true, read_at: new Date().toISOString() } : n
            );
            const unreadCount = notifications.filter(n => !n.is_read && !n.is_dismissed).length;

            return { notifications, unreadCount };
          });
        },

        markAllAsRead: () => {
          set((state) => {
            const now = new Date().toISOString();
            const notifications = state.notifications.map(n =>
              !n.is_read ? { ...n, is_read: true, read_at: now } : n
            );

            return { 
              notifications, 
              unreadCount: 0 
            };
          });
        },

        dismiss: (id) => {
          set((state) => {
            const notifications = state.notifications.map(n =>
              ((n.notification_id || n.id) === id) ? { ...n, is_dismissed: true, dismissed_at: new Date().toISOString() } : n
            );
            const unreadCount = notifications.filter(n => !n.is_read && !n.is_dismissed).length;

            return { notifications, unreadCount };
          });
        },

        clearAll: () => {
          set({
            notifications: [],
            unreadCount: 0,
            error: null
          });
        },

        setConnectionStatus: (isConnected) => {
          set({ isConnected });
        },

        setLoading: (isLoading) => {
          set({ isLoading });
        },

        setError: (error) => {
          set({ error });
        },

        // Sélecteurs
        getUnreadNotifications: () => {
          const state = get();
          return state.notifications.filter(n => !n.is_read && !n.is_dismissed);
        },

        getNotificationsByType: (type) => {
          const state = get();
          return state.notifications.filter(n => n.type === type && !n.is_dismissed);
        }
      }),
      {
        name: 'notification-storage',
        // Ne persister que les notifications non lues
        partialize: (state) => ({
          notifications: state.notifications.filter(n => !n.is_read || !n.is_dismissed).slice(0, 50),
          unreadCount: state.unreadCount
        })
      }
    ),
    {
      name: 'notification-store'
    }
  )
);