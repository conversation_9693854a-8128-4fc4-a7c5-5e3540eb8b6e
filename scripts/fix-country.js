#!/usr/bin/env node

/**
 * Script pour corriger le problème de pays CF (République Centrafricaine)
 * et forcer la sélection d'un pays supporté
 */

console.log('🔧 Correction du pays sélectionné...\n');

// Pour nettoyer le localStorage depuis Node.js, on crée une page HTML temporaire
const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Fix Country Code</title>
</head>
<body>
  <h1>Correction du code pays</h1>
  <div id="status"></div>
  <script>
    const status = document.getElementById('status');
    
    // Récupérer le pays actuel
    const currentCountry = localStorage.getItem('selectedCountry');
    status.innerHTML += '<p>Pays actuel: ' + (currentCountry || 'non défini') + '</p>';
    
    // Si c'est CF ou un pays non supporté, le supprimer
    const supportedCountries = ['FR', 'US', 'CA', 'GB', 'DE', 'ES', 'IT', 'BR', 'JP', 'CN', 'IN', 'MX', 'AU', 'ZA', 'NG', 'EG', 'MA', 'TN', 'SN', 'CI', 'CM', 'KE', 'GH', 'ET', 'UG', 'TZ', 'DZ', 'SD', 'MG', 'AO'];
    
    if (currentCountry && !supportedCountries.includes(currentCountry)) {
      localStorage.removeItem('selectedCountry');
      status.innerHTML += '<p style="color: green;">✅ Pays non supporté supprimé</p>';
      status.innerHTML += '<p>Veuillez recharger la page pour sélectionner un nouveau pays</p>';
    } else if (!currentCountry) {
      status.innerHTML += '<p style="color: orange;">⚠️ Aucun pays défini</p>';
    } else {
      status.innerHTML += '<p style="color: green;">✅ Pays supporté</p>';
    }
    
    // Afficher les pays supportés
    status.innerHTML += '<h2>Pays supportés:</h2>';
    status.innerHTML += '<ul>' + supportedCountries.map(code => '<li>' + code + '</li>').join('') + '</ul>';
    
    setTimeout(() => {
      window.close();
    }, 5000);
  </script>
</body>
</html>
`;

// Créer un fichier HTML temporaire
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

const tempFile = path.join(__dirname, 'temp-fix-country.html');
fs.writeFileSync(tempFile, html);

console.log('📂 Ouverture du navigateur pour corriger le localStorage...');
console.log('La fenêtre se fermera automatiquement dans 5 secondes.\n');

// Ouvrir dans le navigateur par défaut
const openCommand = process.platform === 'darwin' ? 'open' : process.platform === 'win32' ? 'start' : 'xdg-open';
exec(`${openCommand} ${tempFile}`, (error) => {
  if (error) {
    console.error('❌ Erreur:', error);
  }
  
  // Nettoyer après 10 secondes
  setTimeout(() => {
    fs.unlinkSync(tempFile);
    console.log('\n✅ Terminé! Relancez l\'application.');
  }, 10000);
});