#!/bin/bash

# Script pour surveiller le serveur Django

echo "🔍 Surveillance du serveur Django..."
echo "=================================="
echo ""

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Vérifier si le serveur Django est en cours d'exécution
check_django() {
    if curl -s -o /dev/null http://localhost:8000/ht/; then
        echo -e "${GREEN}✓ Serveur Django actif sur http://localhost:8000${NC}"
        return 0
    else
        echo -e "${RED}✗ Serveur Django inactif${NC}"
        return 1
    fi
}

# Afficher les logs en temps réel avec coloration
monitor_logs() {
    echo -e "\n${YELLOW}📋 Logs Django en temps réel:${NC}"
    echo "=================================="
    
    # Suivre le fichier de log Django
    if [ -f "/Users/<USER>/Nextcloud/Code/DjangoDEV/smatflow_membership/django.log" ]; then
        tail -f /Users/<USER>/Nextcloud/Code/DjangoDEV/smatflow_membership/django.log | while read line; do
            # Colorer selon le type de log
            if [[ $line == *"ERROR"* ]]; then
                echo -e "${RED}$line${NC}"
            elif [[ $line == *"WARNING"* ]]; then
                echo -e "${YELLOW}$line${NC}"
            elif [[ $line == *"POST"* ]] || [[ $line == *"GET"* ]]; then
                echo -e "${BLUE}$line${NC}"
            elif [[ $line == *"200"* ]]; then
                echo -e "${GREEN}$line${NC}"
            else
                echo "$line"
            fi
        done
    else
        echo -e "${YELLOW}⚠️  Fichier de log introuvable${NC}"
        echo "Surveillance de la sortie console du serveur..."
    fi
}

# Vérifier l'état initial
check_django

# Si le serveur n'est pas actif, proposer de le démarrer
if [ $? -ne 0 ]; then
    echo -e "\n${YELLOW}Voulez-vous démarrer le serveur Django? (y/n)${NC}"
    read -r response
    if [[ "$response" == "y" ]]; then
        echo -e "${GREEN}Démarrage du serveur Django...${NC}"
        cd /Users/<USER>/Nextcloud/Code/DjangoDEV/smatflow_membership
        python manage.py runserver &
        sleep 3
        check_django
    fi
fi

# Surveiller les logs
monitor_logs