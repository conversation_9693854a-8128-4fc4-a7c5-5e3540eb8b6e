#!/usr/bin/env node

/**
 * Script pour désactiver temporairement le Service Worker
 * et diagnostiquer si c'est la cause des tremblements
 */

console.log(`
🔧 Désactivation du Service Worker pour diagnostic
=================================================

Pour désactiver le Service Worker :

1. Dans Chrome DevTools :
   - Ouvrir l'onglet "Application"
   - Section "Service Workers" 
   - Cocher "Bypass for network"
   - Ou cliquer "Unregister" pour le supprimer

2. Vider le cache :
   - Onglet "Application" → "Storage"
   - Cliquer "Clear site data"

3. Recharger la page avec Ctrl+Shift+R

Si les tremblements disparaissent, le Service Worker est la cause.

ALTERNATIVE : Commenter temporairement l'enregistrement du SW
dans components/pwa/PWAProvider.tsx
`);

// Vérifier si un SW est actif
if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(registrations => {
    console.log(`\n📊 Service Workers actifs : ${registrations.length}`);
    registrations.forEach((reg, index) => {
      console.log(`   ${index + 1}. Scope: ${reg.scope}`);
      console.log(`      Active: ${reg.active ? 'Oui' : 'Non'}`);
    });
  });
}