#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Configuration
const PROJECT_ROOT = path.join(__dirname, '..');
const MANIFEST_DIR = path.join(PROJECT_ROOT, 'docs', 'manifest');
const OUTPUT_FILE = path.join(MANIFEST_DIR, 'manifest-v0.1.0-stable.json');

// Ensure manifest directory exists
if (!fs.existsSync(MANIFEST_DIR)) {
  fs.mkdirSync(MANIFEST_DIR, { recursive: true });
}

// Function to calculate SHA-256 hash
function calculateHash(filePath) {
  try {
    const content = fs.readFileSync(filePath);
    const hash = crypto.createHash('sha256');
    hash.update(content);
    return `sha256:${hash.digest('hex')}`;
  } catch (error) {
    console.error(`Error hashing ${filePath}:`, error.message);
    return null;
  }
}

// Function to count lines in a file
function countLines(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return content.split('\n').length;
  } catch (error) {
    return 0;
  }
}

// Function to determine file criticality
function determineCriticality(filePath) {
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  
  // Core files
  if (relativePath.includes('lib/utils.ts') ||
      relativePath.includes('contexts/AuthContext.tsx') ||
      relativePath.includes('contexts/ThemeContext.tsx') ||
      relativePath.includes('hooks/useToast.ts') ||
      relativePath.includes('hooks/useLocalStorage.ts')) {
    return 'CORE';
  }
  
  // High priority files
  if (relativePath.includes('components/layout/') ||
      relativePath.includes('components/ui/button.tsx') ||
      relativePath.includes('components/ui/card.tsx') ||
      relativePath.includes('hooks/')) {
    return 'HIGH';
  }
  
  // Medium priority files
  if (relativePath.includes('components/') ||
      relativePath.includes('stores/')) {
    return 'MEDIUM';
  }
  
  return 'LOW';
}

// Files to include in the manifest
const filesToInclude = [
  // Core library files
  'lib/utils.ts',
  
  // Context files
  'contexts/AuthContext.tsx',
  'contexts/ThemeContext.tsx',
  
  // Layout components
  'components/layout/Navigation.tsx',
  'components/layout/MainLayout.tsx',
  'components/layout/Sidebar.tsx',
  'components/layout/CountrySelector.tsx',
  
  // UI components
  'components/ui/button.tsx',
  'components/ui/card.tsx',
  'components/ui/toast.tsx',
  'components/ui/toaster.tsx',
  'components/ui/use-toast.ts',
  'components/ui/dialog.tsx',
  'components/ui/dropdown-menu.tsx',
  'components/ui/input.tsx',
  'components/ui/label.tsx',
  'components/ui/select.tsx',
  'components/ui/textarea.tsx',
  'components/ui/tabs.tsx',
  'components/ui/badge.tsx',
  'components/ui/progress.tsx',
  'components/ui/skeleton.tsx',
  'components/ui/avatar.tsx',
  
  // Feature components
  'components/expressions/ExpressionList.tsx',
  'components/expressions/ExpressionCard.tsx',
  'components/expressions/ExpressionDetail.tsx',
  'components/expressions/CreateExpressionDialog.tsx',
  'components/expressions/MediaUpload.tsx',
  
  // Hooks
  'hooks/useToast.ts',
  'hooks/useLocalStorage.ts',
  'hooks/useDebounce.ts',
  'hooks/useMediaQuery.ts',
  'hooks/useGeolocation.ts',
  'hooks/useNotifications.ts',
  'hooks/useCreateExpression.ts',
  'hooks/useExpressionSearch.ts',
  'hooks/useStreak.ts',
  'hooks/useBadges.ts',
  'hooks/useClassification.ts',
  
  // Stores
  'stores/notificationStore.ts',
  
  // App files
  'app/layout.tsx',
  'app/page.tsx',
  'app/globals.css',
  
  // Config files
  'next.config.ts',
  'tailwind.config.ts',
  'tsconfig.json',
  'package.json',
  
  // Public files
  'public/manifest.json',
  'public/sw.js'
];

// Generate manifest
const manifest = {
  manifest_version: "1.0.0",
  app_version: "0.1.0",
  generated_at: new Date().toISOString(),
  git_commit: "034296d",
  files: {}
};

// Process each file
filesToInclude.forEach(relPath => {
  const fullPath = path.join(PROJECT_ROOT, relPath);
  
  if (fs.existsSync(fullPath)) {
    const stats = fs.statSync(fullPath);
    const hash = calculateHash(fullPath);
    
    if (hash) {
      manifest.files[`/${relPath}`] = {
        hash: hash,
        size: stats.size,
        lines: countLines(fullPath),
        criticality: determineCriticality(fullPath),
        last_modified: stats.mtime.toISOString(),
        type: path.extname(relPath).slice(1) || 'unknown'
      };
    }
  } else {
    console.warn(`File not found: ${relPath}`);
  }
});

// Add manifest metadata
manifest.metadata = {
  total_files: Object.keys(manifest.files).length,
  total_size: Object.values(manifest.files).reduce((sum, file) => sum + file.size, 0),
  total_lines: Object.values(manifest.files).reduce((sum, file) => sum + file.lines, 0),
  criticality_distribution: {
    CORE: Object.values(manifest.files).filter(f => f.criticality === 'CORE').length,
    HIGH: Object.values(manifest.files).filter(f => f.criticality === 'HIGH').length,
    MEDIUM: Object.values(manifest.files).filter(f => f.criticality === 'MEDIUM').length,
    LOW: Object.values(manifest.files).filter(f => f.criticality === 'LOW').length
  }
};

// Write manifest to file
fs.writeFileSync(OUTPUT_FILE, JSON.stringify(manifest, null, 2));

console.log(`✅ Manifest generated successfully at: ${OUTPUT_FILE}`);
console.log(`📊 Total files: ${manifest.metadata.total_files}`);
console.log(`📦 Total size: ${(manifest.metadata.total_size / 1024 / 1024).toFixed(2)} MB`);
console.log(`📝 Total lines: ${manifest.metadata.total_lines}`);