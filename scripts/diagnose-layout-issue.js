#!/usr/bin/env node

/**
 * Diagnostic du problème de tremblement de la page
 */

console.log(`
🔍 Diagnostic du problème de tremblement
========================================

PROBLÈME IDENTIFIÉ:
- La page tremble vers le footer (oscillation verticale rapide)
- Warnings sur viewport et themeColor dans metadata

CAUSES POSSIBLES:

1. ✅ Metadata/Viewport (CORRIGÉ)
   - viewport et themeColor déplacés vers export viewport séparé
   
2. ✅ Structure Layout (CORRIGÉ)
   - Ajout de flex layout approprié
   - min-h-screen remplacé par flex-1 dans page.tsx
   - Footer avec mt-auto au lieu de mt-20
   
3. ✅ Stabilisation CSS (CORRIGÉ)
   - html, body { height: 100% }
   - Overflow-x: hidden pour éviter scrollbar horizontale
   - Structure flex cohérente

4. ✅ Infinite Scroll (CORRIGÉ)
   - Ajout de condition sortedExpressions.length > 0
   - Ajout de min-height sur loadMoreRef
   - Debounce de 200ms sur intersection observer
   - Condition !loading ajoutée

ACTIONS EFFECTUÉES:
1. Export viewport séparé dans layout.tsx
2. Structure flex complète (html → body → main → content)
3. CSS globals stabilisé
4. Intersection Observer débounced

PROCHAINES ÉTAPES SI LE PROBLÈME PERSISTE:
1. Vérifier les animations Framer Motion dans ThemedLayout
2. Désactiver temporairement les blobs animés
3. Vérifier la console pour des re-renders infinis
4. Inspecter le DOM pour des changements de hauteur

Pour tester:
1. Redémarrer le serveur dev (npm run dev)
2. Vider le cache du navigateur
3. Ouvrir les DevTools et observer:
   - Console pour erreurs/warnings
   - Network pour requêtes en boucle
   - Performance pour re-renders

Si le problème persiste, essayer:
- Désactiver les animations: supprimer motion.div temporairement
- Mode debug: ajouter console.log dans loadExpressions
`);

// Vérifier si le problème vient du service worker PWA
console.log(`
⚠️  VÉRIFICATION PWA:
Si vous avez un service worker actif, il peut causer des problèmes.
Pour le désactiver temporairement:
1. Ouvrir Chrome DevTools
2. Application → Service Workers
3. Cocher "Bypass for network"
4. Ou cliquer "Unregister"
`);