#!/usr/bin/env node

/**
 * Outils de gestion du manifeste anti-régression
 * 
 * Commandes disponibles :
 * - check : Compare l'état actuel avec le dernier manifeste stable
 * - restore : Restaure un fichier depuis une version stable
 * - clean : Identifie et nettoie les fichiers temporaires
 * - diff : Montre les différences avec le manifeste stable
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { execSync } = require('child_process');

const MANIFEST_DIR = path.join(__dirname, '../docs/manifest');
const PROJECT_ROOT = path.join(__dirname, '..');

// Couleurs pour le terminal
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

// Charge le dernier manifeste stable
function loadLatestManifest() {
  const files = fs.readdirSync(MANIFEST_DIR)
    .filter(f => f.startsWith('manifest-') && f.endsWith('-stable.json'))
    .sort()
    .reverse();
  
  if (files.length === 0) {
    console.error(`${colors.red}Aucun manifeste stable trouvé${colors.reset}`);
    process.exit(1);
  }
  
  console.log(`📋 Utilisation du manifeste : ${files[0]}`);
  const manifestPath = path.join(MANIFEST_DIR, files[0]);
  const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
  
  // Vérifier la structure
  if (!manifest.files) {
    console.error(`${colors.red}Structure invalide : propriété 'files' manquante${colors.reset}`);
    process.exit(1);
  }
  
  return manifest;
}

// Calcule le hash d'un fichier
function calculateFileHash(filePath) {
  if (!fs.existsSync(filePath)) return null;
  
  const content = fs.readFileSync(filePath);
  return crypto.createHash('sha256').update(content).digest('hex');
}

// Compare l'état actuel avec le manifeste
function checkRegression() {
  console.log('🔍 Vérification des régressions potentielles...\n');
  
  const manifest = loadLatestManifest();
  let hasRegression = false;
  let modifiedCore = [];
  let newFiles = [];
  let deletedFiles = [];
  
  // Vérifier les fichiers du manifeste
  for (const [filePath, fileInfo] of Object.entries(manifest.files)) {
    const fullPath = path.join(PROJECT_ROOT, filePath);
    const currentHash = calculateFileHash(fullPath);
    
    if (!currentHash) {
      deletedFiles.push(filePath);
      if (fileInfo.criticality === 'CORE') {
        hasRegression = true;
      }
    } else if (currentHash !== fileInfo.hash) {
      if (fileInfo.criticality === 'CORE') {
        modifiedCore.push({
          path: filePath,
          criticality: fileInfo.criticality,
          tests: fileInfo.tests || []
        });
      }
    }
  }
  
  // Scanner pour les nouveaux fichiers
  function scanDirectory(dir, baseDir = '') {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      if (item.startsWith('.') || item === 'node_modules' || item === 'coverage') continue;
      
      const fullPath = path.join(dir, item);
      const relativePath = path.join(baseDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath, relativePath);
      } else if (stat.isFile() && !manifest.files[`/${relativePath}`]) {
        // Nouveau fichier détecté
        if (relativePath.match(/debug-|test-|tmp-/)) {
          newFiles.push({ path: relativePath, temporary: true });
        } else {
          newFiles.push({ path: relativePath, temporary: false });
        }
      }
    }
  }
  
  scanDirectory(path.join(PROJECT_ROOT, 'components'), 'components');
  scanDirectory(path.join(PROJECT_ROOT, 'lib'), 'lib');
  scanDirectory(path.join(PROJECT_ROOT, 'scripts'), 'scripts');
  
  // Rapport
  console.log(`📊 Rapport basé sur ${colors.blue}${manifest.app_version}${colors.reset} (${manifest.generated_at})\n`);
  
  if (modifiedCore.length > 0) {
    console.log(`${colors.yellow}⚠️  Fichiers CORE modifiés :${colors.reset}`);
    modifiedCore.forEach(file => {
      console.log(`   - ${file.path}`);
      if (file.tests.length > 0) {
        console.log(`     Tests associés : ${file.tests.join(', ')}`);
      }
    });
    console.log('');
  }
  
  if (deletedFiles.length > 0) {
    console.log(`${colors.red}❌ Fichiers supprimés :${colors.reset}`);
    deletedFiles.forEach(file => {
      console.log(`   - ${file}`);
    });
    console.log('');
  }
  
  const tempFiles = newFiles.filter(f => f.temporary);
  if (tempFiles.length > 0) {
    console.log(`${colors.yellow}🧹 Fichiers temporaires détectés :${colors.reset}`);
    tempFiles.forEach(file => {
      console.log(`   - ${file.path}`);
    });
    console.log(`\n   Utilisez '${colors.green}node scripts/manifest-tools.js clean${colors.reset}' pour les nettoyer\n`);
  }
  
  if (hasRegression) {
    console.log(`${colors.red}🚨 RÉGRESSION POTENTIELLE DÉTECTÉE !${colors.reset}`);
    console.log(`   Des fichiers CORE ont été supprimés ou fortement modifiés.`);
    console.log(`   Vérifiez que tous les tests passent.`);
  } else if (modifiedCore.length === 0 && deletedFiles.length === 0) {
    console.log(`${colors.green}✅ Aucune régression détectée${colors.reset}`);
  }
}

// Nettoie les fichiers temporaires
function cleanTemporary() {
  console.log('🧹 Nettoyage des fichiers temporaires...\n');
  
  const patterns = [/debug-/, /test-/, /tmp-/];
  let cleaned = 0;
  
  function scanAndClean(dir, baseDir = '') {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      if (item.startsWith('.') || item === 'node_modules') continue;
      
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanAndClean(fullPath, path.join(baseDir, item));
      } else if (patterns.some(p => item.match(p))) {
        console.log(`   Suppression : ${path.join(baseDir, item)}`);
        fs.unlinkSync(fullPath);
        cleaned++;
      }
    }
  }
  
  scanAndClean(path.join(PROJECT_ROOT, 'scripts'), 'scripts');
  
  console.log(`\n${colors.green}✅ ${cleaned} fichiers temporaires supprimés${colors.reset}`);
}

// Restaure un fichier depuis le manifeste
function restoreFile(filePath) {
  const manifest = loadLatestManifest();
  const fileInfo = manifest.files[filePath] || manifest.files[`/${filePath}`];
  
  if (!fileInfo) {
    console.error(`${colors.red}Fichier non trouvé dans le manifeste : ${filePath}${colors.reset}`);
    process.exit(1);
  }
  
  console.log(`🔄 Restauration de ${filePath} depuis ${manifest.git_commit}...`);
  
  try {
    execSync(`git show ${manifest.git_commit}:${filePath.replace(/^\//, '')} > ${path.join(PROJECT_ROOT, filePath)}`, {
      cwd: PROJECT_ROOT
    });
    console.log(`${colors.green}✅ Fichier restauré avec succès${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}Erreur lors de la restauration : ${error.message}${colors.reset}`);
  }
}

// Main
const command = process.argv[2];
const args = process.argv.slice(3);

switch (command) {
  case 'check':
    checkRegression();
    break;
    
  case 'clean':
    cleanTemporary();
    break;
    
  case 'restore':
    if (args.length === 0) {
      console.error('Usage: manifest-tools.js restore <file-path>');
      process.exit(1);
    }
    restoreFile(args[0]);
    break;
    
  default:
    console.log('Outils de gestion du manifeste anti-régression\n');
    console.log('Usage:');
    console.log('  node scripts/manifest-tools.js check    - Vérifie les régressions');
    console.log('  node scripts/manifest-tools.js clean    - Nettoie les fichiers temporaires');
    console.log('  node scripts/manifest-tools.js restore <file>  - Restaure un fichier');
}