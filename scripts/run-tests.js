#!/usr/bin/env node

/**
 * Test Runner Script
 * Runs Jest tests with proper JSON output and error handling
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

// Ensure test-reports directory exists
const testReportsDir = path.join(__dirname, '..', 'test-reports');
if (!fs.existsSync(testReportsDir)) {
  console.log(`${colors.blue}Creating test-reports directory...${colors.reset}`);
  fs.mkdirSync(testReportsDir, { recursive: true });
}

// Clean up old test results
const testResultsPath = path.join(testReportsDir, 'test-results.json');
if (fs.existsSync(testResultsPath)) {
  fs.unlinkSync(testResultsPath);
}

console.log(`${colors.cyan}🧪 Running tests...${colors.reset}\n`);

try {
  // Run tests with coverage and JSON output
  const command = 'npm run test:dashboard';
  console.log(`${colors.blue}Executing: ${command}${colors.reset}\n`);
  
  execSync(command, {
    stdio: 'inherit',
    env: {
      ...process.env,
      CI: 'true', // Ensures non-interactive mode
    }
  });
  
  console.log(`\n${colors.green}✅ Tests completed successfully!${colors.reset}`);
  
  // Check if test results file was created
  if (fs.existsSync(testResultsPath)) {
    const results = JSON.parse(fs.readFileSync(testResultsPath, 'utf8'));
    
    console.log(`\n${colors.cyan}📊 Test Summary:${colors.reset}`);
    console.log(`   Total Tests: ${results.numTotalTests}`);
    console.log(`   Passed: ${colors.green}${results.numPassedTests}${colors.reset}`);
    console.log(`   Failed: ${colors.red}${results.numFailedTests}${colors.reset}`);
    console.log(`   Skipped: ${colors.yellow}${results.numPendingTests}${colors.reset}`);
    
    if (results.testResults) {
      console.log(`\n${colors.cyan}📁 Test Files:${colors.reset}`);
      results.testResults.forEach((file) => {
        const fileName = path.basename(file.name);
        const status = file.status === 'passed' ? 
          `${colors.green}✓${colors.reset}` : 
          `${colors.red}✗${colors.reset}`;
        console.log(`   ${status} ${fileName}`);
      });
    }
  }
  
} catch (error) {
  console.error(`\n${colors.red}❌ Tests failed!${colors.reset}`);
  
  // Try to parse and display test results even on failure
  if (fs.existsSync(testResultsPath)) {
    try {
      const results = JSON.parse(fs.readFileSync(testResultsPath, 'utf8'));
      
      if (results.testResults) {
        console.log(`\n${colors.red}Failed Tests:${colors.reset}`);
        results.testResults.forEach((file) => {
          if (file.status === 'failed' && file.assertionResults) {
            console.log(`\n  ${colors.yellow}${path.basename(file.name)}:${colors.reset}`);
            file.assertionResults
              .filter(test => test.status === 'failed')
              .forEach(test => {
                console.log(`    ${colors.red}✗ ${test.title}${colors.reset}`);
                if (test.failureMessages && test.failureMessages.length > 0) {
                  console.log(`      ${test.failureMessages[0].split('\n')[0]}`);
                }
              });
          }
        });
      }
    } catch (parseError) {
      console.error('Could not parse test results:', parseError.message);
    }
  }
  
  process.exit(1);
}

console.log(`\n${colors.blue}Test results saved to: ${testResultsPath}${colors.reset}`);