#!/usr/bin/env node

/**
 * Script pour générer toutes les icônes PWA à partir d'une image source
 * Utilise Sharp pour redimensionner et optimiser les images
 */

const sharp = require('sharp');
const fs = require('fs').promises;
const path = require('path');

// Configuration des tailles d'icônes
const ICON_SIZES = [
  { size: 72, name: 'icon-72x72.png' },
  { size: 96, name: 'icon-96x96.png' },
  { size: 128, name: 'icon-128x128.png' },
  { size: 144, name: 'icon-144x144.png' },
  { size: 152, name: 'icon-152x152.png' },
  { size: 192, name: 'icon-192x192.png' },
  { size: 384, name: 'icon-384x384.png' },
  { size: 512, name: 'icon-512x512.png' },
];

// Icônes avec zone de sécurité pour maskable
const MASKABLE_SIZES = [
  { size: 192, name: 'icon-maskable-192x192.png' },
  { size: 512, name: 'icon-maskable-512x512.png' },
];

// Icônes spéciales
const SPECIAL_ICONS = [
  { size: 72, name: 'badge-72x72.png' },
  { size: 96, name: 'shortcut-create.png' },
  { size: 96, name: 'shortcut-profile.png' },
];

async function generateIcons() {
  try {
    // Créer le dossier icons s'il n'existe pas
    const iconsDir = path.join(process.cwd(), 'public', 'icons');
    await fs.mkdir(iconsDir, { recursive: true });

    // Image source (créons une image de base avec le logo)
    const sourceImage = await createBaseIcon();
    
    console.log('🎨 Génération des icônes PWA...');

    // Générer les icônes standard
    for (const { size, name } of ICON_SIZES) {
      await sharp(sourceImage)
        .resize(size, size, {
          fit: 'contain',
          background: { r: 59, g: 130, b: 246, alpha: 1 } // bg-blue-500
        })
        .png({ quality: 90 })
        .toFile(path.join(iconsDir, name));
      
      console.log(`✓ ${name} généré`);
    }

    // Générer les icônes maskable (avec padding)
    for (const { size, name } of MASKABLE_SIZES) {
      const padding = Math.floor(size * 0.1); // 10% de padding
      await sharp(sourceImage)
        .resize(size - padding * 2, size - padding * 2, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }
        })
        .extend({
          top: padding,
          bottom: padding,
          left: padding,
          right: padding,
          background: { r: 59, g: 130, b: 246, alpha: 1 }
        })
        .png({ quality: 90 })
        .toFile(path.join(iconsDir, name));
      
      console.log(`✓ ${name} (maskable) généré`);
    }

    // Générer le favicon
    await sharp(sourceImage)
      .resize(32, 32)
      .toFile(path.join(process.cwd(), 'public', 'favicon.ico'));
    console.log('✓ favicon.ico généré');

    // Générer les captures d'écran (placeholder)
    await generateScreenshots();

    console.log('\n✅ Toutes les icônes ont été générées avec succès !');
    console.log('📁 Emplacement : public/icons/');

  } catch (error) {
    console.error('❌ Erreur lors de la génération des icônes :', error);
    process.exit(1);
  }
}

// Créer une icône de base avec SVG
async function createBaseIcon() {
  const svg = `
    <svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
      <rect width="512" height="512" fill="#3b82f6" rx="64"/>
      <g transform="translate(256, 256)">
        <!-- Target/Pillar icon -->
        <circle cx="0" cy="0" r="180" fill="none" stroke="white" stroke-width="20"/>
        <circle cx="0" cy="0" r="120" fill="none" stroke="white" stroke-width="20"/>
        <circle cx="0" cy="0" r="60" fill="white"/>
        
        <!-- Arrow pointing to center -->
        <path d="M -150 -150 L -80 -80 M -80 -80 L -80 -120 M -80 -80 L -120 -80" 
              stroke="white" stroke-width="20" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
      
      <!-- Text -->
      <text x="256" y="450" font-family="Arial, sans-serif" font-size="48" font-weight="bold" 
            text-anchor="middle" fill="white">PillarScan</text>
    </svg>
  `;

  return Buffer.from(svg);
}

// Générer des captures d'écran placeholder
async function generateScreenshots() {
  const screenshotsDir = path.join(process.cwd(), 'public', 'screenshots');
  await fs.mkdir(screenshotsDir, { recursive: true });

  const screenshots = [
    { name: 'home.png', title: 'Accueil' },
    { name: 'feed.png', title: 'Feed' },
    { name: 'create.png', title: 'Créer' },
  ];

  for (const { name, title } of screenshots) {
    const svg = `
      <svg width="1280" height="720" viewBox="0 0 1280 720" xmlns="http://www.w3.org/2000/svg">
        <rect width="1280" height="720" fill="#f3f4f6"/>
        <rect x="0" y="0" width="1280" height="60" fill="#3b82f6"/>
        <text x="640" y="40" font-family="Arial" font-size="24" font-weight="bold" 
              text-anchor="middle" fill="white">PillarScan - ${title}</text>
        
        <!-- Content placeholder -->
        <rect x="40" y="100" width="400" height="200" rx="8" fill="#e5e7eb"/>
        <rect x="480" y="100" width="400" height="200" rx="8" fill="#e5e7eb"/>
        <rect x="920" y="100" width="320" height="580" rx="8" fill="#e5e7eb"/>
        
        <rect x="40" y="340" width="400" height="200" rx="8" fill="#e5e7eb"/>
        <rect x="480" y="340" width="400" height="200" rx="8" fill="#e5e7eb"/>
      </svg>
    `;

    await sharp(Buffer.from(svg))
      .png({ quality: 80 })
      .toFile(path.join(screenshotsDir, name));
    
    console.log(`✓ Screenshot ${name} généré`);
  }
}

// Lancer le script
generateIcons();