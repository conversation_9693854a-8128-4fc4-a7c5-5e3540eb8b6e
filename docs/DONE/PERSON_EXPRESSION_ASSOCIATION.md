# 🔗 Association Person-Expression dans PillarScan

Date : 24 janvier 2025

## 📊 État des lieux

### 1. <PERSON><PERSON><PERSON><PERSON> (CivicPerson)

Le modèle Person supporte **3 types** :

```python
person_type = columns.Text(required=True)  # 'HUMAN', 'MORAL', 'GROUP'
```

- **HUMAN** : Personne physique (individu)
- **MORAL** : Personne morale (entreprise, organisation)
- **GROUP** : Groupe de personnes

### 2. Frontend - Déjà implémenté ✅

Le frontend a **tout ce qu'il faut** :

#### Composant PersonMention (`components/pillarscan/expression/PersonMention.tsx`)
- Recherche de personnes par type
- Sélection du rôle (target, source, owner)
- Support des personnes temporaires (à résoudre après)
- Interface drag & drop intuitive

#### Types TypeScript (`types/person.ts`)
```typescript
export interface PersonReference {
  person_id?: string;      // UUID si existe
  person_code?: string;    
  person_name: string;     // Requis
  person_type: PersonType;
  role: 'target' | 'source' | 'owner';
  temp_id?: string;        // Si temporaire
  needs_resolution?: boolean;
}
```

#### Page de création (`app/create/page.tsx`)
- État `personReferences`
- Fonction de recherche (mockée)
- Envoi avec l'expression

### 3. Backend - À implémenter ⚠️

**Manques identifiés** :

1. **Pas de modèle de liaison** Person-Expression
2. **Serializers incomplets** : pas de `person_references`
3. **API de recherche** : endpoint manquant
4. **Résolution des personnes temporaires** : logique absente

## 🛠️ Implémentation requise

### 1. Créer le modèle de liaison (Cassandra)

```python
# pillar_scan/models/expressions.py

class PillarScanPersonReference(Model):
    """
    Liaison entre une expression et des personnes mentionnées
    """
    __table_name__ = 'pillarscan_person_references'
    
    # Clés de partition
    expression_id = columns.UUID(partition_key=True)
    person_id = columns.UUID(primary_key=True)
    
    # Métadonnées
    role = columns.Text(required=True)  # target, source, owner
    person_name = columns.Text(required=True)  # Pour cache
    person_type = columns.Text(required=True)  # HUMAN, MORAL, GROUP
    needs_resolution = columns.Boolean(default=False)
    temp_id = columns.Text()  # Si personne temporaire
    
    # Timestamps
    created_at = columns.DateTime(default=datetime.utcnow)
    datakey = columns.Text(required=True)
```

### 2. Mettre à jour les serializers

```python
# pillar_scan/api/v2/serializers.py

class PersonReferenceSerializer(serializers.Serializer):
    person_id = serializers.UUIDField(required=False)
    person_code = serializers.CharField(required=False)
    person_name = serializers.CharField(required=True)
    person_type = serializers.ChoiceField(choices=['HUMAN', 'MORAL', 'GROUP'])
    role = serializers.ChoiceField(choices=['target', 'source', 'owner'])
    temp_id = serializers.CharField(required=False)
    needs_resolution = serializers.BooleanField(default=False)

class CreateExpressionSerializer(serializers.Serializer):
    # ... champs existants ...
    
    # Ajouter :
    person_references = PersonReferenceSerializer(many=True, required=False)
```

### 3. Créer l'endpoint de recherche

```python
# civicperson/api/v2/person/views.py

@action(detail=False, methods=['get'])
def search(self, request):
    """Recherche de personnes par nom et type"""
    query = request.query_params.get('q', '')
    person_type = request.query_params.get('type', None)
    limit = int(request.query_params.get('limit', 10))
    
    if len(query) < 2:
        return Response({'results': []})
    
    # Recherche dans Cassandra
    filters = {'name__icontains': query}
    if person_type:
        filters['person_type'] = person_type
    
    persons = Person.objects.filter(**filters).limit(limit)
    
    return Response({
        'results': PersonSerializer(persons, many=True).data
    })
```

### 4. Gérer les références dans la création d'expression

```python
# pillar_scan/api/v2/views.py

def create(self, request):
    # ... code existant ...
    
    # Après création de l'expression
    person_references = validated_data.pop('person_references', [])
    
    for ref in person_references:
        if ref.get('needs_resolution'):
            # Créer une personne temporaire
            # ou marquer pour résolution future
            pass
        
        # Créer la liaison
        PillarScanPersonReference.create(
            expression_id=expression.expression_id,
            person_id=ref.get('person_id'),
            role=ref['role'],
            person_name=ref['person_name'],
            person_type=ref['person_type'],
            needs_resolution=ref.get('needs_resolution', False),
            temp_id=ref.get('temp_id'),
            datakey=generate_datakey()
        )
```

## 🔄 Workflow complet

1. **Frontend** : L'utilisateur tape "@" ou clique sur le bouton mention
2. **Frontend** : Recherche via `/api/v2/civicperson/person/search/`
3. **Frontend** : Sélection et attribution du rôle
4. **Frontend** : Envoi avec `person_references` dans la création
5. **Backend** : Création de l'expression
6. **Backend** : Création des liaisons dans `pillarscan_person_references`
7. **Backend** : Si `needs_resolution=true`, marquer pour traitement ultérieur

## 📋 TODO Prioritaires

### Backend (Django)

- [ ] Créer le modèle `PillarScanPersonReference`
- [ ] Ajouter `person_references` aux serializers
- [ ] Implémenter l'endpoint `/api/v2/civicperson/person/search/`
- [ ] Gérer les références dans la création d'expression
- [ ] Créer un job Celery pour résoudre les personnes temporaires

### Frontend (Next.js)

- [ ] Connecter la recherche à l'API réelle (remplacer le mock)
- [ ] Gérer les erreurs de recherche
- [ ] Afficher les personnes mentionnées dans le feed
- [ ] Permettre de cliquer sur une personne pour voir son profil

## 🎯 Avantages de cette approche

1. **Flexibilité** : Support des 3 types de personnes
2. **Performance** : Liaison séparée pour requêtes optimisées
3. **Évolutivité** : Peut ajouter d'autres rôles facilement
4. **Résolution différée** : Permet de mentionner des personnes qui n'existent pas encore

## 🚀 Prochaines étapes

1. Implémenter le modèle Cassandra
2. Créer l'endpoint de recherche
3. Tester avec des vraies données
4. Documenter l'API

L'association Person-Expression est déjà bien préparée côté frontend. Il suffit d'implémenter le backend pour avoir une fonctionnalité complète !