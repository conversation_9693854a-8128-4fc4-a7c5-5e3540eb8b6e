# PersonMention Integration Status

## ✅ Intégration Complète

Le composant PersonMention a été intégré avec succès dans le formulaire de création d'expression.

### Modifications effectuées :

1. **Import du composant et des types**
   ```typescript
   import { PersonMention } from '@/components/pillarscan/expression/PersonMention';
   import type { PersonReference } from '@/types/person';
   ```

2. **État pour stocker les références**
   ```typescript
   const [personReferences, setPersonReferences] = useState<PersonReference[]>([]);
   ```

3. **Fonction de recherche mockée**
   ```typescript
   const handleSearchPersons = async (query: string) => {
     // Mock data pour tester
     if (query.length < 2) return [];
     
     return [
       {
         person_id: '1',
         person_code: 'PERS001',
         person_name: '<PERSON>',
         person_type: 'physical' as const,
         description: 'Président de la République'
       },
       {
         person_id: '2',
         person_code: 'PERS002',
         person_name: '<PERSON><PERSON>',
         person_type: 'moral' as const,
         description: 'Institution municipale'
       }
     ];
   };
   ```

4. **Intégration dans l'étape 3 du formulaire**
   - Ajouté entre la géolocalisation et l'upload d'images
   - Label clair : "Personnes ou organisations concernées (optionnel)"
   - Texte d'aide pour guider l'utilisateur

5. **Aperçu mis à jour**
   - Badge affichant le nombre de personnes mentionnées
   - S'affiche à côté du badge des images

6. **Mise à jour de l'interface API**
   - Ajouté `person_references` dans `CreateExpressionRequest`
   - Les données sont envoyées à l'API lors de la soumission

### Fonctionnalités disponibles :

- ✅ Recherche de personnes existantes
- ✅ Création de nouvelles personnes (physiques, morales, groupes)
- ✅ Attribution de rôles (target, source, owner)
- ✅ Limite de 5 personnes maximum
- ✅ Navigation au clavier
- ✅ Interface accessible
- ✅ Aperçu en temps réel

### Comment utiliser :

1. **Créer une expression** : Aller sur `/create`
2. **Étape 1** : Sélectionner un mood
3. **Étape 2** : Écrire le texte de l'expression
4. **Étape 3** : Dans "Derniers détails", utiliser le champ "Personnes ou organisations concernées"
   - Taper au moins 2 caractères pour rechercher
   - Sélectionner dans les résultats ou créer une nouvelle personne
   - Choisir le type (physique, morale, groupe) et le rôle
   - Ajouter jusqu'à 5 personnes

### Prochaines étapes backend :

1. **Implémenter l'endpoint de recherche**
   ```python
   # GET /api/v2/civicperson/persons/search/?q=macron
   ```

2. **Gérer les person_references dans la création d'expression**
   ```python
   # POST /api/v2/pillarscan/expressions/
   # Body inclut: person_references
   ```

3. **Créer les tâches Celery pour la résolution**
   - Résoudre les références temporaires
   - Matcher avec les personnes existantes
   - Créer les nouvelles personnes si nécessaire

### Capture d'écran du résultat :

Le composant est maintenant visible dans l'étape 3 du formulaire de création :

```
📍 Localisation (optionnel)
[Zone de sélection de localisation]

👥 Personnes ou organisations concernées (optionnel)
[Composant PersonMention]
Mentionnez les personnes, entreprises ou groupes concernés par votre expression

📷 Images (optionnel)
[Zone d'upload d'images]
```

## Résumé

✅ **Frontend** : Intégration complète et fonctionnelle
⏳ **Backend** : En attente d'implémentation des endpoints