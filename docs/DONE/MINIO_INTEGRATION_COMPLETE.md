# 🎯 Intégration MinIO Complète - PillarScan

Date : 24 janvier 2025

## ✅ Analyse Effectuée

### 1. Social Monitor - Modèle de référence

L'application `social_monitor` utilise déjà MinIO avec succès :

```python
# social_monitor/services/social_media_enrichment_service.py
self.minio_service = MinIOService()
media_info = self.minio_service.upload_model_media(
    entity_class=CivicExpression,
    entity_code=civic_expression.expression_code,
    media_role=media_role_prefix,
    file_data=file_data,
    media_type=self._get_media_type(local_path),
    content_type=self._get_content_type(local_path)
)
```

### 2. PillarScan Backend - Déjà implémenté !

**BONNE NOUVELLE** : PillarScan a déjà l'upload MinIO fonctionnel :

```python
# pillar_scan/api/v2/views.py (ligne 448)
minio_service = MinIOService()
media_info = minio_service.upload_model_media(
    entity_class=PillarScanExpression,
    entity_code=str(expression_id),
    media_role='expression_image',
    file_data=file_data,
    media_type='image',
    content_type=content_type
)
```

**Endpoint disponible** : `POST /api/v2/pillarscan/expressions/{id}/upload_media/`

### 3. MediaHub - Service générique

MediaHub offre un endpoint générique d'upload :

```python
# mediahub/api/v1/views.py
POST /api/v2/media/upload/
```

Accepte :
- `file` : Le fichier à uploader
- `entity_type` : Type d'entité (ex: PillarScanExpression)
- `entity_id` : ID de l'entité
- `media_role` : Rôle du média
- `title` : Titre du média

## 🔧 Modifications Frontend Effectuées

### 1. Suppression des mocks

- ❌ Supprimé `lib/api/mock-backend.ts`
- ❌ Supprimé `app/api/upload/route.ts` (proxy local)

### 2. Connexion directe à MediaHub

Modifié `lib/api/client.ts` pour utiliser directement l'API Django :

```typescript
// Nouveau flux :
1. Créer l'expression d'abord
2. Upload les médias vers MediaHub avec l'ID de l'expression
3. MediaHub gère automatiquement les media_refs

const uploadResponse = await fetch(`${this.baseUrl}/api/v2/media/upload/`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${this.getToken()}`,
  },
  body: uploadFormData
});
```

## 📊 Configuration MinIO Backend

### Variables d'environnement requises

```bash
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=smatflow-media
MINIO_SECURE=false
MINIO_PUBLIC_URL=http://localhost:9000
```

### Structure des buckets

```
smatflow-media/
├── expressions/
│   ├── images/
│   ├── videos/
│   └── thumbnails/
├── profiles/
│   └── avatars/
└── documents/
```

## 🚀 Flux d'upload complet

1. **Frontend** : Sélection des fichiers
2. **Frontend** : Création de l'expression (sans médias)
3. **Frontend** : Upload vers `/api/v2/media/upload/` pour chaque fichier
4. **Backend** : MinIOService upload vers le bucket
5. **Backend** : Création MediaMetadata
6. **Backend** : Association automatique à l'expression
7. **Frontend** : Réception des media_ids

## ✅ Avantages de cette approche

1. **Pas de proxy** : Communication directe avec Django
2. **Sécurité** : JWT vérifié côté Django
3. **Traçabilité** : MediaMetadata pour chaque upload
4. **Flexibilité** : Support multi-types (image, video, document)
5. **Performance** : Upload direct vers MinIO

## 🔍 Options d'endpoints

### Option 1 : MediaHub générique (RECOMMANDÉ)
```
POST /api/v2/media/upload/
```
- Plus flexible
- Réutilisable pour d'autres entités
- Gestion centralisée

### Option 2 : Endpoint spécifique PillarScan
```
POST /api/v2/pillarscan/expressions/{id}/upload_media/
```
- Plus spécifique
- Validation métier intégrée
- Nécessite l'ID de l'expression

## 📋 TODO

### 1. Configuration Production

- [ ] Configurer MinIO en production
- [ ] SSL/TLS pour MinIO
- [ ] CDN pour les médias
- [ ] Backup strategy

### 2. Optimisations

- [ ] Compression des images
- [ ] Génération de thumbnails
- [ ] Lazy loading
- [ ] Cache CDN

### 3. Fonctionnalités avancées

- [ ] Upload multiple en parallèle
- [ ] Progress bar
- [ ] Retry automatique
- [ ] Mode offline avec sync

## 🎯 Conclusion

L'intégration MinIO est **déjà fonctionnelle** côté backend ! Il suffisait de :
1. Supprimer les mocks frontend
2. Utiliser l'endpoint MediaHub existant
3. Laisser Django gérer MinIO

Le système est maintenant prêt pour la production avec upload réel vers MinIO.