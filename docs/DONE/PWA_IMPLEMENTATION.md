# Implémentation PWA - PillarScan

## Vue d'ensemble

PillarScan est maintenant une Progressive Web App (PWA) complète offrant :
- 📱 Installation sur mobile et desktop
- 🔌 Mode hors ligne complet
- 🔄 Synchronisation en arrière-plan
- 📲 Notifications push
- ⚡ Performances optimisées

## Architecture PWA

### 1. Manifest (`/public/manifest.json`)
Définit les métadonnées de l'application :
- Nom et description
- Icônes (multiple tailles)
- Couleurs du thème
- Mode d'affichage standalone
- Raccourcis d'accès rapide
- Share target pour recevoir des partages

### 2. Service Worker (`/public/sw.js`)
Gère le cache et le mode hors ligne :

#### Stratégies de cache
- **Cache First** : Assets statiques (JS, CSS, images)
- **Network First** : Données API avec fallback cache
- **Network Only** : Auth et notifications temps réel

#### Caches utilisés
- `pillarscan-v1` : Ressources statiques essentielles
- `pillarscan-dynamic-v1` : Assets dynamiques
- `pillarscan-data-v1` : Réponses API

### 3. IndexedDB (OfflineStorageService)
Stockage local structuré pour :

#### Stores
1. **cached_expressions** : Expressions pour consultation hors ligne
2. **pending_expressions** : Expressions créées hors ligne
3. **offline_media** : Médias en attente d'upload
4. **sync_metadata** : Métadonnées de synchronisation

#### API principale
```typescript
// Initialiser
const storage = getOfflineStorage();
await storage.init();

// Cacher des expressions
await storage.cacheExpressions(expressions);

// Ajouter une expression en attente
const id = await storage.addPendingExpression(token, data);

// Stocker un média hors ligne
const mediaId = await storage.storeOfflineMedia(file);
```

### 4. Composants UI

#### InstallPrompt
- Détecte la disponibilité d'installation
- Affiche un prompt élégant
- Gère iOS avec instructions manuelles
- Respecte les préférences utilisateur (dismiss 7 jours)

#### OfflineIndicator
- Affiche le statut de connexion
- Animation lors des changements
- Déclenche la sync automatique

#### PWAProvider
- Enregistre le Service Worker
- Initialise IndexedDB
- Gère les mises à jour
- Écoute les messages du SW

## Fonctionnalités Offline

### 1. Navigation
- Pages statiques toujours disponibles
- Page `/offline` dédiée
- Cache des assets Next.js

### 2. Consultation
- Expressions mises en cache automatiquement
- Images et médias stockés localement
- Navigation dans le feed hors ligne

### 3. Création
- Formulaires fonctionnels hors ligne
- Stockage local des brouillons
- Upload différé des médias

### 4. Synchronisation
- Background Sync API
- Retry automatique avec backoff
- Notification de succès
- Gestion des conflits

## Installation et Test

### 1. Installation PWA

#### Desktop (Chrome/Edge)
1. Icône d'installation dans la barre d'adresse
2. Ou menu → "Installer PillarScan"

#### Mobile Android
1. Banner d'installation automatique
2. Ou menu → "Ajouter à l'écran d'accueil"

#### iOS
1. Bouton partage Safari
2. "Sur l'écran d'accueil"
3. "Ajouter"

### 2. Tester le mode offline

```bash
# 1. Installer l'app
# 2. Charger quelques pages
# 3. Activer le mode avion
# 4. Naviguer dans l'app
# 5. Créer une expression
# 6. Désactiver le mode avion
# 7. Vérifier la synchronisation
```

### 3. Debug

#### Chrome DevTools
1. Application → Service Workers
2. Application → Storage → IndexedDB
3. Network → Offline checkbox

#### Logs
```javascript
// Dans la console
navigator.serviceWorker.controller.postMessage({type: 'DEBUG'});
```

## Optimisations

### 1. Taille du cache
- Limite : 50MB par domaine
- Nettoyage automatique après 7 jours
- LRU pour les ressources dynamiques

### 2. Performance
- Precache des routes critiques
- Lazy loading des images
- Compression des assets

### 3. Batterie
- Sync différée en cas de batterie faible
- Pas de sync en économie d'énergie
- Respect des préférences data

## Évolutions futures

### 1. Push Notifications
```javascript
// Backend requis
Notification.requestPermission();
registration.pushManager.subscribe({
  userVisibleOnly: true,
  applicationServerKey: urlBase64ToUint8Array(publicVapidKey)
});
```

### 2. Periodic Background Sync
```javascript
// Chrome 80+
registration.periodicSync.register('check-updates', {
  minInterval: 24 * 60 * 60 * 1000 // 24h
});
```

### 3. Web Share Target
- Recevoir des partages d'autres apps
- Handler dans le manifest configuré
- Endpoint `/share` à implémenter

### 4. Badging API
```javascript
// Afficher un badge sur l'icône
navigator.setAppBadge(unreadCount);
```

## Commandes utiles

```bash
# Générer les icônes
npm run generate-icons

# Build avec optimisations PWA
npm run build

# Servir en HTTPS local (requis pour SW)
npm run serve:https
```

## Checklist PWA

- ✅ HTTPS en production
- ✅ Manifest valide
- ✅ Service Worker fonctionnel
- ✅ Icônes toutes tailles
- ✅ Splash screens (via manifest)
- ✅ Mode offline
- ✅ Installation prompt
- ✅ Meta tags appropriés
- ✅ Theme color
- ✅ Viewport optimisé

## Ressources

- [PWA Checklist](https://web.dev/pwa-checklist/)
- [Service Worker Cookbook](https://serviceworke.rs/)
- [IndexedDB Best Practices](https://web.dev/indexeddb-best-practices/)
- [Workbox (alternative)](https://developers.google.com/web/tools/workbox)