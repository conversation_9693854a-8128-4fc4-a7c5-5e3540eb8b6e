# 📚 Documentation Complète des Applications Backend PillarScan

## Vue d'ensemble

PillarScan utilise plusieurs applications Django du projet smatflow_membership. Voici la documentation complète de toutes les applications backend et leurs endpoints.

## 🌍 1. Geography (`geography`)

**Description**: Gestion de la localisation géographique et de la souveraineté des données par pays.

### Architecture de la souveraineté des données

- **Middleware CountryEnforcementMiddleware**: Force la présence d'un pays dans toutes les requêtes
- **Header obligatoire**: `X-Country-Code` (ex: FR, US, CA, GB, etc.)
- **Filtrage automatique**: Toutes les données sont filtrées par pays
- **Pays supportés**: FR, US, CA, GB, DE, ES, IT, BR, JP, CN, IN, MX, AU, ZA, NG, EG, MA, TN, SN, CI, CM, KE, GH, ET, UG, TZ, DZ, SD, MG, AO

### Gestion des erreurs par pays

- **Pays non supporté**: 
  - Pour les endpoints PillarScan (`/api/v2/expressions/`): Retourne une liste vide avec message (pas d'erreur)
  - Pour les autres endpoints: Erreur 400 avec liste des pays valides
- **Pays manquant**: Erreur 400 avec message explicite
- **Pays non autorisé pour l'utilisateur**: Erreur 403

### Modèles de données géographiques

#### GeographyCountry (Cassandra)
- **Table**: `geography.geographycountry`
- **Champs principaux**:
  - `country_id` (UUID): Identifiant unique
  - `country_code` (Text, 2 chars): Code ISO 3166-1 alpha-2 (ex: FR, US)
  - `name` (Text): Nom commun en anglais
  - `official_name` (Text): Nom officiel
  - `cca3` (Text): Code ISO 3-lettres
  - `region` (Text): Région (ex: Europe, Africa)
  - `subregion` (Text): Sous-région
  - `active` (Boolean): Si le pays est actif dans le système
  - `currency_refs` (Map): Références vers les devises
  - `language_refs` (Map): Références vers les langues

#### GeographyCurrency (Cassandra)
- **Table**: `geography.geographycurrency`
- **Champs**: `currency_id`, `currency_code`, `name`, `symbol`

#### GeographyLanguage (Cassandra)
- **Table**: `geography.geographylanguage`
- **Champs**: `language_id`, `language_code`, `name`, `native_name`

### Services disponibles

#### GeographyLoadingService
- `get_country_by_code(country_code)`: Récupère un pays avec ses médias
- `get_all_countries()`: Liste tous les pays actifs
- `get_countries_by_region(region)`: Pays par région
- `search_countries(search_term)`: Recherche de pays

### Endpoints Geography (`/api/geography/`)

| Méthode | Endpoint | Description | Statut |
|---------|----------|-------------|---------|
| GET | `/countries/` | Liste des pays supportés | ✅ Actif |
| GET | `/countries/{code}/` | Détails d'un pays | ✅ Actif |
| GET | `/countries/by-region/{region}/` | Pays par région | ✅ Actif |
| GET | `/countries/regions/` | Liste des régions | ✅ Actif |
| GET | `/countries/{code}/cities/` | Villes d'un pays | ✅ Actif |
| GET | `/validate-location/` | Valider coordonnées GPS | ✅ Actif |

## 🔐 2. CivicPerson (`civicperson`)

**Description**: Gestion des personnes physiques et morales, authentification JWT.

### Endpoints d'authentification (`/api/v2/civicperson/auth/`)

| Méthode | Endpoint | Description | Statut |
|---------|----------|-------------|---------|
| POST | `/login/` | Connexion avec email/password | ✅ Utilisé |
| POST | `/register/` | Inscription nouvel utilisateur | ✅ Utilisé |
| GET | `/profile/` | Récupérer profil utilisateur | ✅ Utilisé |
| POST | `/logout/` | Déconnexion | ✅ Utilisé |
| POST | `/check-email/` | Vérifier si email existe | ✅ Utilisé |
| POST | `/password-reminder/` | Rappel mot de passe | ✅ Utilisé |
| POST | `/password-reset/` | Demande réinitialisation | ✅ Utilisé |
| POST | `/password-reset/confirm/` | Confirmer réinitialisation | ✅ Utilisé |
| POST | `/token/refresh/` | Rafraîchir token JWT | ✅ Utilisé |
| POST | `/verify-email/` | Vérifier email | ✅ Utilisé |
| POST | `/resend-verification/` | Renvoyer email vérification | ✅ Utilisé |

### Endpoints Person (`/api/v2/civicperson/person/`)
- Gestion des profils Person
- Relations entre personnes
- Documents et validations

## 📝 3. PillarScan (`pillar_scan`)

**Description**: Application principale pour les expressions citoyennes.

### Endpoints PillarScan (`/api/v2/pillarscan/`)

| Méthode | Endpoint | Description | Statut |
|---------|----------|-------------|---------|
| GET | `/profiles/me/` | Mon profil PillarScan | ✅ Utilisé |
| GET | `/profiles/{id}/` | Profil par ID | ✅ Utilisé |
| POST | `/profiles/` | Créer/MAJ profil | ✅ Utilisé |
| GET | `/expressions/` | Liste expressions publiques | ✅ Utilisé |
| POST | `/expressions/` | Créer expression | ✅ Utilisé |
| GET | `/expressions/{id}/` | Détail expression | ✅ Utilisé |
| POST | `/expressions/{id}/relate/` | Se relier | ✅ Utilisé |
| DELETE | `/expressions/{id}/relate/` | Se délier | ✅ Utilisé |
| GET | `/expressions/my_expressions/` | Mes expressions | ✅ Utilisé |
| GET | `/expressions/nearby/` | Expressions proches | ⚠️ Mock Geo |
| GET | `/assessments/` | Liste évaluations | ✅ Utilisé |
| POST | `/assessments/` | Créer évaluation | ✅ Utilisé |
| GET | `/assessments/pillars/` | Liste 12 piliers | 📌 TODO |

### Modèles Cassandra
- `PillarScanUserProfile`: Profils utilisateurs avec gamification
- `PillarScanExpression`: Expressions avec géolocalisation
- `PillarScanAssessment`: Évaluations des 12 piliers
- `PillarScanActivityCounter`: Métriques temps réel

## 🔔 3. NotifyCore (`notifycore`)

**Description**: Système de notifications temps réel avec SSE.

### Endpoints Notifications (`/api/notifications/`)

| Méthode | Endpoint | Description | Statut |
|---------|----------|-------------|---------|
| GET | `/api/notifications/` | Liste notifications | ✅ Utilisé |
| GET | `/api/notifications/stream/` | SSE stream temps réel | ✅ Utilisé |
| GET | `/api/notifications/test/` | Déclencher test notif | ✅ Utilisé |
| GET/PUT | `/api/notifications/preferences/` | Préférences notifs | 📌 TODO |
| POST | `/api/notifications/{id}/mark_read/` | Marquer comme lue | ✅ Utilisé |

### Types de notifications
- Nouvelle relation sur expression
- Badge débloqué
- Streak en danger
- Message système

## 📸 4. MediaHub (`mediahub`)

**Description**: Gestion des médias avec MinIO.

### Endpoints Media (`/api/v2/media/`)

| Méthode | Endpoint | Description | Statut |
|---------|----------|-------------|---------|
| POST | `/upload/` | Upload fichiers vers MinIO | ⚠️ Mock Frontend |
| GET | `/media/{id}/` | Récupérer média | 📌 TODO |
| DELETE | `/media/{id}/` | Supprimer média | 📌 TODO |

### Pipeline d'enrichissement
1. Upload vers MinIO
2. Génération thumbnails
3. Extraction métadonnées
4. Détection NSFW
5. OCR (images)
6. Détection objets

## 🌍 5. Geography (`geography`)

**Description**: Données géographiques et géolocalisation.

### Endpoints Geography (`/api/geography/`)
- Pays, régions, départements, municipalités
- Géocodage inverse
- Calculs de distance

## 🗳️ 6. CivicPoll (`civicpoll`)

**Description**: Système de sondages et votes.

### Endpoints (intégrés dans PillarScan)
- Création de sondages liés aux expressions
- Votes et résultats

## 🔄 État des connexions Frontend-Backend

### ✅ Connexions fonctionnelles
1. **Authentification JWT complète**
   - Login/Logout
   - Refresh token
   - Gestion profil

2. **CRUD Expressions**
   - Création, lecture, liste
   - Système de "relate"
   - Filtres par mood

3. **Profils PillarScan**
   - Création automatique
   - Avatar et gamification
   - Stats et badges

4. **Notifications SSE**
   - Stream temps réel
   - Events côté serveur

### ⚠️ Utilisant encore des mocks
1. **Upload médias**
   - Mock dans `/api/upload/route.ts`
   - À remplacer par MediaHub Django

2. **Expressions avec médias**
   - `mockGetExpressionsWithMedia`
   - `mockCreateExpressionWithMedia`

3. **Géolocalisation**
   - `MockGeotaggingService` côté Django
   - Nearby expressions pas fonctionnel

### 📌 TODO - Non implémentés
1. **Statistiques**
   - `getExpressionStats()` retourne vide
   
2. **Piliers**
   - `getPillars()` utilise données statiques
   
3. **Préférences notifications**
   - Endpoint existe mais pas utilisé

## 🔧 Configuration requise

### Variables d'environnement Frontend
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### Headers requis
```typescript
headers: {
  'Authorization': `Bearer ${token}`,
  'X-Country-Code': 'FR', // Pour routing Cassandra
  'Content-Type': 'application/json'
}
```

### Authentification
- JWT avec access token (expire 60min)
- Refresh token (expire 7 jours)
- Auto-refresh dans AuthContext

## 📊 Résumé

**Applications utilisées**: 6
**Endpoints totaux**: ~35
**Endpoints connectés**: ~25 (71%)
**Endpoints mockés**: ~5 (14%)
**Endpoints TODO**: ~5 (14%)

La majorité du backend est fonctionnel et connecté. Les priorités sont :
1. Remplacer les mocks média
2. Implémenter la géolocalisation réelle
3. Connecter les statistiques