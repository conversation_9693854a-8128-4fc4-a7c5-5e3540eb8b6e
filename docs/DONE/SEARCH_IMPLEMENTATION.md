# Implémentation de la recherche d'expressions

## Date: 2025-01-23

## Résumé

J'ai implémenté la fonctionnalité de recherche dans le composant ExpressionFeed, permettant aux utilisateurs de rechercher des expressions par texte.

## Modifications apportées

### 1. API Client (`lib/api/client.ts`)
- Ajouté le paramètre `search` dans la méthode `getExpressions()`
- Le paramètre est maintenant transmis au backend via la query string

### 2. Composant ExpressionFeed (`components/feed/ExpressionFeed.tsx`)
- Ajouté l'état `searchQuery` pour stocker la requête de recherche
- Implémenté un débounce de 500ms avec `useDebounce` pour éviter les requêtes excessives
- Ajouté un champ de recherche avec :
  - Icône de recherche
  - Bouton de suppression (X) quand il y a du texte
  - Indicateur de chargement pendant la recherche
- La recherche déclenche automatiquement le rechargement des expressions
- La pagination est réinitialisée lors d'une nouvelle recherche
- Message d'état vide spécifique pour les recherches sans résultats

### 3. Tests (`__tests__/components/feed/ExpressionFeed.test.tsx`)
- Ajouté une suite complète de tests pour la fonctionnalité de recherche :
  - Affichage/masquage du champ de recherche
  - Déclenchement de la recherche lors de la saisie
  - Bouton de suppression
  - État vide spécifique à la recherche
  - Réinitialisation de la pagination

## Utilisation

1. Le champ de recherche apparaît en haut du feed d'expressions
2. Tapez votre recherche - elle se déclenchera automatiquement après 500ms
3. Cliquez sur le X pour effacer la recherche
4. Les résultats se mettent à jour en temps réel

## Points d'attention

- Le backend doit supporter le paramètre `search` dans l'endpoint `/api/v2/pillarscan/expressions/`
- La recherche doit idéalement chercher dans le contenu des expressions et potentiellement dans les tags
- Pour l'instant, le tri côté backend n'est pas implémenté (TODO dans le code)

## Prochaines étapes possibles

1. Ajouter des filtres de recherche avancés (par date, par pilier, etc.)
2. Implémenter la recherche floue (fuzzy search)
3. Ajouter l'historique des recherches récentes
4. Optimiser les performances avec la mise en cache des résultats