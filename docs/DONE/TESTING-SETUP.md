# Configuration du système de tests automatisés

## Vue d'ensemble

Le projet PillarScan dispose d'un système de tests complet avec :
- Tests unitaires et d'intégration avec Jest et React Testing Library
- Rapports de couverture de code
- Rapports HTML visuels
- Hooks pre-commit automatiques
- Intégration continue avec GitHub Actions

## Dépendances installées

### Tests
- `jest` - Framework de test JavaScript
- `@testing-library/react` - Utilitaires de test pour React
- `@testing-library/jest-dom` - Matchers personnalisés pour Jest
- `@testing-library/user-event` - Simulation d'interactions utilisateur
- `jest-environment-jsdom` - Environnement DOM pour Jest

### Rapports
- `jest-html-reporter` - Génération de rapports HTML visuels
- Couverture native de Jest (lcov, html, text, json)

### Automatisation
- `husky` - Hooks Git
- `lint-staged` - Exécution de scripts sur les fichiers staged
- `prettier` - Formatage de code

## Structure des tests

```
smatflow-pillarscan-nextjs/
├── __tests__/
│   ├── components/
│   │   └── ui/
│   │       └── Button.test.tsx
│   ├── contexts/
│   │   └── AuthContext.test.tsx
│   ├── hooks/
│   │   └── useDebounce.test.tsx
│   └── pages/
│       └── HomePage.test.tsx
├── jest.config.js
├── jest.setup.js
├── coverage/              # Rapports de couverture (gitignored)
└── test-reports/          # Rapports HTML (gitignored)
```

## Scripts NPM

### `npm test`
Lance les tests en mode watch pour le développement. Les tests se relancent automatiquement à chaque modification.

### `npm run test:ci`
Exécute tous les tests une fois avec couverture de code. Utilisé pour l'intégration continue.

### `npm run test:report`
Génère un rapport HTML visuel détaillé dans `/test-reports/test-report.html`.

### `npm run test:watch`
Mode développement avec couverture de code en temps réel.

## Configuration Jest

Le fichier `jest.config.js` configure :
- Environnement jsdom pour les tests React
- Alias de chemin `@/` vers `src/`
- Seuils de couverture à 70%
- Génération de rapports multiples
- Exclusion des fichiers de configuration et stories

## Hooks Git

### Pre-commit
Automatiquement configuré avec Husky, exécute :
1. ESLint sur les fichiers TypeScript/JavaScript
2. Tests Jest sur les fichiers modifiés
3. Prettier sur les fichiers JSON, CSS, Markdown

## GitHub Actions

Le workflow `.github/workflows/test.yml` :
- Teste sur Node.js 18.x et 20.x
- Exécute les linters
- Lance les tests avec couverture
- Upload les résultats vers Codecov
- Vérifie que le build fonctionne
- Vérifie les types TypeScript

## Utilisation

### Écrire un nouveau test

1. Créer un fichier `.test.tsx` ou `.test.ts` dans `__tests__`
2. Importer les utilitaires nécessaires :
```typescript
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
```

3. Écrire les tests :
```typescript
describe('Component', () => {
  it('should render correctly', () => {
    render(<Component />);
    expect(screen.getByText('Hello')).toBeInTheDocument();
  });
});
```

### Mocks courants

Les mocks suivants sont configurés dans `jest.setup.js` :
- `next/router`
- `next/image`
- `IntersectionObserver`
- `framer-motion` (dans les tests individuels)

### Tableau de bord des tests

En développement, accédez à `/test-dashboard` pour :
- Voir les commandes disponibles
- Accéder aux rapports de couverture
- Consulter les rapports HTML

## Bonnes pratiques

1. **Nommage** : Utilisez des descriptions claires et en français
2. **Organisation** : Groupez les tests avec `describe`
3. **Isolation** : Chaque test doit être indépendant
4. **Assertions** : Utilisez les matchers appropriés de `@testing-library/jest-dom`
5. **Mocks** : Mockez les dépendances externes et les API
6. **Coverage** : Visez une couverture > 80% pour les composants critiques

## Dépannage

### Les tests échouent avec "Cannot find module"
Vérifiez que les alias de chemin sont correctement configurés dans `jest.config.js`.

### Les tests sont lents
- Utilisez `npm run test:ci` avec `--maxWorkers=2`
- Évitez les `waitFor` inutiles
- Mockez les opérations asynchrones

### Les hooks pre-commit ne fonctionnent pas
Réinitialisez Husky : `npx husky init`

## Prochaines étapes

1. Ajouter des tests E2E avec Playwright
2. Intégrer Storybook pour les tests visuels
3. Configurer des tests de performance
4. Ajouter des tests d'accessibilité automatisés