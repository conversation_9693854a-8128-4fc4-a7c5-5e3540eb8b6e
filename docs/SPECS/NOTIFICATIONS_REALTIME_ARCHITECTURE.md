# Architecture Notifications Temps Réel - PillarScan

## Vue d'ensemble

Implémentation d'un système de notifications temps réel pour PillarScan, s'intégrant avec l'infrastructure existante de `notifycore` et `dashboard`.

## 1. Choix technologique : SSE (Server-Sent Events)

### Pourquoi SSE plutôt que WebSocket ?

#### Avantages SSE :
- ✅ **Simplicité** : Utilise HTTP standard, pas de protocole spécial
- ✅ **Compatibilité** : Fonctionne avec les proxies et firewalls existants
- ✅ **Reconnexion automatique** : Le navigateur gère la reconnexion
- ✅ **Unidirectionnel** : Parfait pour les notifications (serveur → client)
- ✅ **Léger** : Moins de surcharge que WebSocket
- ✅ **RESTful** : S'intègre naturellement avec l'API REST

#### Cas d'usage PillarScan :
- Notifications de nouvelles expressions
- Alertes de "relate" sur vos expressions
- Notifications système
- Mises à jour de badges/achievements

## 2. Architecture proposée

### Backend (Django)
```
Django Backend
    ↓
NotifyCore (existant)
    ↓
SSE Endpoint (/api/v2/notifications/stream/)
    ↓
Redis Pub/Sub
    ↓
Client SSE
```

### Frontend (Next.js)
```
NotificationService (SSE Client)
    ↓
NotificationStore (État global)
    ↓
NotificationCenter (UI)
    ↓
Toast/Badge/Alert Components
```

## 3. Implémentation Frontend

### A. Service de notifications
```typescript
// services/NotificationService.ts
class NotificationService {
  private eventSource: EventSource | null = null;
  private listeners: Map<string, Set<Function>> = new Map();
  private reconnectTimer: NodeJS.Timeout | null = null;
  
  connect(token: string) {
    const url = `${API_URL}/api/v2/notifications/stream/`;
    this.eventSource = new EventSource(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    this.eventSource.onmessage = this.handleMessage;
    this.eventSource.onerror = this.handleError;
    this.eventSource.onopen = this.handleOpen;
  }
  
  subscribe(type: string, callback: Function) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    this.listeners.get(type)!.add(callback);
  }
  
  private handleMessage = (event: MessageEvent) => {
    const notification = JSON.parse(event.data);
    this.emit(notification.type, notification);
  }
}
```

### B. Store de notifications (Zustand)
```typescript
// stores/notificationStore.ts
interface NotificationStore {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  
  addNotification: (notification: Notification) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  dismiss: (id: string) => void;
}
```

### C. Composant NotificationCenter
```typescript
// components/NotificationCenter.tsx
export function NotificationCenter() {
  const { notifications, unreadCount } = useNotificationStore();
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className="relative">
      <Button onClick={() => setIsOpen(!isOpen)}>
        <Bell className="w-5 h-5" />
        {unreadCount > 0 && (
          <Badge className="absolute -top-1 -right-1">
            {unreadCount}
          </Badge>
        )}
      </Button>
      
      {isOpen && (
        <NotificationDropdown 
          notifications={notifications}
          onClose={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
```

## 4. Types de notifications

### Pour PillarScan
```typescript
type NotificationType = 
  | 'expression.new_relate'      // Quelqu'un a "relaté" votre expression
  | 'expression.milestone'       // Votre expression atteint X relates
  | 'expression.reply'          // Réponse à votre expression
  | 'badge.earned'              // Nouveau badge obtenu
  | 'streak.reminder'           // Rappel pour maintenir votre streak
  | 'pillar.trending'           // Un pilier est en tendance
  | 'system.announcement'       // Annonce système
  | 'social.follow'             // Nouveau follower
```

## 5. Intégration avec l'existant

### Backend Django
```python
# pillarscan/notifications.py
from notifycore.services.notification_service import NotificationService
from dashboard.services.notification_service import NotificationService as DashboardNotificationService

class PillarScanNotificationService:
    @staticmethod
    def notify_new_relate(expression, user_who_related):
        # Créer notification dans notifycore
        notification = NotificationService.create_notification(
            recipient_id=expression.person_id,
            notification_type='expression.new_relate',
            title=f'{user_who_related.nickname} a relaté votre expression',
            content=f'"{expression.text[:50]}..."',
            context_data={
                'expression_id': str(expression.expression_id),
                'related_by': str(user_who_related.person_id)
            }
        )
        
        # Créer aussi dans dashboard pour l'UI
        DashboardNotificationService.create_notification(
            user_id=expression.person_id,
            notification_type='INFO',
            title='Nouveau relate',
            message=f'{user_who_related.nickname} a relaté votre expression',
            source_app='pillarscan',
            icon='💙'
        )
        
        # Publier dans Redis pour SSE
        redis_client.publish(
            f'notifications:{expression.person_id}',
            notification.to_json()
        )
```

## 6. Optimisations

### A. Regroupement des notifications
- Agrégation des relates multiples
- Batching des notifications système
- Dédoublonnage des notifications similaires

### B. Performance
- Pagination des notifications historiques
- Cache des notifications récentes
- Compression SSE avec gzip

### C. Fiabilité
- Reconnexion automatique SSE
- Fallback sur polling si SSE échoue
- Persistance locale des notifications non lues

## 7. Sécurité

### A. Authentification
- Token JWT dans les headers SSE
- Validation des permissions par type de notification
- Isolation par utilisateur

### B. Rate limiting
- Limite de connexions SSE par utilisateur
- Throttling des notifications par type
- Protection contre le spam

## 8. UI/UX

### A. Types d'affichage
1. **Badge** : Compteur sur l'icône cloche
2. **Toast** : Notification temporaire en bas
3. **Dropdown** : Liste des notifications
4. **Modal** : Pour les notifications importantes

### B. Actions
- Marquer comme lu
- Dismiss (masquer)
- Action directe (aller à l'expression)
- Réglages de notifications

### C. Préférences utilisateur
- Types de notifications à recevoir
- Mode silencieux
- Notifications push (PWA future)

## 9. Plan d'implémentation

### Phase 1 : Infrastructure de base
1. Service SSE frontend
2. Store de notifications
3. Composant NotificationCenter basique

### Phase 2 : Intégration
1. Connexion avec l'API backend
2. Types de notifications PillarScan
3. Actions et interactions

### Phase 3 : Optimisations
1. Performance et cache
2. Préférences utilisateur
3. Analytics des notifications

## Conclusion

Cette architecture permet d'ajouter des notifications temps réel à PillarScan tout en s'intégrant harmonieusement avec l'infrastructure existante. SSE offre la simplicité et la fiabilité nécessaires pour une excellente expérience utilisateur.