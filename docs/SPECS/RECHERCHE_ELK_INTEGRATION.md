# 🔍 SPÉCIFICATION : INTÉGRATION ELASTICSEARCH (ELK)

> Recherche avancée des expressions avec respect de la souveraineté des données
> Der<PERSON><PERSON> mise à jour : 24 mai 2025

## 🎯 OBJECTIF

Implémenter une recherche par mots-clés performante en utilisant Elasticsearch tout en respectant STRICTEMENT le périmètre pays.

## 📊 ARCHITECTURE

### Configuration Existante

```python
# Django settings.py
ELASTICSEARCH_URL_INTERNAL = "https://192.168.50.8:9200"
ELASTICSEARCH_URL_EXTERNAL = "https://elk.etiolles.smatflow.net:65021"
ELASTICSEARCH_USERNAME = "elastic"
ELASTICSEARCH_PASSWORD = "q2WE4p6mPC=AXl5u9ORR"
```

### Index Structure

```json
{
  "pillarscan_expressions": {
    "mappings": {
      "properties": {
        "id": { "type": "keyword" },
        "country": { "type": "keyword" }, // CRITIQUE: pour filtrage
        "content": { 
          "type": "text",
          "analyzer": "standard",
          "fields": {
            "keyword": { "type": "keyword" }
          }
        },
        "mood": { "type": "keyword" },
        "pillar": {
          "properties": {
            "domain": { "type": "keyword" },
            "category": { "type": "keyword" },
            "subcategory": { "type": "keyword" },
            "topic": { "type": "keyword" }
          }
        },
        "mentioned_persons": {
          "type": "nested",
          "properties": {
            "id": { "type": "keyword" },
            "name": { "type": "text" },
            "type": { "type": "keyword" }
          }
        },
        "location": {
          "properties": {
            "city": { "type": "text" },
            "region": { "type": "text" },
            "coordinates": { "type": "geo_point" }
          }
        },
        "created_at": { "type": "date" },
        "engagement": {
          "properties": {
            "relates": { "type": "integer" },
            "impact_score": { "type": "float" }
          }
        },
        "user": {
          "properties": {
            "id": { "type": "keyword" },
            "is_anonymous": { "type": "boolean" }
          }
        }
      }
    },
    "settings": {
      "number_of_shards": 5,
      "number_of_replicas": 1,
      "analysis": {
        "analyzer": {
          "multilingual": {
            "tokenizer": "standard",
            "filter": ["lowercase", "asciifolding"]
          }
        }
      }
    }
  }
}
```

## 🔧 IMPLÉMENTATION BACKEND

### 1. Service d'Indexation

```python
# pillar_scan/services/elasticsearch_service.py
from elasticsearch import Elasticsearch
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class PillarScanElasticsearchService:
    def __init__(self):
        self.es = settings.ELASTICSEARCH_CLIENT
        self.index_name = "pillarscan_expressions"
    
    def index_expression(self, expression):
        """Index une expression dans Elasticsearch"""
        doc = {
            "id": str(expression.id),
            "country": expression.country,  # CRITIQUE
            "content": expression.content,
            "mood": expression.mood,
            "pillar": {
                "domain": expression.pillar_domain,
                "category": expression.pillar_category,
                "subcategory": expression.pillar_subcategory,
                "topic": expression.pillar_topic
            },
            "mentioned_persons": [
                {
                    "id": str(person_id),
                    "name": person_data.get("name"),
                    "type": person_data.get("type")
                }
                for person_id, person_data in expression.mentioned_persons.items()
            ] if expression.mentioned_persons else [],
            "location": {
                "city": expression.city,
                "region": expression.region,
                "coordinates": {
                    "lat": expression.latitude,
                    "lon": expression.longitude
                } if expression.latitude and expression.longitude else None
            },
            "created_at": expression.created_at.isoformat(),
            "engagement": {
                "relates": expression.relates_count,
                "impact_score": expression.impact_score
            },
            "user": {
                "id": str(expression.user_id),
                "is_anonymous": expression.is_anonymous
            }
        }
        
        try:
            self.es.index(
                index=self.index_name,
                id=str(expression.id),
                body=doc
            )
            logger.info(f"Expression {expression.id} indexed successfully")
        except Exception as e:
            logger.error(f"Failed to index expression {expression.id}: {e}")
    
    def search_expressions(self, query, country, filters=None, size=50, from_=0):
        """
        Recherche des expressions avec filtrage OBLIGATOIRE par pays
        """
        if not country:
            raise ValueError("Country is required for search")
        
        # Construction de la requête avec filtre pays OBLIGATOIRE
        search_body = {
            "size": size,
            "from": from_,
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "country": country  # FILTRE OBLIGATOIRE
                            }
                        }
                    ],
                    "should": []
                }
            },
            "highlight": {
                "fields": {
                    "content": {
                        "fragment_size": 150,
                        "number_of_fragments": 1
                    }
                }
            },
            "sort": [
                {"_score": "desc"},
                {"created_at": "desc"}
            ]
        }
        
        # Ajout de la recherche textuelle si query fourni
        if query:
            search_body["query"]["bool"]["should"].extend([
                {
                    "match": {
                        "content": {
                            "query": query,
                            "boost": 2
                        }
                    }
                },
                {
                    "nested": {
                        "path": "mentioned_persons",
                        "query": {
                            "match": {
                                "mentioned_persons.name": query
                            }
                        }
                    }
                },
                {
                    "match": {
                        "location.city": query
                    }
                }
            ])
            search_body["query"]["bool"]["minimum_should_match"] = 1
        
        # Ajout des filtres additionnels
        if filters:
            if filters.get("mood"):
                search_body["query"]["bool"]["must"].append({
                    "term": {"mood": filters["mood"]}
                })
            
            if filters.get("pillar_domain"):
                search_body["query"]["bool"]["must"].append({
                    "term": {"pillar.domain": filters["pillar_domain"]}
                })
            
            if filters.get("date_from"):
                search_body["query"]["bool"]["must"].append({
                    "range": {
                        "created_at": {
                            "gte": filters["date_from"]
                        }
                    }
                })
        
        try:
            response = self.es.search(
                index=self.index_name,
                body=search_body
            )
            
            return {
                "total": response["hits"]["total"]["value"],
                "results": [
                    {
                        **hit["_source"],
                        "highlight": hit.get("highlight", {}).get("content", [None])[0],
                        "score": hit["_score"]
                    }
                    for hit in response["hits"]["hits"]
                ]
            }
        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise
    
    def delete_expression(self, expression_id):
        """Supprime une expression de l'index"""
        try:
            self.es.delete(
                index=self.index_name,
                id=str(expression_id)
            )
        except Exception as e:
            logger.error(f"Failed to delete expression {expression_id}: {e}")
```

### 2. Signal pour Indexation Automatique

```python
# pillar_scan/signals.py
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import PillarScanExpression
from .services.elasticsearch_service import PillarScanElasticsearchService

@receiver(post_save, sender=PillarScanExpression)
def index_expression_on_save(sender, instance, created, **kwargs):
    """Index automatiquement les expressions dans Elasticsearch"""
    if created or kwargs.get('update_fields'):
        service = PillarScanElasticsearchService()
        service.index_expression(instance)

@receiver(post_delete, sender=PillarScanExpression)
def remove_expression_on_delete(sender, instance, **kwargs):
    """Supprime l'expression de l'index lors de la suppression"""
    service = PillarScanElasticsearchService()
    service.delete_expression(instance.id)
```

### 3. Endpoint de Recherche

```python
# pillar_scan/views.py
class ExpressionSearchView(APIView):
    permission_classes = [AllowAny]  # Mais filtre par pays obligatoire
    
    def get(self, request):
        # Récupération du pays OBLIGATOIRE
        country = request.headers.get('X-Country-Code')
        if not country:
            return Response(
                {"error": "Country header X-Country-Code is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Paramètres de recherche
        query = request.query_params.get('q', '')
        mood = request.query_params.get('mood')
        pillar_domain = request.query_params.get('pillar')
        date_from = request.query_params.get('date_from')
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        
        # Filters
        filters = {}
        if mood:
            filters['mood'] = mood
        if pillar_domain:
            filters['pillar_domain'] = pillar_domain
        if date_from:
            filters['date_from'] = date_from
        
        # Recherche
        service = PillarScanElasticsearchService()
        try:
            results = service.search_expressions(
                query=query,
                country=country,
                filters=filters,
                size=page_size,
                from_=(page - 1) * page_size
            )
            
            return Response({
                "total": results["total"],
                "page": page,
                "page_size": page_size,
                "results": results["results"]
            })
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
```

## 🎨 IMPLÉMENTATION FRONTEND

### 1. Hook de Recherche

```typescript
// hooks/useExpressionSearch.ts
export function useExpressionSearch() {
  const { country } = useCountry();
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({});
  
  const searchQuery = useQuery({
    queryKey: ['expression-search', country, searchTerm, filters],
    queryFn: async () => {
      if (!country) throw new Error('Country not set');
      
      const params = new URLSearchParams({
        q: searchTerm,
        ...filters
      });
      
      const response = await fetch(
        `/api/v2/pillarscan/expressions/search?${params}`,
        {
          headers: {
            'X-Country-Code': country
          }
        }
      );
      
      if (!response.ok) throw new Error('Search failed');
      return response.json();
    },
    enabled: !!country && (!!searchTerm || Object.keys(filters).length > 0),
    staleTime: 30000, // Cache 30 secondes
  });
  
  return {
    searchTerm,
    setSearchTerm,
    filters,
    setFilters,
    results: searchQuery.data,
    isLoading: searchQuery.isLoading,
    error: searchQuery.error
  };
}
```

### 2. Composant de Recherche

```typescript
// components/ExpressionSearch.tsx
export function ExpressionSearch() {
  const { 
    searchTerm, 
    setSearchTerm, 
    results, 
    isLoading,
    filters,
    setFilters
  } = useExpressionSearch();
  
  const [showFilters, setShowFilters] = useState(false);
  
  return (
    <div className="w-full">
      <div className="relative">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Rechercher des expressions..."
          className="w-full pl-10 pr-4 py-2 border rounded-lg"
        />
        <Search className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" />
        
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="absolute right-3 top-2.5"
        >
          <Filter className="w-5 h-5" />
        </button>
      </div>
      
      {showFilters && (
        <SearchFilters 
          filters={filters}
          onChange={setFilters}
        />
      )}
      
      {isLoading && <SearchSkeleton />}
      
      {results && (
        <div className="mt-4 space-y-3">
          <p className="text-sm text-gray-600">
            {results.total} résultats trouvés
          </p>
          
          {results.results.map((expression) => (
            <SearchResultCard
              key={expression.id}
              expression={expression}
              highlight={expression.highlight}
            />
          ))}
        </div>
      )}
    </div>
  );
}
```

### 3. Carte de Résultat avec Highlight

```typescript
// components/SearchResultCard.tsx
export function SearchResultCard({ 
  expression, 
  highlight 
}: SearchResultProps) {
  return (
    <div className="p-4 border rounded-lg hover:shadow-md transition-shadow">
      <div className="flex items-start gap-3">
        <MoodEmoji mood={expression.mood} size="lg" />
        
        <div className="flex-1">
          {highlight ? (
            <p 
              className="text-gray-800"
              dangerouslySetInnerHTML={{ 
                __html: highlight 
              }}
            />
          ) : (
            <p className="text-gray-800">
              {expression.content}
            </p>
          )}
          
          <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
            <span>{expression.location.city}</span>
            <span>{formatRelativeTime(expression.created_at)}</span>
            <span>{expression.engagement.relates} relates</span>
          </div>
          
          {expression.mentioned_persons.length > 0 && (
            <div className="flex gap-2 mt-2">
              {expression.mentioned_persons.map(person => (
                <PersonChip 
                  key={person.id}
                  person={person}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
```

## 🚀 OPTIMISATIONS

### 1. Indexation en Temps Réel
- Utiliser Celery pour indexation asynchrone
- Batch indexing pour imports massifs
- Update partiel pour modifications mineures

### 2. Cache de Recherche
- Redis pour cacher les recherches fréquentes
- Invalidation intelligente par pays
- Pré-chargement des suggestions

### 3. Performance
- Sharding par pays dans Elasticsearch
- Replicas par région géographique
- Query optimization avec analyzers spécifiques

## ⚠️ SÉCURITÉ

1. **Validation du pays** à chaque requête
2. **Pas d'accès inter-pays** même pour les admins
3. **Audit logs** pour toutes les recherches
4. **Rate limiting** par IP/utilisateur

## 📊 MÉTRIQUES

- Temps de réponse moyen < 100ms
- Pertinence des résultats > 85%
- Taux d'utilisation de la recherche > 40%
- Recherches sans résultats < 10%