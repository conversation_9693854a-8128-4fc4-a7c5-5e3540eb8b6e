# API Endpoints Documentation

## Authentication Endpoints

### Main JWT Token Endpoints (Django Rest Framework Simple JWT)

- **POST** `/api/token/` - Obtain JWT tokens (login)

  - Body: `{ "email": "<EMAIL>", "password": "password123" }`
  - Returns: `{ "access": "...", "refresh": "..." }`

- **POST** `/api/token/refresh/` - Refresh access token
  - Body: `{ "refresh": "refresh_token_here" }`
  - Returns: `{ "access": "new_access_token" }`

### CivicPerson v2 Auth Endpoints

- **POST** `/api/v2/civicperson/auth/login/` - Login endpoint
- **POST** `/api/v2/civicperson/auth/register/` - Register new user
- **GET** `/api/v2/civicperson/auth/profile/` - Get user profile
- **POST** `/api/v2/civicperson/auth/logout/` - Logout
- **POST** `/api/v2/civicperson/auth/check-email/` - Check if email exists
- **POST** `/api/v2/civicperson/auth/password-reminder/` - Send password reminder
- **POST** `/api/v2/civicperson/auth/password-reset/` - Request password reset
- **POST** `/api/v2/civicperson/auth/password-reset/confirm/` - Confirm password reset
- **POST** `/api/v2/civicperson/auth/token/refresh/` - Refresh token (alternative)
- **POST** `/api/v2/civicperson/auth/verify-email/` - Verify email address
- **POST** `/api/v2/civicperson/auth/resend-verification/` - Resend verification email

## PillarScan Endpoints

### Expressions

- **GET** `/api/v2/pillarscan/expressions/` - List expressions
- **POST** `/api/v2/pillarscan/expressions/` - Create expression
  - Body: `{ "mood": "happy|frustrated|idea|question", "text": "...", "visibility_level": "public|private|anonymous", "location": {...} }`
- **GET** `/api/v2/pillarscan/expressions/{id}/` - Get expression detail
- **PUT** `/api/v2/pillarscan/expressions/{id}/` - Update expression
- **DELETE** `/api/v2/pillarscan/expressions/{id}/` - Delete expression
- **POST** `/api/v2/pillarscan/expressions/{id}/upload_media/` - Upload media for expression
- **POST** `/api/v2/pillarscan/expressions/{id}/relate/` - Toggle relate on expression
- **GET** `/api/v2/pillarscan/expressions/nearby/` - Get nearby expressions (requires lat/lng params)

### Categories

- **GET** `/api/v2/pillarscan/categories/` - List categories
- **GET** `/api/v2/pillarscan/categories/{id}/` - Get category detail

### Pillars

- **GET** `/api/v2/pillarscan/pillars/` - List pillars
- **GET** `/api/v2/pillarscan/pillars/{id}/` - Get pillar detail

## Media Endpoints

### Generic Media Upload

- **POST** `/api/v2/media/upload/` - Generic media upload endpoint
  - Form data: `file`, `media_type`, `entity_type`, `entity_id`, `title`, `description`
  - Returns: `{ "media_id": "...", "url": "...", "bucket": "..." }`

### MediaHub Endpoints

- **GET** `/api/mediahub/media/` - List media
- **GET** `/api/mediahub/media/{id}/` - Get media detail

## Notification Endpoints

### Server-Sent Events (SSE)

- **GET** `/api/notifications/notifications/stream/` - SSE stream for real-time notifications
  - Headers: `Authorization: Bearer {access_token}`
  - Returns: Server-sent events stream
- **GET** `/api/notifications/notifications/test/` - Trigger test notification

### Regular Notifications

- **GET** `/api/notifications/list/` - List notifications
- **POST** `/api/notifications/mark-read/{id}/` - Mark notification as read

## Test Credentials

- Email: `<EMAIL>`
- Password: `Smatflow@2024`

## Headers Required

All authenticated endpoints require:

```
Authorization: Bearer {access_token}
```

## Content Types

- JSON endpoints: `Content-Type: application/json`
- File upload endpoints: `Content-Type: multipart/form-data`
