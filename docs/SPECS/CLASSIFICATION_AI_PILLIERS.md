# 🧠 SPÉCIFICATION : CLASSIFICATION AI DES EXPRESSIONS PAR PILIERS

> Classification hiérarchique des expressions citoyennes avec Amazon Comprehend
> Dernière mise à jour : 24 mai 2025

## 🎯 VISION

Transformer chaque expression citoyenne en **donnée structurée** en la classifiant automatiquement dans la hiérarchie des 12 piliers du développement humain, permettant ainsi l'émergence de patterns et insights à l'échelle planétaire.

## 📊 HIÉRARCHIE DE CLASSIFICATION

### Structure à 4 niveaux

```yaml
1. DOMAIN (Domaine des piliers):
   - BIEN_ETRE: Bien-être émotionnel et santé
   - EDUCATION_ECONOMIE: Éducation et économie
   - ENVIRONNEMENT_SOCIAL: Environnement et relations
   - GOUVERNANCE_LIBERTE: Gouvernance et libertés
   - ACCOMPLISSEMENT_SERVICES: Accomplissement et services

2. CATEGORY (Pilier - 12 au total):
   BIEN_ETRE:
     - BIEN_ETRE_EMOTIONNEL: Bien-être émotionnel
     - SANTE_PHYSIQUE: Santé physique
   
   EDUCATION_ECONOMIE:
     - EDUCATION_COMPETENCES: Éducation et compétences
     - SECURITE_ECONOMIQUE: Sécurité économique
   
   ENVIRONNEMENT_SOCIAL:
     - ENVIRONNEMENT_SAIN: Environnement sain
     - RELATIONS_SOCIALES: Relations sociales
   
   GOUVERNANCE_LIBERTE:
     - GOUVERNANCE_EQUITABLE: Gouvernance équitable
     - IDENTITE_CULTURELLE: Identité culturelle
     - LIBERTE_PERSONNELLE: Liberté personnelle
   
   ACCOMPLISSEMENT_SERVICES:
     - ACCOMPLISSEMENT_PERSONNEL: Accomplissement personnel
     - EQUILIBRE_VIE_PRO: Équilibre vie professionnelle
     - ACCES_SERVICES_ESSENTIELS: Accès aux services essentiels

3. SUBCATEGORY (Sous-pilier):
   Exemple pour SANTE_PHYSIQUE:
     - ACCES_SOINS: Accès aux soins
     - PREVENTION_SANTE: Prévention santé
     - NUTRITION_ALIMENTATION: Nutrition et alimentation
     - ACTIVITE_PHYSIQUE: Activité physique
     - SANTE_MENTALE_PHYSIQUE: Lien santé mentale-physique

4. TOPIC (Sujet spécifique):
   Exemple pour ACCES_SOINS:
     - HOPITAUX_CLINIQUES: Infrastructure hospitalière
     - MEDECINS_SPECIALISTES: Accès aux spécialistes
     - MEDICAMENTS_PHARMACIE: Accès aux médicaments
     - URGENCES_MEDICALES: Services d'urgence
     - SOINS_DOMICILE: Soins à domicile
```

### Sentiment (5ème dimension)
```yaml
SENTIMENT:
  - POSITIF: Expression de satisfaction
  - NEGATIF: Expression de frustration
  - NEUTRE: Constat factuel
  - MIXTE: Sentiment ambivalent
  
INTENSITE: 0.0 à 1.0
```

## 🔧 ARCHITECTURE TECHNIQUE

### Modèles Django Existants

```python
# civicstore/models/civic_expression.py

class ExpressionCategory(DjangoCassandraModel):
    """Représente les DOMAINS et CATEGORIES (piliers)"""
    category_id = columns.UUID(primary_key=True)
    parent_category_id = columns.UUID()  # NULL pour domains, UUID pour subcategories
    category_code = columns.Text(index=True)  # Ex: "SANTE_PHYSIQUE"
    parent_category_code = columns.Text()  # Ex: "BIEN_ETRE" pour le domain
    name = columns.Text()  # Nom lisible
    scope = columns.Text(default='ECONOMY-SOCIAL')
    description = columns.Text()
    
class ExpressionTopic(DjangoCassandraModel):
    """Représente les TOPICS (sujets spécifiques)"""
    topic_id = columns.UUID(primary_key=True)
    subcategory_id = columns.UUID(index=True)  # Lien vers ExpressionCategory
    topic_code = columns.Text(index=True)  # Ex: "HOPITAUX_CLINIQUES"
    subcategory_code = columns.Text()  # Ex: "ACCES_SOINS"
    name = columns.Text()
    description = columns.Text()

class ExpressionImputationPath(DjangoCassandraModel):
    """Stocke le chemin complet de classification"""
    expression_id = columns.UUID(partition_key=True)
    category_code = columns.Text(primary_key=True)
    subcategory_code = columns.Text(primary_key=True)
    topic_code = columns.Text(primary_key=True)
    expression_type_code = columns.Text(primary_key=True)
    
    # Scores de confiance pour chaque niveau
    category_confidence = columns.Float()
    subcategory_confidence = columns.Float()
    topic_confidence = columns.Float()
    expression_type_confidence = columns.Float()
    
    # Sentiment
    sentiment_value = columns.Text()
    sentiment_intensity = columns.Float()
```

## 🤖 SERVICE DE CLASSIFICATION AI

### Architecture en 2 passes avec Amazon Comprehend

```python
# pillar_scan/services/ai_classification_service.py

import boto3
from typing import Dict, List, Tuple, Optional
import logging
from django.conf import settings
from civicstore.models import ExpressionCategory, ExpressionTopic
import json

logger = logging.getLogger(__name__)

class PillarScanAIClassificationService:
    """
    Service de classification AI utilisant Amazon Comprehend
    avec modèle custom pour les piliers
    """
    
    def __init__(self):
        self.comprehend = boto3.client(
            'comprehend',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )
        
        # Cache des hiérarchies pour optimisation
        self._category_cache = {}
        self._topic_cache = {}
        self._load_hierarchy_cache()
    
    def _load_hierarchy_cache(self):
        """Charge la hiérarchie complète en cache"""
        # Charger toutes les catégories
        for category in ExpressionCategory.objects.all():
            self._category_cache[category.category_code] = {
                'id': category.category_id,
                'name': category.name,
                'parent_code': category.parent_category_code,
                'description': category.description
            }
        
        # Charger tous les topics
        for topic in ExpressionTopic.objects.all():
            if topic.subcategory_code not in self._topic_cache:
                self._topic_cache[topic.subcategory_code] = []
            
            self._topic_cache[topic.subcategory_code].append({
                'id': topic.topic_id,
                'code': topic.topic_code,
                'name': topic.name,
                'description': topic.description
            })
    
    async def classify_expression(
        self, 
        expression_text: str,
        user_location: Dict[str, str],
        mentioned_persons: List[Dict]
    ) -> Dict:
        """
        Classification complète d'une expression en 2 passes
        
        Args:
            expression_text: Le texte de l'expression
            user_location: {'country': 'FR', 'city': 'Paris', 'region': 'IDF'}
            mentioned_persons: Liste des personnes mentionnées
            
        Returns:
            Dict avec classification complète et scores
        """
        
        # Passe 1 : Classification structurelle + Sentiment
        structure_result = await self._classify_structure(
            expression_text, 
            user_location
        )
        
        # Passe 2 : Extraction d'entités et enrichissement
        entities_result = await self._extract_entities(
            expression_text,
            mentioned_persons
        )
        
        # Fusion des résultats
        classification = self._merge_results(
            structure_result,
            entities_result,
            expression_text
        )
        
        return classification
    
    async def _classify_structure(
        self, 
        text: str, 
        location: Dict
    ) -> Dict:
        """
        Passe 1 : Classification dans la hiérarchie des piliers
        """
        
        # Préparer le texte enrichi avec contexte
        enriched_text = self._enrich_text_with_context(text, location)
        
        try:
            # Appel au modèle custom Amazon Comprehend
            response = self.comprehend.classify_document(
                Text=enriched_text,
                EndpointArn=settings.COMPREHEND_PILLAR_ENDPOINT
            )
            
            # Parser les résultats
            classifications = response['Classes']
            
            # Extraire la hiérarchie complète
            result = self._parse_classification_hierarchy(classifications)
            
            # Analyse de sentiment en parallèle
            sentiment_response = self.comprehend.detect_sentiment(
                Text=text,
                LanguageCode='fr'  # Multi-langue à implémenter
            )
            
            result['sentiment'] = {
                'value': sentiment_response['Sentiment'],
                'scores': sentiment_response['SentimentScore'],
                'intensity': max(sentiment_response['SentimentScore'].values())
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error in structure classification: {e}")
            return self._get_default_classification()
    
    def _parse_classification_hierarchy(self, classifications: List) -> Dict:
        """
        Parse les résultats Comprehend en hiérarchie complète
        """
        # Format attendu des classes Comprehend:
        # "BIEN_ETRE/SANTE_PHYSIQUE/ACCES_SOINS/HOPITAUX_CLINIQUES"
        
        result = {
            'domain': None,
            'category': None,
            'subcategory': None,
            'topic': None,
            'confidence_scores': {}
        }
        
        # Trouver la classification avec le score le plus élevé
        best_classification = max(
            classifications, 
            key=lambda x: x['Score']
        )
        
        # Parser le chemin hiérarchique
        path_parts = best_classification['Name'].split('/')
        
        if len(path_parts) >= 1:
            result['domain'] = {
                'code': path_parts[0],
                'confidence': best_classification['Score']
            }
            result['confidence_scores']['domain'] = best_classification['Score']
        
        if len(path_parts) >= 2:
            result['category'] = {
                'code': path_parts[1],
                'name': self._category_cache.get(path_parts[1], {}).get('name'),
                'confidence': best_classification['Score'] * 0.95  # Légère dégradation
            }
            result['confidence_scores']['category'] = result['category']['confidence']
        
        if len(path_parts) >= 3:
            result['subcategory'] = {
                'code': path_parts[2],
                'name': self._category_cache.get(path_parts[2], {}).get('name'),
                'confidence': best_classification['Score'] * 0.90
            }
            result['confidence_scores']['subcategory'] = result['subcategory']['confidence']
        
        if len(path_parts) >= 4:
            # Trouver le topic dans le cache
            topics = self._topic_cache.get(path_parts[2], [])
            topic_info = next(
                (t for t in topics if t['code'] == path_parts[3]), 
                None
            )
            
            result['topic'] = {
                'code': path_parts[3],
                'name': topic_info['name'] if topic_info else path_parts[3],
                'confidence': best_classification['Score'] * 0.85
            }
            result['confidence_scores']['topic'] = result['topic']['confidence']
        
        # Inclure les alternatives
        result['alternatives'] = [
            {
                'path': cls['Name'],
                'score': cls['Score']
            }
            for cls in classifications[1:4]  # Top 3 alternatives
        ]
        
        return result
    
    async def _extract_entities(
        self, 
        text: str,
        mentioned_persons: List[Dict]
    ) -> Dict:
        """
        Passe 2 : Extraction d'entités et enrichissement
        """
        try:
            # Extraction d'entités
            entities_response = self.comprehend.detect_entities(
                Text=text,
                LanguageCode='fr'
            )
            
            # Extraction de phrases clés
            key_phrases_response = self.comprehend.detect_key_phrases(
                Text=text,
                LanguageCode='fr'
            )
            
            return {
                'entities': self._process_entities(
                    entities_response['Entities'],
                    mentioned_persons
                ),
                'key_phrases': [
                    {
                        'text': kp['Text'],
                        'score': kp['Score']
                    }
                    for kp in key_phrases_response['KeyPhrases'][:5]
                ],
                'detected_languages': self._detect_languages(text)
            }
            
        except Exception as e:
            logger.error(f"Error in entity extraction: {e}")
            return {'entities': [], 'key_phrases': []}
    
    def _process_entities(
        self, 
        aws_entities: List,
        mentioned_persons: List[Dict]
    ) -> List[Dict]:
        """
        Enrichit les entités AWS avec les personnes mentionnées
        """
        processed = []
        
        # Mapper les types AWS vers nos types
        type_mapping = {
            'PERSON': 'HUMAN',
            'ORGANIZATION': 'MORAL',
            'LOCATION': 'LOCATION',
            'EVENT': 'EVENT',
            'DATE': 'DATE'
        }
        
        for entity in aws_entities:
            mapped_type = type_mapping.get(entity['Type'], 'OTHER')
            
            # Vérifier si c'est une personne déjà mentionnée
            mentioned = next(
                (p for p in mentioned_persons 
                 if p['name'].lower() in entity['Text'].lower()),
                None
            )
            
            processed.append({
                'text': entity['Text'],
                'type': mentioned['type'] if mentioned else mapped_type,
                'score': entity['Score'],
                'person_id': mentioned['id'] if mentioned else None,
                'begin_offset': entity['BeginOffset'],
                'end_offset': entity['EndOffset']
            })
        
        return processed
    
    def _enrich_text_with_context(
        self, 
        text: str, 
        location: Dict
    ) -> str:
        """
        Enrichit le texte avec le contexte pour améliorer la classification
        """
        context_parts = []
        
        if location.get('city'):
            context_parts.append(f"Ville: {location['city']}")
        
        if location.get('country'):
            context_parts.append(f"Pays: {location['country']}")
        
        context = " | ".join(context_parts)
        
        return f"{context} | {text}" if context else text
    
    def _merge_results(
        self,
        structure: Dict,
        entities: Dict,
        original_text: str
    ) -> Dict:
        """
        Fusionne tous les résultats en un seul objet
        """
        return {
            'classification': {
                'domain': structure['domain'],
                'category': structure['category'],
                'subcategory': structure['subcategory'],
                'topic': structure['topic'],
                'confidence_scores': structure['confidence_scores'],
                'alternatives': structure.get('alternatives', [])
            },
            'sentiment': structure['sentiment'],
            'entities': entities['entities'],
            'key_phrases': entities['key_phrases'],
            'languages': entities.get('detected_languages', ['fr']),
            'metadata': {
                'model_version': settings.COMPREHEND_MODEL_VERSION,
                'classification_date': timezone.now().isoformat(),
                'text_length': len(original_text),
                'word_count': len(original_text.split())
            }
        }
    
    def _get_default_classification(self) -> Dict:
        """
        Classification par défaut en cas d'erreur
        """
        return {
            'domain': {'code': 'AUTRES', 'confidence': 0.0},
            'category': {'code': 'NON_CLASSIFIE', 'confidence': 0.0},
            'subcategory': None,
            'topic': None,
            'confidence_scores': {
                'domain': 0.0,
                'category': 0.0
            },
            'sentiment': {
                'value': 'NEUTRAL',
                'scores': {'Positive': 0.0, 'Negative': 0.0, 'Neutral': 1.0, 'Mixed': 0.0},
                'intensity': 0.0
            }
        }
    
    def _detect_languages(self, text: str) -> List[str]:
        """
        Détecte les langues présentes dans le texte
        """
        try:
            response = self.comprehend.detect_dominant_language(Text=text)
            return [
                lang['LanguageCode'] 
                for lang in response['Languages'] 
                if lang['Score'] > 0.5
            ]
        except:
            return ['fr']  # Défaut au français
```

## 🔄 FLUX DE TRAITEMENT

### 1. Création d'expression avec classification

```python
# pillar_scan/views.py

class ExpressionViewSet(viewsets.ModelViewSet):
    
    async def create(self, request):
        # 1. Créer l'expression de base
        expression = PillarScanExpression(
            content=request.data['content'],
            mood=request.data['mood'],
            country=request.country,  # Du middleware
            user_id=request.user.id
        )
        expression.save()
        
        # 2. Lancer la classification AI (asynchrone)
        classification_task.delay(
            expression_id=str(expression.id),
            text=expression.content,
            location={
                'country': expression.country,
                'city': expression.city,
                'region': expression.region
            },
            mentioned_persons=request.data.get('mentioned_persons', [])
        )
        
        # 3. Retourner immédiatement (classification en background)
        return Response({
            'id': str(expression.id),
            'status': 'created',
            'classification_status': 'pending'
        })
```

### 2. Tâche Celery de classification

```python
# pillar_scan/tasks/classification_tasks.py

@shared_task
def classification_task(
    expression_id: str,
    text: str,
    location: Dict,
    mentioned_persons: List[Dict]
):
    """
    Tâche asynchrone de classification
    """
    try:
        # 1. Initialiser le service
        classifier = PillarScanAIClassificationService()
        
        # 2. Classifier l'expression
        result = asyncio.run(
            classifier.classify_expression(
                text, 
                location, 
                mentioned_persons
            )
        )
        
        # 3. Sauvegarder le résultat
        imputation = ExpressionImputationPath(
            expression_id=expression_id,
            category_code=result['classification']['category']['code'],
            subcategory_code=result['classification']['subcategory']['code'],
            topic_code=result['classification']['topic']['code'],
            expression_type_code='CITIZEN_VOICE',  # Type par défaut
            
            # Scores de confiance
            category_confidence=result['classification']['confidence_scores']['category'],
            subcategory_confidence=result['classification']['confidence_scores']['subcategory'],
            topic_confidence=result['classification']['confidence_scores']['topic'],
            expression_type_confidence=1.0,
            
            # Sentiment
            sentiment_value=result['sentiment']['value'],
            sentiment_intensity=result['sentiment']['intensity'],
            
            # Métadonnées
            model_version=result['metadata']['model_version']
        )
        imputation.save()
        
        # 4. Mettre à jour l'expression
        expression = PillarScanExpression.objects.get(id=expression_id)
        expression.classification_status = 'completed'
        expression.primary_category = result['classification']['category']['code']
        expression.ai_insights = json.dumps(result)
        expression.save()
        
        # 5. Indexer dans Elasticsearch
        index_expression_task.delay(expression_id)
        
        # 6. Notifier via SSE
        notify_classification_complete(expression_id, result)
        
        logger.info(f"Classification completed for expression {expression_id}")
        
    except Exception as e:
        logger.error(f"Classification failed for {expression_id}: {e}")
        
        # Marquer comme échoué
        expression = PillarScanExpression.objects.get(id=expression_id)
        expression.classification_status = 'failed'
        expression.save()
```

## 📊 CONFIGURATION AMAZON COMPREHEND

### 1. Modèle Custom pour les Piliers

```python
# Configuration du modèle custom
COMPREHEND_TRAINING_DATA = {
    "documents": [
        {
            "text": "Le bus n'est jamais à l'heure, j'arrive en retard au travail",
            "labels": ["ACCOMPLISSEMENT_SERVICES/ACCES_SERVICES_ESSENTIELS/TRANSPORT_PUBLIC/PONCTUALITE_TRANSPORT"]
        },
        {
            "text": "Mon patron a mis en place le télétravail flexible, c'est génial",
            "labels": ["ACCOMPLISSEMENT_SERVICES/EQUILIBRE_VIE_PRO/FLEXIBILITE_TRAVAIL/TELETRAVAIL"]
        },
        {
            "text": "L'hôpital manque de médicaments essentiels",
            "labels": ["BIEN_ETRE/SANTE_PHYSIQUE/ACCES_SOINS/MEDICAMENTS_PHARMACIE"]
        },
        # ... Des milliers d'exemples annotés
    ]
}
```

### 2. Configuration Django

```python
# settings.py

# Amazon Comprehend
AWS_REGION = 'eu-west-1'
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')

# Endpoints Comprehend
COMPREHEND_PILLAR_ENDPOINT = os.getenv(
    'COMPREHEND_PILLAR_ENDPOINT',
    'arn:aws:comprehend:eu-west-1:xxx:document-classifier-endpoint/pillarscan-classifier'
)
COMPREHEND_MODEL_VERSION = '1.0.0'

# Langues supportées
SUPPORTED_LANGUAGES = ['fr', 'en', 'es', 'de', 'it', 'pt']
DEFAULT_LANGUAGE = 'fr'
```

## 🎯 MÉTRIQUES ET MONITORING

### KPIs de Classification

```python
# Métriques à tracker
CLASSIFICATION_METRICS = {
    'accuracy': {
        'target': 0.85,
        'calculation': 'validations_correctes / total_validations'
    },
    'coverage': {
        'target': 0.95,
        'calculation': 'expressions_classifiees / total_expressions'
    },
    'confidence_avg': {
        'target': 0.75,
        'calculation': 'moyenne(confidence_scores)'
    },
    'processing_time': {
        'target': '< 2s',
        'calculation': 'p95(temps_classification)'
    }
}
```

### Dashboard de Monitoring

```python
# pillar_scan/services/classification_monitoring.py

class ClassificationMonitoringService:
    
    def get_classification_stats(self, country: str, period: str = '7d'):
        """
        Statistiques de classification par pays
        """
        return {
            'total_classified': self._count_classified(country, period),
            'by_domain': self._group_by_domain(country, period),
            'by_sentiment': self._group_by_sentiment(country, period),
            'confidence_distribution': self._confidence_histogram(country, period),
            'top_topics': self._top_topics(country, period, limit=20),
            'classification_errors': self._get_errors(country, period),
            'average_processing_time': self._avg_processing_time(country, period)
        }
```

## ⚠️ CONSIDÉRATIONS IMPORTANTES

1. **Souveraineté des données** : Les classifications restent dans le pays d'origine
2. **Multilinguisme** : Support de plusieurs langues avec modèles spécifiques
3. **Biais culturels** : Adaptation des modèles par région/culture
4. **Évolution** : Réentraînement régulier avec les validations manuelles
5. **Transparence** : Les utilisateurs peuvent voir et contester la classification

## 🚀 ÉVOLUTIONS FUTURES

1. **Classification multi-labels** : Une expression peut toucher plusieurs piliers
2. **Détection d'urgence** : Identifier les expressions critiques
3. **Clustering** : Regrouper les expressions similaires
4. **Prédiction d'impact** : Estimer l'impact potentiel d'une expression
5. **Recommandations** : Suggérer des actions basées sur la classification