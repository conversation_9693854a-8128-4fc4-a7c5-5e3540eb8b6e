# 📐 Spécifications Techniques Détaillées - Phase 1
## PillarScan MVP : "First Expression"

### 🎯 Objectif Phase 1
Créer une expérience utilisateur si fluide et gratifiante que l'utilisateur fait sa première expression en moins de 30 secondes et revient naturellement le lendemain.

---

## 🏗️ Architecture Technique

### Architecture Globale
```
┌─────────────────────────────────────────────────────────┐
│                    Frontend (Next.js)                    │
│                   Vercel Edge Network                    │
├─────────────────────────────────────────────────────────┤
│                    API Gateway                           │
│                 (Next.js API Routes)                     │
├─────────────────────────────────────────────────────────┤
│                   Backend Services                       │
│          ┌──────────────┐    ┌──────────────┐          │
│          │  Django API  │    │    Redis     │          │
│          │   (REST)     │    │   (Cache)    │          │
│          └──────────────┘    └──────────────┘          │
├─────────────────────────────────────────────────────────┤
│                    Data Layer                            │
│          ┌──────────────┐    ┌──────────────┐          │
│          │ CASSANDRA    │    │   Celery     │          │
│          │   (Main DB)  │    │  (Workers)   │          │
│          └──────────────┘    └──────────────┘          │
└─────────────────────────────────────────────────────────┘
```

---

## 💾 Modèles de Données

### User Model
```typescript
// types/user.ts
export interface User {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Profil basique
  nickname: string;
  avatarStyle: AvatarStyle;
  bio?: string; // Max 160 chars
  
  // Localisation (optionnelle)
  location?: {
    country: string;
    region?: string;
    city?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  
  // Gamification
  stats: {
    expressionCount: number;
    currentStreak: number;
    longestStreak: number;
    lastExpressionDate?: Date;
    impactScore: number;
  };
  
  // Badges
  badges: Badge[];
  
  // Préférences
  preferences: {
    language: string;
    timezone: string;
    notifications: NotificationPreferences;
  };
}

export interface AvatarStyle {
  color: string; // Hex color
  emoji: string; // Single emoji as avatar
  pattern?: number; // Background pattern ID
}

export interface Badge {
  id: string;
  type: BadgeType;
  earnedAt: Date;
  level?: number; // For progressive badges
}

export type BadgeType = 
  | 'first_expression'
  | 'streak_3'
  | 'streak_7'
  | 'streak_30'
  | 'helper' // Helped others
  | 'pioneer' // Early adopter
  | 'voice_of_reason'; // Quality expressions
```

### Expression Model
```typescript
// types/expression.ts
export interface Expression {
  id: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Core content
  mood: ExpressionMood;
  text: string; // Max 280 chars
  
  // Localisation
  location?: {
    displayName: string; // "Paris, France"
    coordinates?: {
      lat: number;
      lng: number;
    };
    accuracy?: number; // In meters
  };
  
  // Classification (simplified for MVP)
  suggestedPillar?: PillarType;
  confirmedPillar?: PillarType;
  
  // Engagement
  relateCount: number; // Number of "relates"
  isPublic: boolean; // Default false
  
  // Metadata
  device: {
    type: 'web' | 'ios' | 'android';
    browser?: string;
  };
}

export type ExpressionMood = 
  | 'frustrated' 
  | 'happy' 
  | 'idea' 
  | 'question';

export enum PillarType {
  EMOTIONAL_WELLBEING = 1,
  PHYSICAL_HEALTH = 2,
  EDUCATION_SKILLS = 3,
  ECONOMIC_SECURITY = 4,
  ENVIRONMENT = 5,
  SOCIAL_CONNECTIONS = 6,
  GOVERNANCE = 7,
  CULTURAL_IDENTITY = 8,
  PERSONAL_FREEDOM = 9,
  LIFE_SATISFACTION = 10,
  WORK_BALANCE = 11,
  ESSENTIAL_SERVICES = 12
}
```

### Database Schema (Cassandra/ScyllaDB)

```cql
-- Keyspace par pays (gouvernance des données)
CREATE KEYSPACE IF NOT EXISTS smatflow_france
    WITH REPLICATION = {
        'class': 'NetworkTopologyStrategy',
        'dc_france': 3,
        'dc_backup': 2
    }
    AND durable_writes = true;

-- Table des profils utilisateurs PillarScan
CREATE TABLE IF NOT EXISTS pillarscan_user_profiles (
    country_code text,
    person_id uuid,
    created_at timestamp,
    updated_at timestamp,
    
    -- Profile
    nickname text,
    avatar_style text, -- JSON
    bio text,
    
    -- Stats (counters pour performance)
    expression_count counter,
    current_streak int,
    longest_streak int,
    last_expression_date date,
    impact_score int,
    
    -- Gamification
    badges map<text, timestamp>, -- badge_type: earned_at
    achievements set<text>,
    level int,
    experience_points int,
    
    -- Expertise par pilier
    pillar_expertise map<int, int>, -- pillar: level
    
    -- Preferences
    preferred_language text,
    notification_preferences map<text, boolean>,
    
    -- Metadata
    datakey text, -- Pour idempotence
    
    PRIMARY KEY (country_code, person_id)
) WITH comment = 'Profils utilisateurs PillarScan par pays';

-- Table des expressions (hérite de CivicExpression)
CREATE TABLE IF NOT EXISTS pillarscan_expressions (
    country_code text,
    city_code text,
    created_at timestamp,
    expression_id timeuuid,
    
    -- Person link
    person_id uuid,
    
    -- Content
    mood text, -- frustrated, happy, idea, question
    text text,
    
    -- Geolocation (GeoJSON)
    location text,
    location_display_name text,
    municipality_code text,
    department_code text,
    region_code text,
    
    -- Classification
    pillar_scores map<int, float>, -- pillar_id: confidence
    suggested_pillar int,
    confirmed_pillar int,
    
    -- Engagement (counters)
    relate_count counter,
    quick_reactions map<text, counter>, -- emoji: count
    
    -- Visibility
    visibility_level text, -- private, anonymous, public
    is_published boolean,
    
    -- Gamification context
    user_streak_at_creation int,
    earned_badges list<text>,
    
    -- Rich context
    weather_context text,
    local_event_context text,
    
    -- Media
    media_refs list<uuid>,
    
    -- Metadata
    device_type text,
    datakey text,
    
    PRIMARY KEY ((country_code, city_code), created_at, expression_id)
) WITH CLUSTERING ORDER BY (created_at DESC, expression_id ASC)
    AND comment = 'Expressions PillarScan partitionnées par ville';

-- Vue matérialisée pour requêtes par mood
CREATE MATERIALIZED VIEW IF NOT EXISTS expressions_by_mood AS
    SELECT * FROM pillarscan_expressions
    WHERE mood IS NOT NULL AND country_code IS NOT NULL 
        AND city_code IS NOT NULL AND created_at IS NOT NULL
        AND expression_id IS NOT NULL
    PRIMARY KEY ((country_code, mood), created_at, city_code, expression_id)
    WITH CLUSTERING ORDER BY (created_at DESC);

-- Table des relations (qui relate à quoi)
CREATE TABLE IF NOT EXISTS pillarscan_relates (
    expression_id uuid,
    person_id uuid,
    created_at timestamp,
    
    PRIMARY KEY (expression_id, person_id)
) WITH comment = 'Tracking des relates par expression';

-- Table des scans de piliers complets
CREATE TABLE IF NOT EXISTS pillarscan_assessments (
    country_code text,
    person_id uuid,
    assessment_date date,
    assessment_id timeuuid,
    
    -- Scores
    pillar_scores map<int, int>, -- pillar: score (0-100)
    
    -- Comparisons
    local_percentiles map<int, int>,
    national_percentiles map<int, int>,
    
    -- Trends
    score_changes map<int, int>, -- vs last scan
    
    -- AI insights
    ai_insights list<text>,
    recommended_actions list<text>,
    identified_strengths list<int>,
    improvement_areas list<int>,
    
    -- Context
    location text, -- GeoJSON
    scan_duration int,
    scan_method text,
    
    -- Metadata
    datakey text,
    
    PRIMARY KEY ((country_code, person_id), assessment_date, assessment_id)
) WITH CLUSTERING ORDER BY (assessment_date DESC, assessment_id DESC)
    AND comment = 'Évaluations complètes des 12 piliers';

-- Table de compteurs pour métriques temps réel
CREATE TABLE IF NOT EXISTS pillarscan_activity_counters (
    country_code text,
    administrative_code text,
    date date,
    hour int,
    
    expression_count counter,
    unique_users counter,
    total_relates counter,
    
    PRIMARY KEY ((country_code, administrative_code, date), hour)
) WITH comment = 'Métriques d\'activité par zone administrative';

-- Table des espaces de débat
CREATE TABLE IF NOT EXISTS pillarscan_debate_spaces (
    country_code text,
    administrative_level text, -- city, department, region
    administrative_code text,
    debate_id timeuuid,
    created_at timestamp,
    
    -- Metadata
    title text,
    pillar int,
    related_pillars set<int>,
    
    -- Participants
    creator_person_id uuid,
    participant_count counter,
    active_participants set<uuid>,
    
    -- Rules
    debate_rules text, -- JSON
    moderation_level text,
    fact_check_enabled boolean,
    
    -- Metrics
    total_arguments counter,
    consensus_points list<text>,
    quality_score float,
    
    -- Status
    status text,
    closing_date timestamp,
    
    -- Results
    outcome_summary text,
    action_items list<text>,
    
    PRIMARY KEY ((country_code, administrative_level, administrative_code), created_at, debate_id)
) WITH CLUSTERING ORDER BY (created_at DESC)
    AND comment = 'Espaces de débat géolocalisés';

-- Indexes secondaires pour requêtes spécifiques
CREATE INDEX IF NOT EXISTS idx_person_expressions ON pillarscan_expressions (person_id);
CREATE INDEX IF NOT EXISTS idx_municipality ON pillarscan_expressions (municipality_code);
CREATE INDEX IF NOT EXISTS idx_visibility ON pillarscan_expressions (visibility_level);
```

---

## 🎨 Design System

### Couleurs
```typescript
// styles/colors.ts
export const colors = {
  // Primary - Espoir et positivité
  primary: {
    50: '#E6F3FF',
    100: '#CCE7FF',
    200: '#99CFFF',
    300: '#66B7FF',
    400: '#339FFF',
    500: '#0087FF', // Main
    600: '#006FCC',
    700: '#005799',
    800: '#003F66',
    900: '#002733',
  },
  
  // Moods
  moods: {
    frustrated: '#FF4757', // Rouge doux
    happy: '#26DE81',      // Vert positif
    idea: '#FFA502',       // Orange créatif
    question: '#5F5FFF',   // Bleu curieux
  },
  
  // Semantic
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  
  // Neutrals
  gray: {
    50: '#FAFAFA',
    100: '#F4F4F5',
    200: '#E4E4E7',
    300: '#D4D4D8',
    400: '#A1A1AA',
    500: '#71717A',
    600: '#52525B',
    700: '#3F3F46',
    800: '#27272A',
    900: '#18181B',
  },
};
```

### Typographie
```typescript
// styles/typography.ts
import { Inter, Sora } from 'next/font/google';

export const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
});

export const sora = Sora({
  subsets: ['latin'],
  variable: '--font-sora',
  weight: ['400', '600', '700'],
});

export const typography = {
  // Headings (Sora)
  h1: 'text-4xl md:text-5xl font-bold font-sora',
  h2: 'text-3xl md:text-4xl font-bold font-sora',
  h3: 'text-2xl md:text-3xl font-semibold font-sora',
  h4: 'text-xl md:text-2xl font-semibold font-sora',
  
  // Body (Inter)
  body: 'text-base font-inter',
  bodyLarge: 'text-lg font-inter',
  bodySmall: 'text-sm font-inter',
  
  // Special
  button: 'text-base font-medium font-inter',
  caption: 'text-xs font-inter text-gray-500',
};
```

### Composants UI de Base

#### Button Component
```typescript
// components/ui/Button.tsx
import { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { motion, HTMLMotionProps } from 'framer-motion';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-xl font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        primary: 'bg-primary-500 text-white hover:bg-primary-600 active:bg-primary-700',
        secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300',
        ghost: 'hover:bg-gray-100 active:bg-gray-200',
        mood: '', // Special variant for mood buttons
      },
      size: {
        sm: 'h-9 px-3 text-sm',
        md: 'h-11 px-4',
        lg: 'h-13 px-6 text-lg',
        xl: 'h-16 px-8 text-xl',
      },
      mood: {
        frustrated: 'bg-red-100 text-red-700 hover:bg-red-200',
        happy: 'bg-green-100 text-green-700 hover:bg-green-200',
        idea: 'bg-orange-100 text-orange-700 hover:bg-orange-200',
        question: 'bg-blue-100 text-blue-700 hover:bg-blue-200',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
    },
  }
);

interface ButtonProps
  extends HTMLMotionProps<'button'>,
    VariantProps<typeof buttonVariants> {
  isLoading?: boolean;
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, mood, isLoading, children, ...props }, ref) => {
    return (
      <motion.button
        ref={ref}
        className={buttonVariants({ variant, size, mood, className })}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{ type: 'spring', stiffness: 400, damping: 17 }}
        {...props}
      >
        {isLoading ? (
          <span className="animate-spin">⏳</span>
        ) : children}
      </motion.button>
    );
  }
);

Button.displayName = 'Button';
```

#### Expression Card Component
```typescript
// components/ExpressionCard.tsx
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import type { Expression } from '@/types/expression';

interface ExpressionCardProps {
  expression: Expression;
  onRelate?: () => void;
  showRelateButton?: boolean;
}

export function ExpressionCard({ 
  expression, 
  onRelate, 
  showRelateButton = true 
}: ExpressionCardProps) {
  const moodEmojis = {
    frustrated: '😤',
    happy: '😊',
    idea: '💡',
    question: '❓',
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{ y: -2 }}
      className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <span className="text-3xl">{moodEmojis[expression.mood]}</span>
          <div>
            <p className="text-sm text-gray-500">
              {expression.location?.displayName || 'Quelque part'}
            </p>
            <p className="text-xs text-gray-400">
              {formatDistanceToNow(new Date(expression.createdAt), {
                addSuffix: true,
                locale: fr,
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <p className="text-gray-800 mb-4 whitespace-pre-wrap">
        {expression.text}
      </p>

      {/* Footer */}
      {showRelateButton && (
        <div className="flex items-center justify-between">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onRelate}
            className="flex items-center gap-2 text-gray-500 hover:text-primary-500 transition-colors"
          >
            <span>🤝</span>
            <span className="text-sm font-medium">
              Je relate ({expression.relateCount})
            </span>
          </motion.button>
        </div>
      )}
    </motion.div>
  );
}
```

---

## 🔄 API Endpoints

### Authentication
```typescript
// pages/api/auth/[...nextauth].ts
import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';

export default NextAuth({
  providers: [
    // Magic link pour onboarding rapide
    CredentialsProvider({
      id: 'magic-link',
      name: 'Magic Link',
      credentials: {
        token: { label: "Token", type: "text" }
      },
      async authorize(credentials) {
        // Validate magic link token
        const user = await validateMagicToken(credentials.token);
        return user;
      }
    }),
    // OAuth pour ceux qui préfèrent
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.userId = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      session.userId = token.userId;
      return session;
    },
  },
});
```

### Expression API avec Backend Django/Cassandra
```typescript
// lib/api/expressions.ts
import axios from 'axios';

const DJANGO_API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v2';

// Client API pour communiquer avec Django
export const expressionsAPI = {
  // Créer une expression via l'API Django
  async createExpression(data: {
    mood: string;
    text: string;
    location?: { lat: number; lng: number };
    pillar?: number;
  }) {
    const response = await axios.post(
      `${DJANGO_API_BASE}/civicperson/pillarscan/expressions/`,
      {
        ...data,
        device_type: 'web',
        datakey: generateDataKey(), // Idempotence
      },
      {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'X-Country-Code': getUserCountry(), // Pour routing Cassandra
        }
      }
    );
    return response.data;
  },

  // Récupérer les expressions proches
  async getNearbyExpressions(params: {
    lat: number;
    lng: number;
    radius: number;
    mood?: string;
    limit?: number;
  }) {
    const response = await axios.get(
      `${DJANGO_API_BASE}/civicperson/pillarscan/expressions/nearby/`,
      {
        params,
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'X-Country-Code': getUserCountry(),
        }
      }
    );
    return response.data;
  },

  // Récupérer les tendances municipales
  async getMunicipalTrends(municipalityCode: string) {
    const response = await axios.get(
      `${DJANGO_API_BASE}/civicperson/pillarscan/trends/municipal/${municipalityCode}/`,
      {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
        }
      }
    );
    return response.data;
  }
};

// Hook React Query pour les expressions
export function useNearbyExpressions(location: { lat: number; lng: number } | null) {
  return useQuery({
    queryKey: ['expressions', 'nearby', location],
    queryFn: () => location ? expressionsAPI.getNearbyExpressions({
      ...location,
      radius: 5000, // 5km
      limit: 50
    }) : Promise.resolve([]),
    enabled: !!location,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
```

### Pages API Next.js (Proxy vers Django)
```typescript
// pages/api/v2/[...path].ts
// Proxy toutes les requêtes vers Django pour simplifier CORS et auth

import { NextApiRequest, NextApiResponse } from 'next';
import httpProxy from 'http-proxy-middleware';

const proxy = httpProxy.createProxyMiddleware({
  target: process.env.DJANGO_API_URL || 'http://localhost:8000',
  changeOrigin: true,
  pathRewrite: {
    '^/api/v2': '/api/v2',
  },
  onProxyReq: (proxyReq, req) => {
    // Ajouter le country code pour le routing Cassandra
    if (req.session?.user?.countryCode) {
      proxyReq.setHeader('X-Country-Code', req.session.user.countryCode);
    }
    
    // Forward l'auth token
    if (req.headers.authorization) {
      proxyReq.setHeader('Authorization', req.headers.authorization);
    }
  },
});

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  return proxy(req, res);
}

export const config = {
  api: {
    bodyParser: false,
    externalResolver: true,
  },
};
```

---

## 🎮 Système de Gamification

### Badges Logic
```typescript
// services/gamification.ts
export async function checkBadges(userId: string): Promise<Badge[]> {
  const user = await getUser(userId);
  const newBadges: Badge[] = [];

  // First Expression
  if (user.stats.expressionCount === 1) {
    newBadges.push(await awardBadge(userId, 'first_expression'));
  }

  // Streak badges
  const streakBadges = [
    { days: 3, type: 'streak_3' },
    { days: 7, type: 'streak_7' },
    { days: 30, type: 'streak_30' },
  ];

  for (const { days, type } of streakBadges) {
    if (user.stats.currentStreak === days) {
      newBadges.push(await awardBadge(userId, type));
    }
  }

  // Helper badge (related to 10+ expressions)
  const relateCount = await getUserRelateCount(userId);
  if (relateCount >= 10 && !user.badges.find(b => b.type === 'helper')) {
    newBadges.push(await awardBadge(userId, 'helper'));
  }

  return newBadges;
}

export async function calculateImpactScore(userId: string): Promise<number> {
  // Complex calculation based on:
  // - Number of expressions
  // - Quality (relate count)
  // - Consistency (streaks)
  // - Diversity (different moods/pillars)
  
  const weights = {
    expressionCount: 1,
    totalRelates: 2,
    uniqueDays: 3,
    moodDiversity: 2,
  };
  
  // Implementation...
  return score;
}
```

---

## 🚀 Performance Optimizations

### Image Optimization
```typescript
// next.config.js
module.exports = {
  images: {
    domains: ['avatars.dicebear.com'], // For avatar generation
    formats: ['image/avif', 'image/webp'],
  },
  
  // Enable SWC minification
  swcMinify: true,
  
  // Optimize for production
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
};
```

### Caching Strategy
```typescript
// lib/cache.ts
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

export const cache = {
  // Cache nearby expressions for 5 minutes
  async getNearbyExpressions(lat: number, lng: number, radius: number) {
    const key = `expressions:nearby:${lat}:${lng}:${radius}`;
    const cached = await redis.get(key);
    
    if (cached) return cached;
    
    const expressions = await fetchNearbyExpressions(lat, lng, radius);
    await redis.setex(key, 300, expressions); // 5 minutes
    
    return expressions;
  },
  
  // Cache user stats for 1 minute
  async getUserStats(userId: string) {
    const key = `user:stats:${userId}`;
    const cached = await redis.get(key);
    
    if (cached) return cached;
    
    const stats = await calculateUserStats(userId);
    await redis.setex(key, 60, stats); // 1 minute
    
    return stats;
  },
};
```

---

## 📱 Mobile-First Responsive Design

### Responsive Utilities
```typescript
// hooks/useMediaQuery.ts
import { useState, useEffect } from 'react';

export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);
    
    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
}

// Usage
export const useIsMobile = () => useMediaQuery('(max-width: 768px)');
export const useIsTablet = () => useMediaQuery('(max-width: 1024px)');
```

### Touch Gestures
```typescript
// hooks/useSwipe.ts
import { useEffect, useRef } from 'react';

interface SwipeHandlers {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
}

export function useSwipe(handlers: SwipeHandlers) {
  const touchStart = useRef({ x: 0, y: 0 });
  const touchEnd = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const handleTouchStart = (e: TouchEvent) => {
      touchStart.current = {
        x: e.targetTouches[0].clientX,
        y: e.targetTouches[0].clientY,
      };
    };

    const handleTouchMove = (e: TouchEvent) => {
      touchEnd.current = {
        x: e.targetTouches[0].clientX,
        y: e.targetTouches[0].clientY,
      };
    };

    const handleTouchEnd = () => {
      const deltaX = touchStart.current.x - touchEnd.current.x;
      const deltaY = touchStart.current.y - touchEnd.current.y;
      const minSwipeDistance = 50;

      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        if (deltaX > minSwipeDistance) {
          handlers.onSwipeLeft?.();
        } else if (deltaX < -minSwipeDistance) {
          handlers.onSwipeRight?.();
        }
      } else {
        // Vertical swipe
        if (deltaY > minSwipeDistance) {
          handlers.onSwipeUp?.();
        } else if (deltaY < -minSwipeDistance) {
          handlers.onSwipeDown?.();
        }
      }
    };

    document.addEventListener('touchstart', handleTouchStart);
    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', handleTouchEnd);

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handlers]);
}
```

---

## 🧪 Tests

### Unit Tests
```typescript
// __tests__/components/ExpressionCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ExpressionCard } from '@/components/ExpressionCard';

describe('ExpressionCard', () => {
  const mockExpression = {
    id: '1',
    userId: 'user1',
    mood: 'happy',
    text: 'Test expression',
    relateCount: 5,
    createdAt: new Date(),
  };

  it('renders expression content', () => {
    render(<ExpressionCard expression={mockExpression} />);
    
    expect(screen.getByText('Test expression')).toBeInTheDocument();
    expect(screen.getByText('😊')).toBeInTheDocument();
  });

  it('calls onRelate when button clicked', () => {
    const onRelate = jest.fn();
    render(
      <ExpressionCard 
        expression={mockExpression} 
        onRelate={onRelate}
      />
    );
    
    fireEvent.click(screen.getByText(/Je relate/));
    expect(onRelate).toHaveBeenCalledTimes(1);
  });
});
```

---

## 🚦 Checklist Pré-Production Phase 1

### Sécurité
- [ ] Rate limiting sur toutes les API
- [ ] Validation des inputs (Zod schemas)
- [ ] Sanitization du contenu utilisateur
- [ ] CORS configuré correctement
- [ ] Headers de sécurité (Helmet.js)
- [ ] Authentification robuste

### Performance
- [ ] Lighthouse score > 90
- [ ] Bundle size < 200KB (first load)
- [ ] Time to Interactive < 3s
- [ ] Images optimisées (WebP/AVIF)
- [ ] Code splitting par route
- [ ] Service Worker pour offline

### Accessibilité
- [ ] WCAG 2.1 AA compliant
- [ ] Keyboard navigation
- [ ] Screen reader friendly
- [ ] Color contrast ratios
- [ ] Focus indicators
- [ ] ARIA labels

### Analytics
- [ ] Mixpanel configuré
- [ ] Events tracking plan
- [ ] Error tracking (Sentry)
- [ ] Performance monitoring
- [ ] User flow tracking
- [ ] A/B test framework

### DevOps
- [ ] CI/CD pipeline
- [ ] Automated tests
- [ ] Environment variables
- [ ] Database migrations
- [ ] Backup strategy
- [ ] Monitoring alerts

---

Cette Phase 1 pose des fondations solides pour une application addictive et scalable. L'accent est mis sur l'expérience utilisateur fluide et la satisfaction immédiate, tout en préparant l'architecture pour les phases futures.

**Prochaine étape : Commencer par le setup du projet et l'implémentation de l'onboarding magique ! 🚀**