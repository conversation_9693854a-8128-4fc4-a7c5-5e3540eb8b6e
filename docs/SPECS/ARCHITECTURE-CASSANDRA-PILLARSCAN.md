# 🏗️ Architecture PillarScan avec Infrastructure Cassandra
## Intégration Native avec l'Écosystème SmatFlow Existant

### 🎯 Vue d'Ensemble

PillarScan s'appuie sur l'infrastructure Cassandra/ScyllaDB existante de SmatFlow, réutilisant les modèles CivicStore pour les expressions civiques, CivicPerson pour la gestion des entités, et le système Geography/Geotagging pour la localisation universelle.

### 🗄️ Architecture de Données Cassandra

```
┌─────────────────────────────────────────────────────────┐
│                   Frontend Next.js                       │
│                  (Vercel Edge Network)                   │
├─────────────────────────────────────────────────────────┤
│                    API Gateway                           │
│              Django REST Framework v2                    │
├─────────────────────────────────────────────────────────┤
│                  Services Python                         │
│    ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│    │ CivicStore  │  │ CivicPerson │  │  Geography  │  │
│    │  Services   │  │  Services   │  │  Services   │  │
│    └─────────────┘  └─────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────┤
│              Cassandra/ScyllaDB Cluster                  │
│         Partitionnement par Pays (Gouvernance)          │
│    ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│    │   France    │  │  Cameroun   │  │    USA      │  │
│    │   Keyspace  │  │  Keyspace   │  │  Keyspace   │  │
│    └─────────────┘  └─────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────┘
```

### 📊 Modèles de Données PillarScan dans Cassandra

#### 1. Extension du modèle CivicExpression pour PillarScan

```python
# civicstore/models/pillarscan_expression.py
from cassandra.cqlengine import columns
from civicstore.models import CivicExpression

class PillarScanExpression(CivicExpression):
    """
    Extension du modèle CivicExpression pour PillarScan
    Hérite de toute l'infrastructure existante
    """
    __table_name__ = 'pillarscan_expressions'
    
    # Partition keys héritées : country_code, city_code
    # Clustering keys hérités : created_at DESC, expression_id
    
    # Champs spécifiques PillarScan
    mood = columns.Text(required=True)  # frustrated, happy, idea, question
    pillar_scores = columns.Map(columns.Integer, columns.Float)  # {1: 0.95, 2: 0.80}
    
    # Gamification
    user_streak_at_creation = columns.Integer(default=0)
    earned_badges = columns.List(columns.Text)  # Badges gagnés avec cette expression
    
    # Engagement rapide
    relate_count = columns.Counter()  # Utilise les counters Cassandra
    quick_reactions = columns.Map(columns.Text, columns.Integer)  # {"🔥": 12, "💡": 5}
    
    # Contexte enrichi
    weather_context = columns.Text()  # Météo au moment de l'expression
    local_event_context = columns.Text()  # Événement local en cours
    
    # Visibilité communautaire
    visibility_level = columns.Text(default='private')  # private, anonymous, public
    debate_space_id = columns.UUID()  # Si associé à un débat
```

#### 2. Profil Utilisateur PillarScan

```python
# civicstore/models/pillarscan_user_profile.py
class PillarScanUserProfile(Model):
    """
    Profil utilisateur spécifique PillarScan
    Lié à Person via person_id
    """
    __table_name__ = 'pillarscan_user_profiles'
    
    # Partition par pays pour gouvernance
    country_code = columns.Text(partition_key=True)
    person_id = columns.UUID(primary_key=True)
    
    # Avatar et personnalisation
    avatar_style = columns.Text()  # JSON avec couleur, emoji, pattern
    nickname = columns.Text(required=True, max_length=50)
    bio = columns.Text(max_length=160)
    
    # Stats dénormalisées pour performance
    expression_count = columns.Counter()
    current_streak = columns.Integer(default=0)
    longest_streak = columns.Integer(default=0)
    last_expression_date = columns.Date()
    impact_score = columns.Integer(default=0)
    
    # Gamification
    badges = columns.Map(columns.Text, columns.DateTime)  # type: earned_at
    achievements = columns.Set(columns.Text)
    level = columns.Integer(default=1)
    experience_points = columns.Integer(default=0)
    
    # Préférences
    preferred_language = columns.Text(default='fr')
    notification_preferences = columns.Map(columns.Text, columns.Boolean)
    
    # Expertise par pilier
    pillar_expertise = columns.Map(columns.Integer, columns.Integer)  # pillar: level
    
    # Metadata
    created_at = columns.DateTime(default=datetime.utcnow)
    updated_at = columns.DateTime(default=datetime.utcnow)
    datakey = columns.Text(required=True)  # Idempotence
```

#### 3. Système de Scan des 12 Piliers

```python
# civicstore/models/pillarscan_assessment.py
class PillarScanAssessment(Model):
    """
    Évaluation complète des 12 piliers
    """
    __table_name__ = 'pillarscan_assessments'
    
    # Partition par pays et utilisateur
    country_code = columns.Text(partition_key=True)
    person_id = columns.UUID(partition_key=True)
    assessment_date = columns.Date(primary_key=True, clustering_order='DESC')
    assessment_id = columns.TimeUUID(primary_key=True, default=uuid1)
    
    # Scores par pilier (1-100)
    pillar_scores = columns.Map(columns.Integer, columns.Integer)
    
    # Comparaisons
    local_percentiles = columns.Map(columns.Integer, columns.Integer)  # vs ville
    national_percentiles = columns.Map(columns.Integer, columns.Integer)  # vs pays
    
    # Tendances
    score_changes = columns.Map(columns.Integer, columns.Integer)  # vs dernier scan
    
    # Insights IA
    ai_insights = columns.List(columns.Text)
    recommended_actions = columns.List(columns.Text)
    identified_strengths = columns.List(columns.Integer)  # Piliers forts
    improvement_areas = columns.List(columns.Integer)  # Piliers à améliorer
    
    # Contexte
    location = columns.Text()  # GeoJSON point
    scan_duration = columns.Integer()  # Secondes
    scan_method = columns.Text()  # quick, detailed, guided
    
    # Metadata
    datakey = columns.Text(required=True)
```

#### 4. Espaces de Débat Communautaires

```python
# civicstore/models/pillarscan_debate_space.py
class PillarScanDebateSpace(Model):
    """
    Espaces de débat thématiques géolocalisés
    """
    __table_name__ = 'pillarscan_debate_spaces'
    
    # Partition géographique
    country_code = columns.Text(partition_key=True)
    administrative_level = columns.Text(partition_key=True)  # city, department, region
    administrative_code = columns.Text(partition_key=True)
    
    # Identification
    debate_id = columns.TimeUUID(primary_key=True, default=uuid1)
    created_at = columns.DateTime(primary_key=True, clustering_order='DESC')
    
    # Métadonnées du débat
    title = columns.Text(required=True)
    pillar = columns.Integer(required=True)  # Pilier principal
    related_pillars = columns.Set(columns.Integer)
    
    # Participants
    creator_person_id = columns.UUID(required=True)
    participant_count = columns.Counter()
    active_participants = columns.Set(columns.UUID)  # Dernières 24h
    
    # Règles et modération
    debate_rules = columns.Text()  # JSON
    moderation_level = columns.Text(default='community')  # ai, community, hybrid
    fact_check_enabled = columns.Boolean(default=True)
    
    # Métriques d'engagement
    total_arguments = columns.Counter()
    consensus_points = columns.List(columns.Text)
    quality_score = columns.Float(default=0.0)
    
    # État
    status = columns.Text(default='active')  # active, closed, archived
    closing_date = columns.DateTime()
    
    # Résultats
    outcome_summary = columns.Text()
    action_items = columns.List(columns.Text)
    linked_initiatives = columns.Set(columns.UUID)
```

### 🌍 Intégration avec Geography et Geotagging

#### Utilisation du système Geography existant

```python
# PillarScan utilise directement les modèles Geography
from geography.models import (
    GeographyCountry,
    GeographyAdministrativeRegion,
    GeographyAdministrativeDepartment,
    GeographyAdministrativeMunicipality
)

# Enrichissement automatique des expressions avec contexte géographique
def enrich_expression_with_geo_context(expression, coordinates):
    # Utilise le service Geotagging pour identifier la localisation
    location = geotagging_service.reverse_geocode(coordinates)
    
    # Enrichit avec les données administratives
    municipality = GeographyAdministrativeMunicipality.objects(
        municipality_code=location['municipality_code']
    ).first()
    
    expression.municipality_data = {
        'name': municipality.name,
        'population': municipality.population,
        'density': municipality.density,
        'has_civic_expression': True  # Flag pour PillarScan
    }
    
    return expression
```

#### Requêtes géospatiales optimisées

```python
# Utilisation du GeotaggingService pour requêtes spatiales
from geotagging.services import GeotaggingService

class PillarScanGeoQueries:
    def get_nearby_expressions(self, lat, lon, radius_km=5):
        """
        Récupère les expressions proches avec grille spatiale optimisée
        """
        # Utilise la grille Cassandra pour performance
        grid_cells = self.geo_service.get_grid_cells_in_radius(
            lat, lon, radius_km
        )
        
        # Requête optimisée sur les cellules
        expressions = PillarScanExpression.objects.filter(
            spatial_grid_id__in=grid_cells
        ).limit(100)
        
        return expressions
    
    def get_municipal_trends(self, municipality_code):
        """
        Analyse des tendances au niveau municipal
        """
        # Requête directe grâce au partitionnement
        return PillarScanExpression.objects.filter(
            country_code=municipality_code[:2],
            municipality_code=municipality_code
        ).order_by('-created_at')
```

### 🔐 Gouvernance des Données par Pays

```python
# Configuration des keyspaces par pays
COUNTRY_KEYSPACES = {
    'FR': 'smatflow_france',
    'CM': 'smatflow_cameroun',
    'US': 'smatflow_usa',
    # ... autres pays
}

# Middleware pour router vers le bon keyspace
class CountryBasedRoutingMiddleware:
    def process_request(self, request):
        # Détermine le pays de l'utilisateur
        user_country = request.user.primary_residence_country_code
        
        # Configure la connexion Cassandra pour ce keyspace
        connection.set_keyspace(COUNTRY_KEYSPACES[user_country])
        
        return request
```

### 🚀 Optimisations Cassandra pour PillarScan

#### 1. Stratégie de partitionnement

```cql
-- Table principale avec partitionnement géographique
CREATE TABLE pillarscan_expressions (
    country_code text,
    city_code text,
    created_at timestamp,
    expression_id timeuuid,
    -- Autres colonnes...
    PRIMARY KEY ((country_code, city_code), created_at, expression_id)
) WITH CLUSTERING ORDER BY (created_at DESC, expression_id ASC)
  AND gc_grace_seconds = 86400
  AND compaction = {'class': 'TimeWindowCompactionStrategy',
                    'compaction_window_unit': 'DAYS',
                    'compaction_window_size': '1'};
```

#### 2. Vues matérialisées pour requêtes fréquentes

```cql
-- Vue pour expressions par mood
CREATE MATERIALIZED VIEW expressions_by_mood AS
    SELECT * FROM pillarscan_expressions
    WHERE mood IS NOT NULL
    PRIMARY KEY ((country_code, mood), created_at, city_code, expression_id)
    WITH CLUSTERING ORDER BY (created_at DESC);

-- Vue pour trending topics
CREATE MATERIALIZED VIEW trending_expressions AS
    SELECT * FROM pillarscan_expressions
    WHERE relate_count > 10
    PRIMARY KEY ((country_code, city_code), relate_count, created_at, expression_id)
    WITH CLUSTERING ORDER BY (relate_count DESC, created_at DESC);
```

#### 3. Tables de compteurs pour métriques temps réel

```cql
-- Compteurs d'activité par zone
CREATE TABLE pillarscan_activity_counters (
    country_code text,
    administrative_code text,
    date date,
    hour int,
    expression_count counter,
    unique_users counter,
    total_relates counter,
    PRIMARY KEY ((country_code, administrative_code, date), hour)
);

-- Stats globales par pilier
CREATE TABLE pillarscan_pillar_stats (
    country_code text,
    pillar int,
    date date,
    positive_count counter,
    negative_count counter,
    neutral_count counter,
    improvement_suggestions counter,
    PRIMARY KEY ((country_code, pillar), date)
) WITH CLUSTERING ORDER BY (date DESC);
```

### 🔄 API REST v2 Integration

```python
# civicperson/api/v2/pillarscan_endpoints.py
from rest_framework import viewsets
from civicperson.api.v2.base import BasePersonAPIView

class PillarScanExpressionViewSet(BasePersonAPIView, viewsets.ModelViewSet):
    """
    API endpoints pour PillarScan
    Hérite de l'authentification et permissions Person
    """
    
    def create_expression(self, request):
        # Vérifie que la personne existe
        person = self.get_person_or_error(request.user)
        
        # Crée l'expression avec contexte géographique
        expression_data = request.data
        expression_data['person_id'] = person.person_id
        expression_data['country_code'] = person.country_primary_residence_code
        
        # Enrichissement automatique
        if 'coordinates' in expression_data:
            geo_context = self.enrich_with_geography(
                expression_data['coordinates']
            )
            expression_data.update(geo_context)
        
        # Sauvegarde dans Cassandra
        expression = PillarScanExpression.create(**expression_data)
        
        # Déclenche les processus asynchrones
        self.trigger_gamification(person.person_id)
        self.trigger_ai_classification(expression.expression_id)
        
        return Response(ExpressionSerializer(expression).data)
```

### 📈 Performance et Scalabilité

#### Configuration ScyllaDB optimisée

```yaml
# scylla.yaml pour PillarScan
# Optimisé pour lectures géographiques fréquentes

# Cache important pour requêtes géospatiales
key_cache_size_in_mb: 2048
row_cache_size_in_mb: 4096

# Compaction pour données temporelles
compaction_throughput_mb_per_sec: 64
compaction_large_partition_warning_threshold_mb: 500

# Optimisation pour counters
counter_cache_size_in_mb: 512
counter_cache_save_period: 3600

# Réplication par datacenter (gouvernance)
endpoint_snitch: GossipingPropertyFileSnitch
# dc_france: 3 replicas
# dc_cameroun: 3 replicas
# dc_usa: 3 replicas
```

#### Stratégies de cache Redis

```python
# Cache patterns pour PillarScan
class PillarScanCache:
    def __init__(self):
        self.redis = Redis(
            connection_pool=BlockingConnectionPool(
                host=REDIS_HOST,
                port=REDIS_PORT,
                max_connections=50
            )
        )
    
    def cache_user_stats(self, person_id, stats, ttl=300):
        """Cache les stats utilisateur pour 5 minutes"""
        key = f"pillarscan:stats:{person_id}"
        self.redis.setex(key, ttl, json.dumps(stats))
    
    def cache_municipal_trends(self, municipality_code, trends, ttl=3600):
        """Cache les tendances municipales pour 1 heure"""
        key = f"pillarscan:trends:{municipality_code}"
        self.redis.setex(key, ttl, json.dumps(trends))
    
    def get_or_compute(self, key, compute_func, ttl=300):
        """Pattern cache-aside générique"""
        cached = self.redis.get(key)
        if cached:
            return json.loads(cached)
        
        result = compute_func()
        self.redis.setex(key, ttl, json.dumps(result))
        return result
```

### 🌐 Distribution Géographique Multi-Datacenter

```python
# Configuration pour déploiement planétaire
CASSANDRA_CLUSTERS = {
    'europe': {
        'contact_points': ['********', '********', '********'],
        'keyspaces': ['smatflow_france', 'smatflow_belgium', 'smatflow_swiss']
    },
    'africa': {
        'contact_points': ['********', '********', '********'],
        'keyspaces': ['smatflow_cameroun', 'smatflow_gabon', 'smatflow_senegal']
    },
    'americas': {
        'contact_points': ['********', '********', '********'],
        'keyspaces': ['smatflow_usa', 'smatflow_canada', 'smatflow_brazil']
    }
}

# Routing intelligent basé sur la localisation
class GeoAwareCassandraRouter:
    def get_cluster_for_country(self, country_code):
        # Détermine le cluster optimal pour un pays
        country_to_region = {
            'FR': 'europe', 'CM': 'africa', 'US': 'americas'
        }
        return CASSANDRA_CLUSTERS[country_to_region[country_code]]
```

Cette architecture tire parti de toute l'infrastructure Cassandra existante de SmatFlow, garantissant scalabilité planétaire, gouvernance par pays, et performance optimale pour PillarScan.