# 🌍 SPÉCIFICATION : LOCALISATION ET SOUVERAINETÉ DES DONNÉES

> **CRITIQUE** : PillarScan est une application PLANÉTAIRE avec souveraineté des données par PAYS
> Dernière mise à jour : 24 mai 2025

## 🎯 PRINCIPE FONDAMENTAL

**TOUTES les données sont partitionnées par PAYS** - C'est un principe NON NÉGOCIABLE de l'architecture.

- Les expressions sont stockées avec une partition Cassandra par pays
- Les recherches sont TOUJOURS limitées au pays de l'utilisateur
- Les statistiques sont calculées par pays
- Les données ne traversent JAMAIS les frontières sans autorisation explicite

## 📍 DÉTERMINATION DU PAYS

### 1. Utilisateur Connecté
```typescript
// Le profil utilisateur contient le pays principal
interface UserProfile {
  id: string;
  primaryCountry: string; // Code ISO 3166-1 alpha-2 (FR, US, CA...)
  allowedCountries: string[]; // Pays autorisés pour cet utilisateur
  defaultViewCountry: string; // Pays par défaut pour la vue
}
```

### 2. Utilisateur Non Connecté

#### Flux de détermination du pays :

```mermaid
graph TD
    A[Utilisateur arrive] --> B{Connecté?}
    B -->|Oui| C[Utiliser pays du profil]
    B -->|Non| D{Géolocalisation autorisée?}
    D -->|Oui| E[Détecter pays par IP/GPS]
    D -->|Non| F[FORCER choix]
    F --> G[Modal obligatoire]
    G --> H{Choix fait?}
    H -->|Autoriser géoloc| E
    H -->|Se connecter| I[Page login]
    H -->|Sélectionner pays| J[Sauver en localStorage]
    E --> K[Valider pays]
    J --> K
    K --> L[Afficher expressions du pays]
```

### 3. Implémentation Frontend

```typescript
// hooks/useCountryEnforcement.ts
export function useCountryEnforcement() {
  const { user } = useAuth();
  const [country, setCountry] = useState<string | null>(null);
  const [showCountryModal, setShowCountryModal] = useState(false);

  useEffect(() => {
    const determineCountry = async () => {
      // 1. Utilisateur connecté
      if (user?.primaryCountry) {
        setCountry(user.primaryCountry);
        return;
      }

      // 2. Vérifier localStorage
      const savedCountry = localStorage.getItem('selectedCountry');
      if (savedCountry) {
        setCountry(savedCountry);
        return;
      }

      // 3. Tenter géolocalisation
      try {
        const position = await getCurrentPosition();
        const detectedCountry = await getCountryFromCoords(
          position.coords.latitude,
          position.coords.longitude
        );
        if (detectedCountry) {
          setCountry(detectedCountry);
          localStorage.setItem('selectedCountry', detectedCountry);
          return;
        }
      } catch (error) {
        console.log('Géolocalisation refusée ou échouée');
      }

      // 4. Forcer le choix
      setShowCountryModal(true);
    };

    determineCountry();
  }, [user]);

  return { country, showCountryModal, setCountry };
}
```

### 4. Modal de Sélection Obligatoire

```typescript
// components/CountrySelectionModal.tsx
export function CountrySelectionModal({ 
  isOpen, 
  onClose 
}: CountryModalProps) {
  // Cette modal NE PEUT PAS être fermée sans sélection
  return (
    <Modal 
      isOpen={isOpen} 
      onClose={() => {}} // Pas de fermeture possible
      closeOnOverlayClick={false}
      closeOnEsc={false}
    >
      <ModalContent>
        <ModalHeader>
          <h2>Sélection du pays obligatoire</h2>
        </ModalHeader>
        
        <ModalBody>
          <p className="mb-4">
            PillarScan respecte la souveraineté des données. 
            Vous devez sélectionner votre pays pour continuer.
          </p>
          
          <div className="space-y-4">
            <Button 
              onClick={requestGeolocation}
              variant="primary"
              fullWidth
            >
              🌍 Autoriser la géolocalisation
            </Button>
            
            <Button 
              onClick={redirectToLogin}
              variant="secondary"
              fullWidth
            >
              🔐 Se connecter
            </Button>
            
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">Ou</span>
              </div>
            </div>
            
            <CountrySelector 
              onChange={(country) => {
                localStorage.setItem('selectedCountry', country);
                window.location.reload();
              }}
              placeholder="Sélectionner manuellement un pays"
            />
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
```

## 🔍 IMPACT SUR L'APPLICATION

### 1. Toutes les Requêtes API

```typescript
// lib/api/client.ts
class APIClient {
  private getCountryHeader(): string {
    const country = this.getRequiredCountry();
    if (!country) {
      throw new Error('Country not set - cannot make API requests');
    }
    return country;
  }

  async request(url: string, options: RequestOptions = {}) {
    const headers = {
      ...options.headers,
      'X-Country-Code': this.getCountryHeader(),
    };
    
    return fetch(url, { ...options, headers });
  }
}
```

### 2. Affichage des Expressions

```typescript
// Toutes les expressions affichées DOIVENT appartenir au pays sélectionné
const { data: expressions } = useQuery({
  queryKey: ['expressions', country],
  queryFn: () => api.getExpressions({ country }), // Paramètre obligatoire
  enabled: !!country, // Pas de requête sans pays
});
```

### 3. Création d'Expression

```typescript
// Le pays est automatiquement ajouté à toute expression créée
const createExpression = async (data: ExpressionData) => {
  const country = getRequiredCountry();
  
  return api.createExpression({
    ...data,
    country, // Ajouté automatiquement
    location: {
      ...data.location,
      country, // Redondance pour sécurité
    }
  });
};
```

## 🛡️ SÉCURITÉ BACKEND

### 1. Middleware Django

```python
# middleware/country_enforcement.py
class CountryEnforcementMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Extraire le pays du header
        country_code = request.headers.get('X-Country-Code')
        
        # Pour les utilisateurs connectés, vérifier la cohérence
        if request.user.is_authenticated:
            allowed_countries = request.user.profile.allowed_countries
            if country_code not in allowed_countries:
                return JsonResponse(
                    {'error': 'Country not allowed for this user'}, 
                    status=403
                )
        
        # Attacher le pays à la requête
        request.country = country_code
        
        response = self.get_response(request)
        return response
```

### 2. Filtrage Automatique

```python
# Toutes les queries doivent filtrer par pays
class ExpressionViewSet(viewsets.ModelViewSet):
    def get_queryset(self):
        # Filtrage automatique par pays
        country = self.request.country
        if not country:
            raise ValidationError("Country is required")
            
        return PillarScanExpression.objects.filter(
            country=country
        ).order_by('-created_at')
```

## 📊 CHANGEMENT DE PAYS

### Pour les Utilisateurs Connectés

1. Peuvent voir plusieurs pays s'ils ont les permissions
2. Sélecteur de pays dans l'interface
3. Le changement recharge toutes les données

### Pour les Non-Connectés

1. Bouton "Changer de pays" dans les paramètres
2. Efface le localStorage
3. Relance le processus de sélection

## ⚠️ POINTS D'ATTENTION

1. **Pas de données globales** - Aucune vue "monde entier"
2. **Pas de fuite entre pays** - Les statistiques sont isolées
3. **Conformité RGPD** - Les données restent dans leur juridiction
4. **Performance** - Les partitions Cassandra par pays optimisent les requêtes

## 🎯 CRITÈRES D'ACCEPTATION

- [ ] Impossible d'utiliser l'app sans pays défini
- [ ] Toutes les API vérifient le header X-Country-Code
- [ ] Les expressions sont filtrées par pays côté backend
- [ ] Le changement de pays vide le cache local
- [ ] Les tests vérifient l'isolation des données
- [ ] Documentation utilisateur sur la souveraineté