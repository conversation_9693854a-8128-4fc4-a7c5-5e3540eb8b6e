# Updated PillarScan Roadmap - With Person Linking & AI Classification

## Executive Summary

PillarScan expressions will now support:

1. **Person Linking**: Link expressions to Physical persons, Moral entities (organizations), and Groups
2. **AI Classification**: Automatic categorization into the 4-level pillar hierarchy
3. **Sentiment Analysis**: Expression type/mood mapping with aggregation values
4. **Background Processing**: Async person resolution and classification

## Updated Phase 1: "First Expression with Intelligence" (6 weeks)

### Week 1-2: Original MVP Features ✅

- [x] Onboarding flow
- [x] Basic expression creation
- [x] Profile & gamification
- [x] Community feed

### Week 3-4: Person Linking System 🆕

- [ ] **Person Mention UI**

  - @ mentions in expression text
  - Person type selector (Physical/Moral/Group)
  - Search existing persons
  - Create temporary person references

- [ ] **Person Resolution Backend**
  - Search algorithm with fuzzy matching
  - Background task for person creation
  - Link expressions to resolved persons
  - Track resolution status

### Week 5-6: AI Classification System 🆕

- [ ] **Classification Infrastructure**

  - Amazon Comprehend integration
  - Custom pillar classification model
  - Real-time preview API
  - Confidence scoring

- [ ] **UI Components**
  - ClassificationPreview component
  - Manual override interface
  - Pillar hierarchy browser
  - Sentiment visualization

## Updated Architecture

### Data Model Changes

```python
# Expression Model (Cassandra)
class PillarScanExpression:
    # Original fields
    expression_id = UUID
    text = Text
    mood = Text  # Maps to expression_type

    # New fields for person linking
    mentioned_persons = Map(UUID, Text)  # temp_id -> person details JSON
    resolved_persons = Map(UUID, UUID)   # temp_id -> person_id mapping

    # New fields for classification
    ai_classification = Text  # JSON with full classification result
    classification_confidence = Float
    category_code = Text      # From hierarchy
    subcategory_code = Text   # From hierarchy
    topic_code = Text         # From hierarchy
    expression_type_code = Text  # Sentiment-based type

    # Manual overrides
    manual_classification = Boolean
    manual_category = Text
    manual_subcategory = Text
    manual_topic = Text
```

### Service Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Frontend UI   │────▶│  Django API     │────▶│ Classification  │
│                 │     │                 │     │    Service      │
│ - PersonMention │     │ - Expression    │     │                 │
│ - Classification│     │   Creation      │     │ - Amazon        │
│   Preview       │     │ - Person Search │     │   Comprehend    │
│ - Manual        │     │ - Queue Tasks   │     │ - Custom Model  │
│   Override      │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │
                               ▼
                    ┌─────────────────┐
                    │  Celery Tasks   │
                    │                 │
                    │ - Classify      │
                    │ - Resolve       │
                    │   Persons       │
                    │ - Enrich        │
                    └─────────────────┘
```

## Expression Creation Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant AI
    participant Tasks
    participant DB

    User->>Frontend: Write expression with @mentions
    Frontend->>Frontend: Detect persons & show preview
    Frontend->>API: Submit expression
    API->>DB: Save with temp person refs
    API->>Tasks: Queue classification
    API->>Tasks: Queue person resolution
    API-->>Frontend: Return expression ID

    Tasks->>AI: Classify text
    AI-->>Tasks: Return classification
    Tasks->>DB: Update expression

    Tasks->>DB: Search persons
    alt Person not found
        Tasks->>DB: Create new person
    end
    Tasks->>DB: Update expression links

    Frontend->>API: Poll for updates
    API-->>Frontend: Return classified expression
```

## Mood to Expression Type Mapping

```typescript
const moodToExpressionType = {
  frustrated: {
    expression_type_code: 'denunciation',
    aggregation_value: -1,
    sentiment: 'NEGATIVE',
  },
  happy: {
    expression_type_code: 'appreciation',
    aggregation_value: 1,
    sentiment: 'POSITIVE',
  },
  idea: {
    expression_type_code: 'suggestion',
    aggregation_value: 1,
    sentiment: 'POSITIVE',
  },
  question: {
    expression_type_code: 'citizen-consultation',
    aggregation_value: 0,
    sentiment: 'NEUTRAL',
  },
};
```

## Pillar Hierarchy Example

```
Domain: "wellbeing-social"
  └── Category: "health"
      └── SubCategory: "mental-health"
          └── Topic: "stress-management"
              └── Expression Type: "denunciation" (frustrated mood)
```

## API Changes

### New Endpoints

```typescript
// Person search with autocomplete
GET /api/v2/civicperson/search/?q=maire&type=moral&limit=10

// Create expression with classification
POST /api/v2/pillarscan/expressions/create-classified/
{
  "text": "Le @[Maire de Paris](moral) devrait améliorer les pistes cyclables",
  "mood": "idea",
  "mentioned_persons": [{
    "temp_id": "uuid-1",
    "person_name": "Maire de Paris",
    "person_type": "moral",
    "role": "target"
  }]
}

// Get classification preview
POST /api/v2/pillarscan/classify-preview/
{
  "text": "Les transports sont trop chers"
}

// Manual classification override
PATCH /api/v2/pillarscan/expressions/{id}/classification/
{
  "category": "mobility-transport",
  "subcategory": "public-transport",
  "topic": "pricing"
}
```

## Implementation Priority

### Phase 1 (Must Have)

1. Basic person mention (text storage)
2. Simple classification (1 level)
3. Mood to expression type mapping
4. Manual pillar selection

### Phase 2 (Should Have)

1. Person search & resolution
2. Full AI classification
3. Real-time preview
4. Background processing

### Phase 3 (Nice to Have)

1. Advanced entity extraction
2. Multi-language support
3. Classification learning
4. Batch processing

## Success Metrics

### Technical KPIs

- Classification accuracy: > 85%
- Person resolution rate: > 90%
- Processing time: < 30 seconds
- API latency: < 200ms

### User KPIs

- Expressions with persons: > 40%
- Manual override rate: < 10%
- Classification satisfaction: > 4/5
- Feature adoption: > 60%

## Development Timeline

**Week 1**: Backend models & services setup
**Week 2**: Frontend person mention UI
**Week 3**: AI classification integration
**Week 4**: Background tasks & processing
**Week 5**: Testing & optimization
**Week 6**: Deployment & monitoring

Total: 6 weeks for complete implementation
