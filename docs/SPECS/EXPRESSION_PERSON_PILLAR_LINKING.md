# Expression-Person-Pillar Linking System Documentation

## Overview

This document describes the complete system for linking PillarScan expressions to:

1. **Persons** (Physical, Moral, Group entities)
2. **Pillars** (Hierarchical classification system)
3. **Expression Types** (Sentiments/moods)

## Architecture

### 1. Person Linking System

#### Person Types

```typescript
enum PersonType {
  HUMAN = 'physical', // Individual persons
  MORAL = 'moral', // Organizations, companies
  GROUP = 'group', // Groups of persons
}

interface PersonReference {
  person_id?: string; // UUID if person exists in DB
  person_code?: string; // Unique identifier
  person_name: string; // Display name (required)
  person_type: PersonType;
  role: 'target' | 'source' | 'owner';
  // For unresolved persons (not yet in DB)
  temp_id?: string; // Temporary ID for frontend tracking
  needs_resolution: boolean;
}
```

#### Person Resolution Workflow

```mermaid
graph TD
    A[User mentions person] --> B{Person exists?}
    B -->|Yes| C[Link with person_id]
    B -->|No| D[Store temp reference]
    D --> E[Background task]
    E --> F{Search existing}
    F -->|Found| G[Update expression with person_id]
    F -->|Not found| H[Create new person]
    H --> G
```

### 2. Pillar Classification System

#### Hierarchy Structure

```
Domain (3) → Category (12) → SubCategory (107) → Topic (N)
```

#### Classification Levels

1. **Domain**: Top-level grouping (education, economy, wellbeing)
2. **Category**: The 12 main pillars
3. **SubCategory**: Specific areas within pillars
4. **Topic**: Finest granularity for classification

#### Expression Type (4th level)

- Sentiment/mood classification
- Values: `frustrated`, `happy`, `idea`, `question`
- Maps to expression types in DB with aggregation values

### 3. AI Classification System

#### Amazon Comprehend Integration

```typescript
interface ClassificationRequest {
  text: string;
  language: 'fr' | 'en';
  context?: {
    mentioned_persons: string[];
    location?: GeoPoint;
    user_history?: string[];
  };
}

interface ClassificationResult {
  // Pillar classification
  category_scores: { [key: string]: number };
  subcategory_scores: { [key: string]: number };
  topic_scores: { [key: string]: number };

  // Sentiment analysis
  sentiment: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL' | 'MIXED';
  sentiment_scores: {
    positive: number;
    negative: number;
    neutral: number;
    mixed: number;
  };

  // Entity recognition
  entities: {
    persons: DetectedPerson[];
    organizations: DetectedOrganization[];
    locations: DetectedLocation[];
  };

  confidence: number;
}
```

## Implementation Plan

### Frontend Tasks

#### 1. Expression Creation Form Enhancement

```typescript
interface ExpressionFormData {
  // Basic fields
  text: string;
  mood: 'frustrated' | 'happy' | 'idea' | 'question';

  // Person mentions
  mentioned_persons: PersonReference[];

  // Manual classification (optional)
  suggested_category?: string;
  suggested_subcategory?: string;
  suggested_topic?: string;

  // Auto-detected entities
  detected_entities?: {
    persons: string[];
    organizations: string[];
  };
}
```

#### 2. Person Mention Component

```tsx
// Component for mentioning persons in expressions
<PersonMention
  onPersonSelect={(person: PersonReference) => void}
  searchPersons={(query: string) => Promise<Person[]>}
  createTempPerson={(name: string, type: PersonType) => PersonReference}
/>
```

#### 3. Classification Preview

```tsx
// Real-time classification preview
<ClassificationPreview
  text={expression.text}
  onClassification={(result: ClassificationResult) => void}
  showConfidence={true}
/>
```

### Backend Tasks

#### 1. Expression Creation Endpoint Enhancement

```python
# pillar_scan/api/v2/views.py
class PillarScanExpressionViewSet(viewsets.ModelViewSet):
    @action(detail=False, methods=['post'])
    def create_with_classification(self, request):
        """
        Create expression with:
        1. Person references (resolved and unresolved)
        2. AI classification
        3. Manual overrides
        """
        # Step 1: Validate input
        # Step 2: Create expression
        # Step 3: Queue classification task
        # Step 4: Queue person resolution tasks
        # Step 5: Return expression with temp classifications
```

#### 2. Background Tasks (Celery)

```python
# pillar_scan/tasks.py

@shared_task
def classify_expression(expression_id: str):
    """
    1. Call Amazon Comprehend for text analysis
    2. Extract entities and sentiment
    3. Classify into pillar hierarchy
    4. Update expression with results
    """

@shared_task
def resolve_person_mentions(expression_id: str, person_refs: List[dict]):
    """
    For each unresolved person:
    1. Search existing persons by name/type
    2. Use fuzzy matching for similar names
    3. Create new person if not found
    4. Update expression with resolved person_ids
    """

@shared_task
def enrich_expression_metadata(expression_id: str):
    """
    1. Analyze expression context
    2. Extract key phrases
    3. Generate tags
    4. Calculate impact scores
    """
```

#### 3. AI Service Integration

```python
# pillar_scan/services/ai_classification.py

class AIClassificationService:
    def __init__(self):
        self.comprehend = boto3.client('comprehend')
        self.custom_model = self._load_pillar_model()

    def classify_expression(self, text: str, language: str = 'fr'):
        # Amazon Comprehend analysis
        sentiment = self.comprehend.detect_sentiment(
            Text=text,
            LanguageCode=language
        )

        entities = self.comprehend.detect_entities(
            Text=text,
            LanguageCode=language
        )

        # Custom pillar classification
        pillar_scores = self.custom_model.predict(text)

        return ClassificationResult(
            sentiment=sentiment,
            entities=entities,
            pillar_scores=pillar_scores
        )
```

### Database Schema Updates

#### 1. Expression Model Extensions

```python
# In Cassandra models
class PillarScanExpression(Model):
    # Existing fields...

    # Person references
    mentioned_persons = columns.Map(
        columns.UUID,  # temp_id or person_id
        columns.Text   # JSON with person details
    )

    # Classification
    ai_classification = columns.Text()  # JSON result
    classification_confidence = columns.Float()
    classification_timestamp = columns.DateTime()

    # Manual overrides
    manual_category = columns.Text()
    manual_subcategory = columns.Text()
    manual_topic = columns.Text()
```

#### 2. Person Resolution Tracking

```python
class PersonResolutionTask(Model):
    __table_name__ = 'person_resolution_tasks'

    task_id = columns.UUID(primary_key=True)
    expression_id = columns.UUID(index=True)
    temp_person_id = columns.UUID()
    person_name = columns.Text()
    person_type = columns.Text()
    status = columns.Text()  # pending, resolved, failed
    resolved_person_id = columns.UUID()
    created_at = columns.DateTime()
    resolved_at = columns.DateTime()
```

## API Endpoints

### 1. Expression Creation with Classification

```
POST /api/v2/pillarscan/expressions/create-classified/
{
  "text": "Le maire de Paris devrait améliorer les pistes cyclables",
  "mood": "idea",
  "mentioned_persons": [
    {
      "person_name": "Maire de Paris",
      "person_type": "moral",
      "role": "target",
      "needs_resolution": true
    }
  ],
  "location": { "lat": 48.8566, "lng": 2.3522 }
}
```

### 2. Person Search/Autocomplete

```
GET /api/v2/civicperson/search/?q=maire&type=moral&limit=10
```

### 3. Classification Preview

```
POST /api/v2/pillarscan/classify-preview/
{
  "text": "Les transports en commun sont trop chers",
  "language": "fr"
}
```

### 4. Manual Classification Override

```
PATCH /api/v2/pillarscan/expressions/{id}/classification/
{
  "category": "mobility-transport",
  "subcategory": "public-transport",
  "topic": "transport-pricing"
}
```

## Frontend Components Structure

```
components/pillarscan/
├── expression/
│   ├── ExpressionForm.tsx          # Main form
│   ├── MoodSelector.tsx            # Sentiment selector
│   ├── PersonMention.tsx           # Person mention UI
│   ├── PersonSearchModal.tsx       # Search existing persons
│   ├── ClassificationPreview.tsx   # Real-time classification
│   └── PillarSelector.tsx          # Manual pillar selection
├── classification/
│   ├── PillarHierarchy.tsx         # Visual hierarchy browser
│   ├── ConfidenceIndicator.tsx     # AI confidence display
│   └── ClassificationHistory.tsx   # User's classification patterns
└── services/
    ├── classificationService.ts     # AI classification calls
    ├── personService.ts            # Person search/creation
    └── expressionService.ts        # Enhanced expression API
```

## Workflow Example

1. **User writes expression**: "Le directeur de Carrefour devrait baisser les prix"

2. **Real-time detection**:

   - Detects "directeur de Carrefour" as organization reference
   - Shows classification preview: Economy → Consumer goods → Pricing

3. **User confirms/adjusts**:

   - Confirms Carrefour detection or selects from search
   - Can override suggested classification

4. **On submit**:

   - Expression created with temporary person reference
   - Background task searches for Carrefour in Person database
   - AI classification runs for final categorization

5. **Post-processing**:
   - Person resolved or created
   - Expression updated with final classification
   - User notified of processing completion

## Configuration

### Environment Variables

```env
# AI Services
AWS_REGION=eu-west-1
AWS_ACCESS_KEY_ID=xxx
AWS_SECRET_ACCESS_KEY=xxx
COMPREHEND_ENDPOINT=xxx

# Classification
CLASSIFICATION_CONFIDENCE_THRESHOLD=0.7
CLASSIFICATION_LANGUAGE_DEFAULT=fr

# Person Resolution
PERSON_SEARCH_FUZZY_THRESHOLD=0.8
PERSON_AUTO_CREATE_ENABLED=true
```

### Feature Flags

```typescript
const features = {
  aiClassification: true,
  personMentions: true,
  manualClassificationOverride: true,
  realtimePreview: true,
  batchProcessing: false,
};
```

## Testing Strategy

1. **Unit Tests**:

   - Classification service mocks
   - Person resolution logic
   - Entity extraction

2. **Integration Tests**:

   - End-to-end expression creation
   - Background task processing
   - API endpoint responses

3. **E2E Tests**:
   - Complete user workflow
   - Person mention and search
   - Classification preview and override

## Performance Considerations

1. **Caching**:

   - Cache person search results
   - Cache classification models
   - Cache pillar hierarchy

2. **Batch Processing**:

   - Queue similar classifications
   - Batch person resolutions
   - Aggregate API calls

3. **Optimization**:
   - Debounce classification preview
   - Lazy load person search
   - Progressive classification (simple → detailed)

## Security & Privacy

1. **Person Data**:

   - Only public persons searchable
   - Private persons require permission
   - GDPR compliance for person data

2. **Classification Data**:

   - Anonymize training data
   - Secure AI model storage
   - Audit classification changes

3. **Access Control**:
   - Role-based classification override
   - Person creation permissions
   - Expression ownership rules
