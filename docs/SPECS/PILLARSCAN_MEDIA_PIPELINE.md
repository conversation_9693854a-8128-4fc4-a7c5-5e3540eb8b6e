# Pipeline Media PillarScan - Architecture Professionnelle

## Vue d'ensemble

Pipeline complet de gestion des médias pour PillarScan, inspiré de l'architecture social_monitor/mediahub.

## 1. Architecture du Pipeline

### A. Upload Pipeline
```
PillarScan Frontend
    ↓ (1) Create Expression + File Upload
API Gateway
    ↓ (2) Save Expression (sans media)
Cassandra (PillarScanExpression)
    ↓ (3) Upload Media to MinIO
MinIO Storage (pillarscan-medias bucket)
    ↓ (4) Create MediaMetadata
Cassandra (MediaMetadata)
    ↓ (5) Enrich Expression with media_refs
PillarScanEnrichmentService
    ↓ (6) Update Expression
Expression avec media_refs ✓
```

### B. Display Pipeline
```
PillarScan Frontend Request
    ↓ (1) Get Expression with media_refs
API Gateway
    ↓ (2) Load MediaMetadata
MediaReadService (avec cache)
    ↓ (3) Generate Presigned URLs
MinIO Service
    ↓ (4) Return enriched data
API Response avec URLs
    ↓ (5) Display
Frontend avec images ✓
```

## 2. Implémentation Backend

### A. Models Cassandra

```python
# Dans pillarscan/models.py
class PillarScanExpression(DjangoCassandraModel):
    # ... existing fields ...
    media_refs = columns.Map(
        key_type=columns.Text,
        value_type=columns.UUID,
        default=dict
    )
    has_media = columns.Boolean(default=False)
    media_processing_status = columns.Text(default='pending')
```

### B. Service d'Upload

```python
# pillarscan/services/media_upload_service.py
class PillarScanMediaUploadService:
    def __init__(self):
        self.minio_service = MinIOService()
        self.media_metadata_service = MediaMetadataService()
        
    async def process_expression_with_media(
        self,
        expression_data: dict,
        media_files: List[UploadFile],
        person_id: str
    ) -> PillarScanExpression:
        # 1. Créer l'expression sans médias
        expression = await self.create_expression(expression_data)
        
        # 2. Upload des médias en parallèle
        media_tasks = [
            self.upload_media(file, expression.expression_id, person_id)
            for file in media_files
        ]
        media_results = await asyncio.gather(*media_tasks)
        
        # 3. Enrichir l'expression
        await self.enrich_expression_with_media(expression, media_results)
        
        return expression
```

### C. Service d'Enrichissement

```python
# pillarscan/services/enrichment_service.py
class PillarScanEnrichmentService:
    def enrich_expression_with_media(
        self,
        expression: PillarScanExpression,
        media_metadata_list: List[MediaMetadata]
    ):
        media_refs = {}
        for idx, media in enumerate(media_metadata_list):
            role = f"image_{idx + 1}" if idx < 3 else f"extra_{idx - 2}"
            media_refs[role] = media.media_id
            
        expression.media_refs = media_refs
        expression.has_media = True
        expression.media_processing_status = 'completed'
        expression.save()
```

### D. Service de Lecture

```python
# pillarscan/services/media_read_service.py
class PillarScanMediaReadService:
    @cached(ttl=300)  # Cache 5 minutes
    async def get_expression_with_media_urls(
        self,
        expression_id: str
    ) -> dict:
        expression = PillarScanExpression.objects.get(
            expression_id=expression_id
        )
        
        if not expression.has_media:
            return expression.to_dict()
            
        # Charger les métadonnées
        media_ids = list(expression.media_refs.values())
        media_metadatas = MediaMetadata.objects.filter(
            media_id__in=media_ids
        )
        
        # Générer les URLs présignées
        media_urls = {}
        for role, media_id in expression.media_refs.items():
            metadata = next(
                (m for m in media_metadatas if m.media_id == media_id),
                None
            )
            if metadata:
                url = self.minio_service.get_presigned_url(
                    bucket_name=metadata.bucket_name,
                    object_name=metadata.file_path,
                    expiry=3600  # 1 heure
                )
                media_urls[role] = {
                    'id': str(media_id),
                    'url': url,
                    'type': metadata.file_type,
                    'size': metadata.size
                }
                
        return {
            **expression.to_dict(),
            'media_urls': media_urls
        }
```

## 3. API Endpoints

### A. Upload avec médias
```python
# POST /api/v2/expressions/create-with-media/
@api_view(['POST'])
@parser_classes([MultiPartParser])
async def create_expression_with_media(request):
    # Extraire les données
    expression_data = json.loads(request.data.get('expression'))
    files = request.FILES.getlist('media')
    
    # Valider
    if len(files) > 3:
        return Response(
            {'error': 'Maximum 3 images autorisées'},
            status=400
        )
    
    # Processer
    service = PillarScanMediaUploadService()
    expression = await service.process_expression_with_media(
        expression_data,
        files,
        request.user.person_id
    )
    
    # Retourner avec URLs
    read_service = PillarScanMediaReadService()
    enriched = await read_service.get_expression_with_media_urls(
        expression.expression_id
    )
    
    return Response(enriched, status=201)
```

### B. Lecture avec médias
```python
# GET /api/v2/expressions/?with_media=true
@api_view(['GET'])
async def list_expressions(request):
    with_media = request.query_params.get('with_media') == 'true'
    
    expressions = PillarScanExpression.objects.filter(
        is_published=True
    ).limit(20)
    
    if not with_media:
        return Response([e.to_dict() for e in expressions])
    
    # Enrichir avec URLs
    read_service = PillarScanMediaReadService()
    tasks = [
        read_service.get_expression_with_media_urls(e.expression_id)
        for e in expressions
    ]
    enriched = await asyncio.gather(*tasks)
    
    return Response(enriched)
```

## 4. Adaptation Frontend

### A. Service API mis à jour
```typescript
// lib/api/client.ts
async createExpressionWithMedia(
  data: CreateExpressionRequest,
  files: File[]
): Promise<PillarScanExpression> {
  const formData = new FormData();
  formData.append('expression', JSON.stringify(data));
  
  files.forEach((file, index) => {
    formData.append('media', file);
  });
  
  const response = await fetch(
    `${this.baseUrl}/api/v2/expressions/create-with-media/`,
    {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: formData
    }
  );
  
  if (!response.ok) throw new Error('Erreur création');
  return response.json();
}

async getExpressions(params?: {
  with_media?: boolean;
  // ... autres params
}): Promise<PillarScanExpression[]> {
  const queryParams = new URLSearchParams();
  if (params?.with_media) queryParams.append('with_media', 'true');
  
  // ... reste de l'implémentation
}
```

### B. Types mis à jour
```typescript
interface PillarScanExpression {
  // ... champs existants ...
  media_refs?: Record<string, string>;
  has_media?: boolean;
  media_urls?: Record<string, {
    id: string;
    url: string;
    type: string;
    size: number;
  }>;
}
```

### C. Composant ExpressionCard adapté
```typescript
// Utiliser media_urls au lieu de media_refs
{expression.media_urls && Object.keys(expression.media_urls).length > 0 && (
  <div className="grid gap-2">
    {Object.entries(expression.media_urls)
      .filter(([role]) => role.startsWith('image_'))
      .slice(0, 3)
      .map(([role, media]) => (
        <Image
          key={media.id}
          src={media.url}
          alt={`Image ${role}`}
          // ...
        />
      ))}
  </div>
)}
```

## 5. Configuration MinIO

### Bucket Structure
```
pillarscan-medias/
├── expressions/
│   ├── {year}/
│   │   ├── {month}/
│   │   │   ├── {expression_id}/
│   │   │   │   ├── image_1.jpg
│   │   │   │   ├── image_2.jpg
│   │   │   │   └── image_3.jpg
```

### Politique CORS
```json
{
  "Version": "2012-10-17",
  "Statement": [{
    "Effect": "Allow",
    "Principal": {"AWS": ["*"]},
    "Action": ["s3:GetObject"],
    "Resource": ["arn:aws:s3:::pillarscan-medias/*"]
  }]
}
```

## 6. Optimisations

### A. Upload
- Upload parallèle des fichiers
- Compression côté serveur si nécessaire
- Génération de thumbnails asynchrone

### B. Lecture
- Cache des URLs présignées (5 min)
- Batch loading des métadonnées
- CDN pour distribution globale

### C. Monitoring
- Logs structurés pour chaque étape
- Métriques de performance
- Alertes sur erreurs

## 7. Gestion des erreurs

### Rollback en cas d'échec
```python
try:
    # Upload pipeline
except Exception as e:
    # Nettoyer MinIO
    # Supprimer métadonnées
    # Marquer expression comme failed
    # Logger l'erreur
    raise
```

## Conclusion

Ce pipeline professionnel assure :
- ✅ Séparation des préoccupations
- ✅ Scalabilité horizontale
- ✅ Performance optimale
- ✅ Résilience aux pannes
- ✅ Traçabilité complète