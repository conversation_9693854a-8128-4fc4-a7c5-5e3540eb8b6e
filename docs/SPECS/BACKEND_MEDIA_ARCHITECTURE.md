# Architecture Backend Média - PillarScan

## Vue d'ensemble

L'architecture média de PillarScan suit un pipeline professionnel inspiré de `social_monitor` et `mediahub` du backend Django, adapté pour Next.js.

## Pipeline de traitement

```mermaid
graph LR
    A[Client Upload] --> B[API Route /api/upload]
    B --> C[Validation]
    C --> D[MinIO Storage]
    D --> E[Enrichment Queue]
    E --> F[Processing Tasks]
    F --> G[Results Storage]
    G --> H[Client Display]
    
    F --> F1[Thumbnail Generation]
    F --> F2[Metadata Extraction]
    F --> F3[NSFW Check]
    F --> F4[OCR]
    F --> F5[Object Detection]
```

## 1. Upload API (`/api/upload`)

### Endpoint
```typescript
POST /api/upload
Content-Type: multipart/form-data
Authorization: Bearer <token>

Body:
- files: File[] (multiple files supported)
```

### Response
```json
{
  "success": true,
  "files": [
    {
      "media_id": "uuid-v4",
      "bucket_name": "pillarscan-media",
      "file_path": "expressions/images/uuid.jpg",
      "content_type": "image/jpeg",
      "size": 1234567,
      "url": "https://minio.example.com/pillarscan-media/expressions/images/uuid.jpg",
      "thumbnail_url": "https://minio.example.com/pillarscan-media/expressions/thumbnails/uuid_thumb.jpg",
      "metadata": {
        "width": 1920,
        "height": 1080
      }
    }
  ],
  "message": "1 fichier(s) téléversé(s) avec succès"
}
```

### Validation
- Types autorisés : `image/jpeg`, `image/png`, `image/gif`, `image/webp`, `video/mp4`, `video/webm`
- Taille max : 10MB (images), 100MB (vidéos)
- Authentification requise

## 2. Service d'enrichissement

### MediaEnrichmentService
Service Node.js qui gère le traitement asynchrone des médias après upload.

#### Tâches d'enrichissement
1. **Génération de miniatures**
   - Images : 200x200, 400x400, 800x800
   - Vidéos : Frame à 10% de la durée

2. **Extraction de métadonnées**
   - Images : dimensions, format, espace couleur, EXIF
   - Vidéos : durée, résolution, codec, bitrate

3. **Vérification NSFW**
   - Utilise un modèle ML pour détecter le contenu inapproprié
   - Score de 0 à 1 (1 = sûr)

4. **OCR (images uniquement)**
   - Extraction de texte avec Tesseract
   - Support multilingue

5. **Détection d'objets**
   - Identification d'objets et personnes
   - Labels avec scores de confiance

### API de statut
```typescript
GET /api/media/enrichment/{mediaId}
Authorization: Bearer <token>

Response:
{
  "success": true,
  "task": {
    "media_id": "uuid",
    "status": "processing", // pending | processing | completed | failed
    "tasks": {
      "thumbnail": true,
      "ocr": true,
      // ...
    },
    "results": {
      "thumbnail_url": "...",
      "text_content": "...",
      // ...
    },
    "created_at": "2025-05-23T10:00:00Z",
    "updated_at": "2025-05-23T10:00:30Z"
  }
}
```

## 3. Configuration MinIO

### Variables d'environnement
```env
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=pillarscan-media
MINIO_SECURE=false
```

### Structure des buckets
```
pillarscan-media/
├── expressions/
│   ├── images/
│   │   └── {media_id}.{ext}
│   ├── videos/
│   │   └── {media_id}.{ext}
│   └── thumbnails/
│       └── {media_id}_thumb.jpg
├── profiles/
│   ├── avatars/
│   └── covers/
└── temp/
    └── {session_id}/
```

## 4. Intégration avec le frontend

### Upload de médias
```typescript
// Dans le composant de création d'expression
const { mutate: createExpression } = useMutation({
  mutationFn: async ({ data, files }) => {
    return pillarScanAPI.createExpressionWithMedia(data, files);
  }
});
```

### Suivi de l'enrichissement
```typescript
// Hook pour suivre le statut
function useMediaEnrichment(mediaId: string) {
  const [status, setStatus] = useState(null);
  
  useEffect(() => {
    const checkStatus = async () => {
      const response = await fetch(`/api/media/enrichment/${mediaId}`);
      const data = await response.json();
      setStatus(data.task);
    };
    
    const interval = setInterval(checkStatus, 2000);
    return () => clearInterval(interval);
  }, [mediaId]);
  
  return status;
}
```

## 5. Sécurité

### Authentification
- Tous les endpoints nécessitent un JWT valide
- Vérification des permissions par utilisateur

### Validation
- Vérification stricte des types MIME
- Scan antivirus (en production)
- Limitation du taux de requêtes

### Stockage
- URLs signées avec expiration (4h)
- Chiffrement au repos dans MinIO
- Backup automatique quotidien

## 6. Optimisations

### Cache
- CDN CloudFlare pour les médias publics
- Cache navigateur : 7 jours
- Cache serveur : 30 jours

### Performance
- Traitement asynchrone via queue
- Redimensionnement adaptatif
- Lazy loading côté client

### Monitoring
- Métriques de performance
- Alertes sur les échecs
- Dashboard de suivi

## 7. Migration depuis le mock

Pour passer du mock actuel au vrai backend :

1. **Déployer MinIO**
   ```bash
   docker run -p 9000:9000 -p 9001:9001 \
     -e MINIO_ROOT_USER=minioadmin \
     -e MINIO_ROOT_PASSWORD=minioadmin \
     minio/minio server /data --console-address ":9001"
   ```

2. **Configurer les buckets**
   ```bash
   mc alias set myminio http://localhost:9000 minioadmin minioadmin
   mc mb myminio/pillarscan-media
   mc policy set public myminio/pillarscan-media/expressions
   ```

3. **Installer les dépendances**
   ```bash
   npm install minio sharp tesseract.js @tensorflow/tfjs
   ```

4. **Activer le code réel**
   - Remplacer les simulations par les vraies implémentations
   - Configurer les workers de traitement
   - Mettre en place le monitoring

## 8. Évolutions futures

1. **IA générative**
   - Génération d'images avec DALL-E
   - Amélioration automatique des images
   - Suggestions de contenu

2. **Vidéo avancée**
   - Transcription automatique
   - Chapitrage intelligent
   - Compression adaptative

3. **Collaboration**
   - Édition collaborative
   - Versions et historique
   - Commentaires sur médias