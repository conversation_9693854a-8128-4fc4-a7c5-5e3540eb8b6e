# 📚 Organisation de la Documentation

## Structure des Répertoires

### 📁 `/docs/avancements/`

Documents de suivi de progression et d'avancement du projet :

- Documents d'avancement par date et fonctionnalité
- Rapports de progression des tests
- États d'avancement des features

### 📁 `/docs/corrections/`

Documents relatant les corrections et fixes appliqués :

- Corrections de bugs
- Fixes de contraste et accessibilité
- Résolutions de problèmes spécifiques

### 📁 `/docs/DONE/`

Tâches et fonctionnalités complétées avec succès

### 📁 `/docs/guides/`

Guides utilisateur et documentation d'utilisation

### 📁 `/docs/manifest/`

Système de manifeste anti-régression :

- `README.md` : Guide d'utilisation du système
- `SPEC-MANIFESTE-NON-REGRESSION.md` : Spécification complète
- `manifest-v*.json` : Fichiers de manifeste versionnés

### 📁 `/docs/SPECS/`

Spécifications techniques et fonctionnelles :

- Architecture et design
- Spécifications d'API
- Roadmaps et planification

### 📁 `/docs/STATUS/`

Documents d'état et de statut du projet

### 📁 `/docs/technical/`

Documentation technique approfondie

### 📁 `/docs/TODO/`

Listes de tâches et planification :

- TODOs actuels par session
- Priorisation des tâches
- Backlogs

## Conventions de Nommage

- **MAJUSCULES** : Documents importants ou de référence
- **Date dans le nom** : Pour les documents temporels (ex: `AVANCEMENT-2025-05-25-*.md`)
- **Préfixes descriptifs** :
  - `SPEC-` : Spécifications
  - `AVANCEMENT-` : Progression
  - `TODO-` : Tâches
  - `GUIDE-` : Guides utilisateur

## Navigation Rapide

- 🚀 [Derniers avancements](./avancements/)
- 🐛 [Corrections récentes](./corrections/)
- 📋 [TODOs actuels](./TODO/)
- 🔧 [Spécifications techniques](./SPECS/)
- 📊 [Système de manifeste](./manifest/)

## Mise à Jour

Cette organisation a été mise en place le 25/05/2025 pour améliorer la navigabilité et la maintenance de la documentation du projet.
