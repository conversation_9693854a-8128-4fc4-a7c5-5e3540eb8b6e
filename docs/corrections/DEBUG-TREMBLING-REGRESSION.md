# Debug - Régression des Tremblements

**Date**: 24/05/2025
**Status**: EN COURS DE DEBUG

## Problème

Les tremblements de la page sont revenus après les corrections précédentes.

## Actions de Debug

### 1. Désactivation des Animations de Fond

```typescript
// ThemedLayout.tsx - Ligne 99
{false && (
  // Blobs animés désactivés temporairement
)}
```

**Raison**: Les animations continues peuvent causer des re-renders et affecter la hauteur du viewport.

### 2. Désactivation du Scroll Infini

```typescript
// ExpressionFeed.tsx - Ligne 121
// TEMPORAIREMENT DÉSACTIVÉ POUR DEBUG
/*
useEffect(() => {
  if (inView && hasMore && !loadingMore && !loading) {
    // Intersection Observer désactivé
  }
});
*/
```

**Raison**: L'Intersection Observer peut se déclencher en boucle si la hauteur du contenu change constamment.

### 3. Bouton "Charger Plus" Manuel

Remplacé le scroll infini automatique par un bouton manuel pour isoler le problème.

## Hypothèses

1. **Boucle de Re-render**:
   - L'Intersection Observer se déclenche → charge du contenu → change la hauteur → déclenche à nouveau
2. **Service Worker PWA**:
   - Peut intercepter les requêtes et causer des délais qui affectent le rendu
3. **Animations Framer Motion**:
   - Les animations continues (blobs) peuvent forcer des re-layouts

## Tests à Effectuer

1. **Sans Service Worker**:

   ```bash
   # Dans Chrome DevTools
   Application → Service Workers → Unregister
   ```

2. **Sans Animations**:

   - Les blobs sont déjà désactivés
   - Tester si le tremblement persiste

3. **Avec Bouton Manuel**:
   - Le scroll infini est désactivé
   - Utiliser le bouton "Charger plus"

## Solution Permanente (À Implémenter)

1. **Debounce plus agressif** sur l'Intersection Observer
2. **Virtualisation** de la liste d'expressions
3. **Skeleton loaders** de hauteur fixe
4. **Désactiver les animations** sur les appareils moins performants

## Commandes Utiles

```bash
# Voir les Service Workers actifs
node scripts/disable-service-worker.js

# Diagnostiquer le layout
node scripts/diagnose-layout-issue.js
```
