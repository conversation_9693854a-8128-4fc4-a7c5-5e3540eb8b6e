# TODO: Implémenter l'endpoint Media Upload Backend

## Contexte

L'upload d'images est actuellement implémenté côté frontend mais utilise un mock temporaire car l'endpoint backend n'existe pas encore.

## Endpoint requis

### POST `/api/v2/media/upload/`

#### Request
- **Method**: POST
- **Content-Type**: multipart/form-data
- **Auth**: Bearer token requis
- **Body**: 
  ```
  file: <binary file data>
  ```

#### Response (200 OK)
```json
{
  "id": "uuid-du-media",
  "url": "https://cdn.example.com/media/uuid-du-media.jpg"
}
```

#### Erreurs possibles
- 400: Type de fichier non supporté
- 413: Fichier trop volumineux
- 401: Non authentifié
- 500: Erreur serveur

## Implémentation suggérée

### 1. Dans `mediahub/urls_api.py`
```python
from django.urls import path
from . import views

urlpatterns = [
    path('upload/', views.MediaUploadView.as_view(), name='media-upload'),
]
```

### 2. Dans `mediahub/views.py`
```python
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .services import MinioService

class MediaUploadView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        file = request.FILES.get('file')
        if not file:
            return Response(
                {'error': 'Aucun fichier fourni'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validation du type MIME
        allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
        if file.content_type not in allowed_types:
            return Response(
                {'error': 'Type de fichier non supporté'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validation de la taille (5MB max)
        if file.size > 5 * 1024 * 1024:
            return Response(
                {'error': 'Fichier trop volumineux (max 5MB)'}, 
                status=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE
            )
        
        try:
            # Upload vers MinIO
            minio_service = MinioService()
            media_id = minio_service.upload_file(
                file=file,
                bucket='pillarscan-media',
                person_id=request.user.person_id
            )
            
            # Récupérer l'URL publique
            media_url = minio_service.get_public_url(media_id)
            
            return Response({
                'id': str(media_id),
                'url': media_url
            })
            
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
```

### 3. Router l'endpoint dans `smatflow_membership/urls.py`
```python
path('api/v2/media/', include('mediahub.urls_api')),
```

## Code frontend à réactiver

Une fois l'endpoint implémenté, supprimer le mock dans `/lib/api/client.ts` et réactiver le code commenté.

## Tests à ajouter

1. Test d'upload réussi
2. Test de validation des types de fichiers
3. Test de validation de la taille
4. Test d'authentification requise
5. Test de gestion des erreurs

## Sécurité

- Vérifier le type MIME réel du fichier (pas seulement l'extension)
- Scanner les virus si possible
- Limiter le rate d'upload par utilisateur
- Nettoyer les métadonnées EXIF des images

## Performance

- Utiliser des workers Celery pour l'upload asynchrone
- Générer des thumbnails en arrière-plan
- Implémenter un CDN pour la distribution des médias

## Priorité : HAUTE

Cette fonctionnalité est essentielle pour l'engagement des utilisateurs. L'implémentation backend devrait être prioritaire.