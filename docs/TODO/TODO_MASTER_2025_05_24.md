# TODO Master - PillarScan

**Date**: 24 Mai 2025  
**Version**: 3.1  
**Status**: Post-vérification complète

## ✅ COMPLÉTÉ (vérifié par le code)

### Frontend Notifications

- [x] NotificationService avec SSE complet
- [x] Store Zustand persistant
- [x] Hook useNotifications
- [x] NotificationCenter UI
- [x] Service Worker pour push notifications
- [x] Intégration dans le layout

### Backend Notifications (notifycore)

- [x] Modèles Cassandra
- [x] SSENotificationView fonctionnel
- [x] WebSocket Consumer configuré
- [x] API REST complète
- [x] NotificationService CRUD

## 🚨 Priorité CRITIQUE (à faire immédiatement)

### 1. Connexion Backend-Frontend Notifications

- [ ] **Configurer ASGI** pour WebSocket/SSE
  - Ajouter routes notifycore dans asgi.py principal
  - Configurer le routing WebSocket
- [ ] **Connecter les signaux métier**
  - Signal lors de création d'expression
  - Signal lors de nouveau relate
  - Signal lors de badge gagné
  - Signal lors de classification AI
- [ ] **Tester la connexion E2E**
  - Vérifier que SSE fonctionne avec authentification
  - Tester les notifications temps réel

### 2. Réactiver Animations Fond (si besoin)

- [ ] **Vérifier performance actuelle**
  - Mesurer FPS avec animations actives
  - Tester sur mobile/tablette
  - Si problème, implémenter throttling

## 🔥 Priorité HAUTE (cette semaine)

### 3. PersonReferencePicker UI

- [ ] Composant recherche entités
- [ ] Autocomplete avec API civicperson
- [ ] Tags avec rôles (target/source/owner)
- [ ] Validation types entités
- [ ] Intégration CreateExpression

### 4. Tests Automatisés Frontend

- [ ] Tests NotificationService
- [ ] Tests ExpressionFeed
- [ ] Tests AuthContext
- [ ] Coverage > 70%

## 📋 Priorité MOYENNE (semaine prochaine)

### 5. Dashboard Analytics

- [ ] Stats utilisateur (expressions, relates, badges)
- [ ] Graphiques Chart.js
- [ ] Export PDF/CSV
- [ ] Filtres temporels

### 6. Préférences Notifications

- [ ] UI settings notifications
- [ ] Endpoint update preferences
- [ ] Email notifications toggle
- [ ] Fréquence résumés

### 7. Performance Optimizations

- [ ] Virtual scroll pour grandes listes
- [ ] Image lazy loading avec blur placeholder
- [ ] Bundle splitting par route
- [ ] Préchargement routes critiques

## 📌 Priorité NORMALE (2 semaines)

### 8. Gamification Avancée

- [ ] Système de niveaux complexe
- [ ] Achievements spéciaux
- [ ] Leaderboards par pays
- [ ] Rewards système

### 9. Mode Offline Complet

- [ ] Sync bidirectionnelle
- [ ] Queue d'actions offline
- [ ] Conflict resolution
- [ ] Background sync

### 10. API Publique

- [ ] Documentation OpenAPI
- [ ] Rate limiting
- [ ] Versioning strategy
- [ ] SDK JavaScript/Python

## 🐛 Bugs à Corriger

### P1 - Haute Priorité

1. [ ] SSE endpoint 404
2. [ ] Dismissed notifications réapparaissent

### P2 - Moyenne Priorité

1. [ ] Tests frontend cassés (Framer Motion)
2. [ ] Country display parfois vide
3. [ ] Upload image sans preview

### P3 - Basse Priorité

1. [ ] Animations saccadées sur Firefox
2. [ ] Console warnings React keys
3. [ ] TypeScript strict errors

## 🧹 Tech Debt

### Code Quality

- [ ] Migrer vers TypeScript strict
- [ ] Standardiser error handling
- [ ] Centraliser constants
- [ ] Documenter API interne

### Architecture

- [ ] Extraire business logic des composants
- [ ] Créer custom hooks réutilisables
- [ ] Implémenter Repository pattern
- [ ] Cache strategy cohérente

### DevOps

- [ ] CI/CD pipeline complet
- [ ] Monitoring production
- [ ] Error tracking (Sentry)
- [ ] Performance monitoring

## 📅 Planning Sprints

### Sprint 5 (27-31 Mai)

- Réactiver fonctionnalités
- SSE backend
- PersonReferencePicker
- Tests automatisés

### Sprint 6 (3-7 Juin)

- Dashboard analytics
- Préférences notifications
- Optimisations performance
- Bug fixes P1/P2

### Sprint 7 (10-14 Juin)

- Gamification avancée
- Mode offline
- API publique v1
- Documentation

## 📊 Métriques Cibles

- **Coverage**: Backend 85%, Frontend 70%
- **Performance**: Lighthouse > 90
- **Bundle Size**: < 200KB gzipped
- **Time to Interactive**: < 3s

## 🔗 Dépendances

### Backend

- Django 5.0 → 5.1 (après tests)
- Cassandra driver → optimisations
- Redis → Redis Cluster ready

### Frontend

- Next.js 14 → 15 (après stable)
- Framer Motion → optimisations
- React Query → pour cache

## 📝 Notes

- Toujours tester sur mobile first
- Respecter les principes WCAG 2.1
- Documenter les décisions architecture
- Faire des PR atomiques

---

**Progression**: 40% Phase 2 complétée
**Prochaine revue**: 31 Mai 2025
