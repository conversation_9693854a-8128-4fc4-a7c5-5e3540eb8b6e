# 📋 GLOBAL TODO LIST - PILLARSCAN

> Consolidation de toutes les tâches à faire pour le projet PillarScan
> Dernière mise à jour : 24 mai 2025

## 🚨 PRIORITÉ CRITIQUE - NOUVELLES DÉCOUVERTES

### 0. Localisation et Souveraineté des Données (FONDAMENTAL)
- [x] **Implémenter le système de localisation obligatoire** ✅ (24/05/2025)
  - [x] Hook useCountryEnforcement ✅
  - [x] Modal de sélection de pays obligatoire ✅
  - [x] Middleware Django CountryEnforcementMiddleware ✅
  - [x] Header X-Country-Code sur toutes les requêtes (sauf auth) ✅
  - [x] Filtrage automatique par pays dans toutes les queries ✅
  - [x] Gestion gracieuse des pays non supportés (liste vide) ✅
  - [x] Intégration avec geography app pour validation ✅
  - [x] Détection pays par IP avec fallback FR ✅
  - [x] Exemption du header pour endpoints auth ✅
  - [ ] Tests d'isolation des données par pays
  - [ ] Documentation utilisateur sur la souveraineté
  - [ ] Géolocalisation GPS (plus précise que IP)
  - [ ] Sélecteur de pays dans le header

### 1. Intégration Elasticsearch (ELK DÉJÀ CONFIGURÉ!) ✅
- [x] **Créer le service d'indexation PillarScanElasticsearchService**
  - [x] Mapping de l'index avec champ country obligatoire
  - [x] Méthode index_expression
  - [x] Méthode search_expressions avec filtre pays
  - [x] Signals pour indexation automatique
  - [x] Endpoint /api/v2/pillarscan/expressions/search
  - [x] Hook useExpressionSearch frontend
  - [x] Composant ExpressionSearch avec highlights
  - [x] Tests de recherche par pays

### 2. Classification AI Multi-Provider (IMPLÉMENTÉ!) ✅
- [x] **Implémenter PillarScanAIClassificationService** ✅ (24/05/2025)
  - [x] Architecture multi-provider (Claude, OpenAI, Ollama, Comprehend)
  - [x] Factory pattern avec fallback automatique
  - [x] Cache hiérarchie des piliers (12 piliers → sous-catégories → topics)
  - [x] Méthode classify unifiée pour tous providers
  - [x] Format de réponse standardisé AIProviderResult
  - [x] Support header X-AI-Provider pour override
  - [x] Tests complets pour tous les providers
  - [x] Documentation et guides d'utilisation
- [x] **Créer modèle ExpressionImputationPath** (DÉJÀ EXISTANT!)
  - [x] Structure complète : domain → category → subcategory → topic
  - [x] Scores de confiance pour chaque niveau
  - [x] Sentiment (POSITIF/NEGATIF/NEUTRE/MIXTE) avec intensité
- [x] **Tâche Celery classification_task**
  - [x] Classification asynchrone post-création
  - [x] Sauvegarde dans ExpressionImputationPath
  - [x] Mise à jour expression avec résultats
  - [x] Indexation Elasticsearch automatique
  - [x] Notification SSE de completion
- [ ] **Entraînement modèle custom Comprehend** (FUTUR)
  - [ ] Dataset annoté par pilier/sous-catégorie/topic
  - [ ] Validation avec experts domaine
  - [ ] Déploiement endpoint custom
  - [ ] Monitoring accuracy > 85%

### 2.1 Améliorations AI Multi-Provider (NOUVEAU)
- [ ] **Configuration Production**
  - [ ] Ajouter vraies API keys (Claude, OpenAI)
  - [ ] Configurer rate limiting par provider
  - [ ] Monitoring des coûts API
  - [ ] Métriques de performance par provider
- [ ] **Frontend Integration**
  - [ ] Page settings pour sélection provider
  - [ ] Affichage provider utilisé dans UI
  - [ ] Indicateur de confiance classification
  - [ ] Fallback UI si classification échoue
- [ ] **Optimisations**
  - [ ] Cache Redis pour résultats classification
  - [ ] Batch classification pour imports
  - [ ] Fine-tuning prompts par provider
  - [ ] A/B testing entre providers

## 🚨 PRIORITÉ HAUTE

### 3. Backend Media Upload (BLOQUANT)
- [ ] **Implémenter l'endpoint `/api/v2/media/upload/`** dans MediaHub
  - [ ] Créer MediaUploadView dans mediahub/views.py
  - [ ] Configurer les routes dans mediahub/urls_api.py
  - [ ] Validation des types de fichiers (jpeg, png, gif, webp)
  - [ ] Validation de la taille (max 5MB)
  - [ ] Intégration avec MinIO existant
  - [ ] Tests unitaires et d'intégration
  - [ ] Supprimer le mock dans client.ts

### 4. Person Search API (CRITIQUE)
- [ ] **Implémenter `/api/v2/civicperson/persons/search/?q=`**
  - [ ] Endpoint de recherche avec fuzzy matching
  - [ ] Support des 3 types : HUMAN, MORAL, GROUP
  - [ ] Pagination et filtres
  - [ ] Tests de performance

### 5. Tests en échec (34 tests)
- [ ] Corriger l'import du Button dans HomePage
- [ ] Résoudre le problème de loading state dans AuthContext
- [ ] Supprimer les warnings Framer Motion
- [ ] Faire passer tous les tests unitaires
- [ ] Mettre à jour les tests pour le nouveau flux auth/pays

### 6. Nettoyage post-refonte auth/pays ✅ (24/05/2025)
- [x] Flux auth sans X-Country-Code ✅
- [x] Pays depuis profil utilisateur ✅
- [x] Protection page création expression ✅
- [ ] Supprimer fichiers obsolètes :
  - [ ] `contexts/CountryContext.tsx`
  - [ ] `hooks/useCountryEnforcement.ts`
  - [ ] `components/country/CountrySelectionModal.tsx`
- [ ] Afficher le pays utilisateur dans le header

## 📊 PHASE 1 : INFRASTRUCTURE & BACKEND

### Modèles et Base de données
- [ ] Test création/read pour PillarScanUserProfile
- [ ] Test increments pour counters
- [ ] Créer keyspaces Cassandra si nécessaire
- [ ] Exécuter CREATE TABLE statements
- [ ] Créer materialized views

### Person-Expression Association
- [ ] Ajouter `mentioned_persons` Map field à PillarScanExpression
- [ ] Ajouter `ai_classification` JSON field
- [ ] Ajouter `classification_confidence` Float field
- [ ] Ajouter fields de manual override (category, subcategory, topic)
- [ ] Créer PersonResolutionTask model
- [ ] Créer PillarScanPersonReference model dans pillar_scan

### Services Backend (CLASSIFICATION DÉFINIE DANS SPECS!)
- [ ] ✅ Architecture définie : DOMAIN → CATEGORY → SUBCATEGORY → TOPIC
- [ ] ✅ Modèles existants : ExpressionCategory, ExpressionTopic, ExpressionImputationPath
- [ ] Implémenter la classification selon la spec CLASSIFICATION_AI_PILLIERS.md
  
- [ ] Créer Person Resolution Service
  - [ ] Recherche par nom/type
  - [ ] Algorithme de fuzzy matching
  - [ ] Création si inexistant
  - [ ] Mise à jour des IDs

### Tâches Celery
- [ ] Tâche de résolution des références temporaires
- [ ] Tâche de matching avec personnes existantes
- [ ] Tâche de création de nouvelles personnes
- [ ] Tâche de classification AI
- [ ] Tâche d'enrichissement des expressions

## 🎨 PHASE 2 : UI/UX ENHANCEMENTS

### Components manquants
- [ ] **PersonMention Component** (améliorations)
  - [ ] Intégration avec vraie API de recherche
  - [ ] Gestion offline des mentions
  - [ ] Animations de résolution

- [ ] **PersonSearchModal Component**
  - [ ] Filtres avancés
  - [ ] Liste des personnes récentes
  - [ ] Formulaire de création
  - [ ] Preview cards

- [ ] **ClassificationPreview Component**
  - [ ] Classification temps réel pendant la saisie
  - [ ] Visualisation du score de confiance
  - [ ] Affichage hiérarchie des piliers
  - [ ] Indicateur de sentiment

- [ ] **PillarSelector Component**
  - [ ] Navigation hiérarchique (Domain → Category → SubCategory → Topic)
  - [ ] Recherche dans les piliers
  - [ ] Sélections populaires/récentes
  - [ ] Icônes visuelles des piliers

### Expression Creation Flow
- [ ] Mood Selector avec animations
- [ ] Auto-resize textarea optimisé
- [ ] Compteur de caractères (280 max)
- [ ] Suggestions contextuelles
- [ ] Géolocalisation fluide avec fallback
- [ ] Animation de succès épique
- [ ] Son satisfaisant (optionnel)

## 🎮 PHASE 3 : GAMIFICATION

### Système de Streak
- [ ] Calculer et afficher les streaks
- [ ] Animation "Fire" pour streak actif
- [ ] Notification "streak en danger"
- [ ] Persistance offline

### Badges
- [ ] Badge "Première Expression"
- [ ] Badge "3 jours d'affilée"
- [ ] Animation de déblocage
- [ ] Collection dans le profil

### Impact Score
- [ ] Calcul basique (expressions + relates)
- [ ] Affichage mystérieux
- [ ] Teasing de progression
- [ ] Leaderboard local

## 🔧 PHASE 4 : OPTIMISATIONS & PRODUCTION

### Performance Backend
- [ ] Redis cache pour stats utilisateur
- [ ] Cache des expressions nearby
- [ ] Batch operations Cassandra
- [ ] Workers Celery pour uploads asynchrones
- [ ] Génération de thumbnails en background
- [ ] CDN pour distribution des médias

### Performance Frontend
- [ ] Code splitting des routes
- [ ] Images optimisées (avatar API)
- [ ] Bundle size < 200KB
- [ ] Compression d'images côté client
- [ ] Lazy loading des images

### PWA Améliorations
- [ ] Push Notifications (backend requis)
- [ ] Periodic Background Sync
- [ ] Web Share Target
- [ ] Badging API
- [ ] Installation prompt personnalisé

### Tests complets
- [ ] Tests pour pages expressions
- [ ] Tests système gamification
- [ ] Tests intégration API
- [ ] Tests E2E onboarding
- [ ] Tests cross-platform (iOS, Android, Desktop)
- [ ] Tests de charge

### CI/CD & DevOps
- [ ] Configurer GitHub Actions
- [ ] Tests automatiques sur PR
- [ ] Déploiement automatique staging
- [ ] Pipeline production manuelle
- [ ] Monitoring avec Sentry
- [ ] Analytics basiques
- [ ] Uptime monitoring

## 🔍 FONCTIONNALITÉS ADDITIONNELLES

### Recherche avancée
- [ ] Filtres par date, pilier, etc.
- [ ] Recherche floue (fuzzy search)
- [ ] Historique des recherches récentes
- [ ] Cache des résultats de recherche

### Media Features
- [ ] Crop/resize d'images
- [ ] Filtres d'images
- [ ] Support vidéos courtes
- [ ] Galerie avec lightbox
- [ ] Édition d'images intégrée
- [ ] OCR pour extraction de texte
- [ ] Génération d'images par IA

### Sécurité
- [ ] Vérifier type MIME réel des fichiers
- [ ] Scanner antivirus si possible
- [ ] Rate limiting par utilisateur
- [ ] Nettoyer métadonnées EXIF
- [ ] Audit trail pour classifications

## 📈 MÉTRIQUES DE SUCCÈS

### KPIs à implémenter
- [ ] Temps moyen première expression : < 30 secondes
- [ ] Taux de complétion onboarding : > 80%
- [ ] Rétention D1 : > 60%
- [ ] Expressions par utilisateur actif : > 2/jour
- [ ] Accuracy classification : > 85%
- [ ] Succès résolution personnes : > 90%
- [ ] Temps réponse API : < 200ms
- [ ] Completion tâches background : < 30s

### Monitoring
- [ ] Configurer Mixpanel events
- [ ] Dashboard temps réel
- [ ] Alertes sur KPIs critiques
- [ ] Rapports hebdomadaires

## 🐛 BUGS CONNUS

- [ ] GeotaggingService complètement mocké
- [ ] Import du Button dans HomePage
- [ ] Loading state dans AuthContext
- [ ] Warnings Framer Motion
- [ ] 34 tests en échec

## 📝 DOCUMENTATION

- [ ] Mettre à jour README principal
- [ ] Documentation API complète
- [ ] Guide d'installation
- [ ] Guide de contribution
- [ ] Documentation architecture
- [ ] Tutoriels utilisateur

---

## 🎯 ORDRE DE PRIORITÉ SUGGÉRÉ

1. **URGENT** : Backend Media Upload (débloque l'engagement utilisateur)
2. **URGENT** : Person Search API (débloque les mentions)
3. **HAUTE** : Corriger les tests en échec
4. **HAUTE** : Tâches Celery pour person resolution
5. **MOYENNE** : Gamification (streaks, badges)
6. **MOYENNE** : Optimisations performance
7. **BASSE** : Features additionnelles (filtres images, etc.)

---

> 💡 **Note** : Cette liste est vivante et doit être mise à jour régulièrement.
> Chaque item complété doit être déplacé vers le dossier DONE avec sa documentation.