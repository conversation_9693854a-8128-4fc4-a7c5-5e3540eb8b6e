# 📋 TODO List Phase 1 - PillarScan MVP Implementation
## Ordre d'exécution rigoureux - 4 semaines

### 📅 Semaine 1 : Infrastructure & Backend
*Objectif : Poser les fondations solides côté serveur*

#### Sprint 1.1 : Setup Backend Django (2 jours)
- [x] **1.1.1** <PERSON><PERSON>er l'app Django `pillar_scan` dans `smatflow_membership` [FAIT]
  ```bash
  cd smatflow_membership
  python manage.py startapp pillar_scan  # Créé avec underscore pour éviter conflit
  ```
- [x] **1.1.2** Configurer l'app dans `settings.py` [FAIT]
- [x] **1.1.3** Créer la structure des dossiers [FAIT]
  ```
  pillar_scan/
  ├── models/
  │   ├── __init__.py
  │   ├── expressions.py
  │   ├── profiles.py
  │   └── assessments.py
  ├── api/
  │   ├── v2/
  │   │   ├── views.py
  │   │   ├── serializers.py
  │   │   └── urls.py
  ├── services/
  │   ├── gamification.py
  │   └── geo_enrichment.py
  └── tests/
  ```

#### Sprint 1.2 : <PERSON><PERSON><PERSON><PERSON> (2 jours)
- [x] **1.2.1** <PERSON><PERSON><PERSON> le modèle `PillarScanUserProfile` [FAIT]
  - [x] Hériter des patterns existants (datakey, timestamps)
  - [x] Implémenter les counters pour stats
  - [ ] Tester la création/lecture
  
- [x] **1.2.2** Créer le modèle `PillarScanExpression` [FAIT]
  - [x] Étendre `CivicExpression` si possible
  - [x] Ajouter les champs spécifiques (mood, badges)
  - [x] Implémenter le partitionnement géographique
  
- [x] **1.2.3** Créer les tables de counters [FAIT]
  - [x] `PillarScanActivityCounters`
  - [x] `PillarScanRelates`
  - [ ] Tester les incréments

- [x] **1.2.4** Migrations Cassandra [FAIT]
  - [ ] Créer les keyspaces si nécessaire
  - [ ] Exécuter les CREATE TABLE
  - [ ] Créer les vues matérialisées

#### Sprint 1.3 : API REST v2 (1 jour)
- [x] **1.3.1** Créer les serializers [FAIT]
  ```python
  # api/v2/serializers.py
  - PillarScanUserProfileSerializer
  - PillarScanExpressionSerializer
  - CreateExpressionSerializer
  - PillarScanAssessmentSerializer
  ```

- [x] **1.3.2** Créer les ViewSets [FAIT]
  ```python
  # api/v2/views.py
  - PillarScanExpressionViewSet
  - PillarScanProfileViewSet
  - PillarScanAssessmentViewSet
  ```

- [x] **1.3.3** Configurer les URLs [FAIT]
  ```python
  # urls.py
  path('api/v2/pillarscan/', include('pillar_scan.api.v2.urls'))
  ```

- [x] **1.3.4** Intégrer l'authentification Person [FAIT]
  - [x] Utiliser `BasePersonAPIView`
  - [x] Vérifier les permissions
  - [x] Gérer le routing par pays

### 📅 Semaine 2 : Frontend Foundation & Onboarding
*Objectif : Interface utilisateur fluide et première expérience magique*

#### Sprint 2.1 : Setup Next.js (1 jour) ✅
- [x] **2.1.1** Initialiser le projet Next.js [FAIT]
  ```bash
  cd smatflow-pillarscan-nextjs
  npx create-next-app@14 . --typescript --tailwind --app
  ```

- [x] **2.1.2** Installer les dépendances essentielles [FAIT]
  - [x] framer-motion installé
  - [x] clsx et tailwind-merge installés
  - [x] Autres dépendances Next.js de base

- [x] **2.1.3** Structure de base [FAIT]
  - [x] app/ avec layout et pages
  - [x] components/ui/ et components/pillarscan/
  - [x] lib/api/ et lib/types/
  - [x] hooks/ créé
  - [x] contexts/ pour AuthContext

- [x] **2.1.4** Configuration de base [FAIT]
  - [x] Proxy configuré dans next.config.ts
  - [x] Tailwind configuré
  - [x] TypeScript strict mode

#### Sprint 2.2 : Design System (2 jours) ✅
- [x] **2.2.1** Implémenter les couleurs et typographie [FAIT]
  - [x] Créé `styles/colors.ts` avec palette complète
  - [x] Créé `styles/typography.ts` avec système typographique
  - [x] Fonts Inter configurée par défaut

- [x] **2.2.2** Composants UI de base [FAIT]
  - [x] `Button` créé (sans animations pour l'instant)
  - [x] `Card` component avec variants
  - [x] `Input` et `Textarea` avec validation
  - [x] `Avatar` component avec support emoji

- [x] **2.2.3** Layout responsive [FAIT]
  - [x] `Layout` wrapper avec header minimal
  - [x] Container responsive intégré
  - [x] Navigation mobile-first

- [x] **2.2.4** Hooks utilitaires [FAIT]
  - [x] `useMediaQuery` avec breakpoints
  - [x] `useLocalStorage` avec sync
  - [x] `useDebounce` avec variants

#### Sprint 2.3 : Onboarding Magique (2 jours) ✅
- [x] **2.3.1** Écrans d'onboarding [FAIT]
  - [x] Page 1 : "Votre voix compte" avec animation logo
  - [x] Page 2 : "3 gestes pour changer" avec démo interactive
  - [x] Page 3 : "Commençons par vous" avec création avatar

- [x] **2.3.2** Animations Framer Motion [FAIT]
  - [x] Transitions fluides entre écrans
  - [x] Micro-animations sur boutons et sélections
  - [x] Progress bar animée

- [x] **2.3.3** Création profil rapide [FAIT]
  - [x] Choix emoji + couleur pour avatar
  - [x] Input nickname avec validation
  - [x] Bouton "Explorer d'abord" pour skip

- [x] **2.3.4** Intégration API [FAIT]
  - [x] Appel createOrUpdateProfile
  - [x] Gestion erreurs avec messages
  - [x] État persisté avec useOnboardingState

### 📅 Semaine 3 : Expression Flow & Gamification
*Objectif : Créer l'expérience core addictive*

#### Sprint 3.1 : Interface d'Expression (2 jours)
- [ ] **3.1.1** Mood Selector
  - [ ] 4 boutons mood avec emojis
  - [ ] Animation de sélection satisfaisante
  - [ ] Feedback haptique (vibration API)

- [ ] **3.1.2** Text Input optimisé
  - [ ] Auto-resize textarea
  - [ ] Compteur de caractères (280 max)
  - [ ] Suggestions contextuelles

- [ ] **3.1.3** Géolocalisation fluide
  - [ ] Demande permission élégante
  - [ ] Fallback sur ville manuelle
  - [ ] Affichage "Near [Lieu]"

- [ ] **3.1.4** Submit avec feedback
  - [ ] Animation de succès épique
  - [ ] Son satisfaisant (optionnel)
  - [ ] Transition vers feed

#### Sprint 3.2 : Gamification Core (2 jours)
- [ ] **3.2.1** Système de Streaks
  - [ ] Calcul et affichage du streak
  - [ ] Animation "feu" pour streak actif
  - [ ] Notification de streak en danger

- [ ] **3.2.2** Premiers Badges
  - [ ] Badge "Première Expression"
  - [ ] Badge "3 jours d'affilée"
  - [ ] Animation de déblocage

- [ ] **3.2.3** Impact Score
  - [ ] Calcul basique (expressions + relates)
  - [ ] Affichage mystérieux
  - [ ] Teasing progression

- [ ] **3.2.4** Profil utilisateur
  - [ ] Vue profil avec stats
  - [ ] Collection de badges
  - [ ] Historique expressions (privé)

#### Sprint 3.3 : Feed Communautaire (1 jour)
- [ ] **3.3.1** Liste d'expressions nearby
  - [ ] Cards avec mood emoji
  - [ ] Anonymat par défaut
  - [ ] Infinite scroll

- [ ] **3.3.2** Système de "Relate"
  - [ ] Bouton "Je relate 🤝"
  - [ ] Animation de connexion
  - [ ] Compteur temps réel

- [ ] **3.3.3** Filtres basiques
  - [ ] Par mood
  - [ ] Par proximité
  - [ ] Refresh pull-to-refresh

### 📅 Semaine 4 : Polish & Production
*Objectif : Optimiser, tester et déployer*

#### Sprint 4.1 : Optimisations Performance (2 jours)
- [ ] **4.1.1** Backend optimisations
  - [ ] Cache Redis pour stats user
  - [ ] Cache expressions nearby
  - [ ] Batch operations Cassandra

- [ ] **4.1.2** Frontend optimisations
  - [ ] Code splitting routes
  - [ ] Images optimisées (avatar API)
  - [ ] Bundle size < 200KB

- [ ] **4.1.3** PWA Setup
  - [ ] Service Worker basique
  - [ ] Manifest.json
  - [ ] Offline fallback

- [ ] **4.1.4** SEO & Meta
  - [ ] Meta tags dynamiques
  - [ ] Open Graph pour partage
  - [ ] Sitemap basique

#### Sprint 4.2 : Tests & QA (2 jours)
- [ ] **4.2.1** Tests Backend
  - [ ] Tests modèles Cassandra
  - [ ] Tests API endpoints
  - [ ] Tests gamification logic

- [ ] **4.2.2** Tests Frontend
  - [ ] Tests composants critiques
  - [ ] Tests hooks custom
  - [ ] Tests E2E onboarding flow

- [ ] **4.2.3** Tests Cross-Platform
  - [ ] iOS Safari
  - [ ] Android Chrome
  - [ ] Desktop browsers

- [ ] **4.2.4** Bug Fixes
  - [ ] Liste des bugs trouvés
  - [ ] Priorisation P0/P1/P2
  - [ ] Corrections

#### Sprint 4.3 : Déploiement (1 jour)
- [ ] **4.3.1** Setup Infrastructure
  - [ ] Vercel pour frontend
  - [ ] Kubernetes pour backend
  - [ ] ScyllaDB Cloud ou self-hosted

- [ ] **4.3.2** CI/CD Pipeline
  - [ ] GitHub Actions pour tests
  - [ ] Auto-deploy sur staging
  - [ ] Manual deploy production

- [ ] **4.3.3** Monitoring
  - [ ] Sentry pour erreurs
  - [ ] Analytics basiques
  - [ ] Uptime monitoring

- [ ] **4.3.4** Launch Checklist
  - [ ] SSL certificates
  - [ ] DNS configuration
  - [ ] Backup strategy
  - [ ] Documentation finale

---

## 📊 Métriques de Succès Phase 1

### KPIs à tracker dès le lancement :
- [ ] Temps moyen première expression : < 30 secondes ✅
- [ ] Taux de complétion onboarding : > 80% ✅
- [ ] Taux de rétention J1 : > 60% ✅
- [ ] Expressions par utilisateur actif : > 2/jour ✅

### Outils de mesure :
- [ ] Mixpanel events configurés
- [ ] Dashboard temps réel
- [ ] Alertes sur KPIs critiques

---

## 🚨 Points d'Attention Critiques

1. **Cassandra Consistency** : Toujours utiliser LOCAL_QUORUM
2. **Country Routing** : Header X-Country-Code obligatoire
3. **Idempotence** : Datakey sur toutes les créations
4. **Rate Limiting** : 100 req/min par user
5. **Privacy** : Anonymat par défaut sur expressions

---

## ✅ Definition of Done

Chaque tâche est considérée DONE quand :
- [ ] Code reviewé et approuvé
- [ ] Tests écrits et passants
- [ ] Documentation à jour
- [ ] Déployé sur staging
- [ ] QA validé

---

**Note** : Ce document sera mis à jour quotidiennement avec les statuts [EN COURS] et [FAIT] pour chaque tâche.