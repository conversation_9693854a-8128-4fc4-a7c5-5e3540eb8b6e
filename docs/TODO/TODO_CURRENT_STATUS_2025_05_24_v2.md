# 📋 TODO Current Status - PillarScan

**Date**: 24 mai 2025 - v2
**Status**: Person References ✅ | Notifications API En attente

## 🎯 Résumé de l'État Actuel

### ✅ COMPLÉTÉ AUJOURD'HUI (24/05/2025)

#### 1. Backend - Multi-Provider AI Classification ✅

- ✅ Architecture multi-provider avec 4 providers AI
  - ✅ Claude (Anthropic)
  - ✅ OpenAI (ChatGPT)
  - ✅ Ollama (Local AI)
  - ✅ Amazon Comprehend
- ✅ Factory pattern avec fallback automatique
- ✅ API endpoints pour sélection de provider
- ✅ Tests complets (unit + integration)

#### 2. Refactoring Gestion des Pays ✅

- ✅ Suppression complète de la sélection manuelle du pays
- ✅ Le pays vient UNIQUEMENT du profil utilisateur
- ✅ CountryEnforcementMiddleware mis à jour
- ✅ Frontend débarrassé de CountryProvider
- ✅ Activation du pays FR dans GeographyCountry

#### 3. Person References Implementation ✅

- ✅ Ajout du champ `user_id` (obligatoire) aux expressions
- ✅ Ajout du champ `person_references` (Liste des entités associées)
- ✅ Migration de 289 expressions existantes
- ✅ Validation des types (physical, moral, group)
- ✅ Validation des rôles (target, source, owner)
- ✅ Tests complets du pipeline avec person_references

#### 4. Corrections Critiques ✅

- ✅ Fix erreur "Country code 'CF' not supported"
- ✅ Fix boucle infinie dans ExpressionFeed
- ✅ Fix AttributeError 'Person' has no 'full_name'
- ✅ Fix port API dans .env.local (8000 → 8001)
- ✅ Fix référence undefined `country` dans ExpressionFeed

#### 5. Système de Notifications Backend ✅ (24/05/2025 - Session 2)

- ✅ Endpoints notifications implémentés dans notifycore
- ✅ Routes exposées sur /api/v2/notifications/
- ✅ Service PillarScanNotificationService créé
- ✅ 7 types de notifications PillarScan
- ✅ Tests unitaires et d'intégration
- ✅ Scripts de test et génération de données

### 🚧 EN COURS - PRIORITÉS IMMÉDIATES

## 1. Frontend - Mise à jour NotificationService

### Remplacer les mocks par les vrais endpoints

- [ ] Mettre à jour lib/services/NotificationService.ts
- [ ] Utiliser /api/v2/notifications/ au lieu des mocks
- [ ] Implémenter le SSE pour /api/v2/notifications/stream/
- [ ] Tester l'intégration complète

## 2. Frontend - Interface Person References

### Composant PersonReferencePicker

- [ ] Recherche d'entités existantes
- [ ] Création rapide de nouvelles références
- [ ] Multi-sélection avec tags
- [ ] Validation des rôles par type

### Intégration dans CreateExpression

- [ ] Ajouter PersonReferencePicker au formulaire
- [ ] Afficher les références dans ExpressionCard
- [ ] Permettre l'édition des références

## 3. Système de Recherche Avancée

### Backend - Filtres par person_references

- [ ] Endpoint `/api/v2/pillarscan/expressions/?person_name=X`
- [ ] Endpoint `/api/v2/pillarscan/expressions/?person_type=moral`
- [ ] Agrégation des statistiques par entité

### Frontend - Interface de recherche

- [ ] Filtres avancés dans ExpressionFeed
- [ ] Page dédiée par entité référencée
- [ ] Graphiques de tendances par entité

## 📝 TODO Semaine Prochaine

### Sprint 4.1 : Notifications & Real-time

1. **Backend Notifications** (1 jour)

   - Modèle Cassandra
   - ViewSet REST
   - WebSocket/SSE handler
   - Tests unitaires

2. **Frontend Notifications** (2 jours)
   - NotificationProvider context
   - NotificationBell component
   - NotificationList dropdown
   - Real-time updates via SSE

### Sprint 4.2 : Person References UI

1. **PersonReferencePicker** (1 jour)

   - Composant de recherche/sélection
   - Intégration API civicperson
   - Gestion des tags

2. **Analytics Dashboard** (2 jours)
   - Stats par entité référencée
   - Graphiques de sentiment
   - Export des données

## 🚨 Points d'Attention

### Sécurité

- ✅ Country code vérifié côté serveur uniquement
- ✅ user_id automatique depuis request.user
- ⚠️ Vérifier permissions sur person_references

### Performance

- ✅ Indexes Cassandra sur user_id et country_code
- ⚠️ Optimiser requêtes avec person_references
- ⚠️ Cache pour les entités fréquemment référencées

### UX

- ✅ Plus de sélection manuelle du pays
- ⚠️ Rendre person_references intuitif
- ⚠️ Feedback clair sur les notifications

## 📊 État du Projet

### Coverage

- Backend PillarScan: 75% ✅
- Frontend Components: 35% ⚠️
- Integration Tests: 60% ✅

### Bugs Connus

- P0: 0 ✅
- P1: 1 (Notifications mock à remplacer)
- P2: 8 (Principalement tests frontend)

### Tech Debt

- ✅ Refactoring pays complété
- ⚠️ Tests frontend à améliorer
- ⚠️ Documentation API à mettre à jour

## 🎯 Objectifs Court Terme

1. **Cette semaine**:
   - ✅ Person references (FAIT)
   - ⏳ Endpoints notifications
2. **Semaine prochaine**:

   - UI Person references
   - Système notifications complet
   - Dashboard analytics v1

3. **Dans 2 semaines**:
   - Gamification avancée
   - Export/Import données
   - API publique documentée

## 💡 Notes Architecture

### Person References

- Flexible: supporte tous types d'entités
- Extensible: nouveaux rôles faciles à ajouter
- Performant: stocké directement dans l'expression

### Notifications

- Real-time via SSE (pas WebSocket pour simplicité)
- Stockage Cassandra pour scalabilité
- Types extensibles via le champ data

---

**Next Action**: ~~Intégrer les vrais endpoints de notifications dans le frontend NotificationService~~ ✅ FAIT

## 📝 Mise à jour 24/05/2025 - 21h45

### Notifications Frontend Integration ✅

- [x] NotificationService mis à jour avec vrais endpoints
- [x] Structure des données adaptée (notification_id, content, status)
- [x] NotificationCenter et hooks mis à jour
- [x] Tests d'intégration créés et validés
- [x] 12 notifications récupérées avec succès
- [x] Actions mark_as_read et dismiss fonctionnelles

### Limitations identifiées:

- [ ] SSE endpoint retourne 404 (`/api/v2/notifications/notifications/stream/`)
- [ ] État dismissed non persisté côté backend
- [ ] Préférences utilisateur non implémentées

### Prochaine étape recommandée:

**Implémenter l'endpoint SSE côté backend pour les notifications temps réel**
