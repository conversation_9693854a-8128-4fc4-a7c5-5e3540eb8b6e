# 📋 TODO List - Person Linking & Pillar Classification System

## Overview

This TODO list covers the implementation of:

1. Person linking (Physical, Moral, Group entities)
2. AI-powered pillar classification
3. Expression type/sentiment analysis
4. Background task processing

---

## 🔴 Phase 1: Core Infrastructure (Week 1)

### Backend Foundation

- [ ] **1.1 Update Expression Models**

  - [ ] Add `mentioned_persons` Map field to PillarScanExpression
  - [ ] Add `ai_classification` JSON field
  - [ ] Add `classification_confidence` Float field
  - [ ] Add manual override fields (category, subcategory, topic)
  - [ ] Create PersonResolutionTask model

- [ ] **1.2 Create AI Classification Service**

  ```python
  # pillar_scan/services/ai_classification.py
  - [ ] Setup Amazon Comprehend client
  - [ ] Implement classify_expression method
  - [ ] Add sentiment analysis
  - [ ] Add entity extraction
  - [ ] Create custom pillar classifier
  ```

- [ ] **1.3 Create Person Resolution Service**
  ```python
  # pillar_scan/services/person_resolution.py
  - [ ] Search existing persons by name/type
  - [ ] Implement fuzzy matching algorithm
  - [ ] Create person if not found
  - [ ] Update expression with resolved IDs
  ```

### Frontend Foundation

- [ ] **1.4 Create Person Reference Types**

  ```typescript
  // types/person.ts
  - [ ] PersonType enum (HUMAN, MORAL, GROUP)
  - [ ] PersonReference interface
  - [ ] PersonSearchResult interface
  - [ ] ClassificationResult interface
  ```

- [ ] **1.5 Create Base Services**

  ```typescript
  // services/personService.ts
  - [ ] searchPersons(query, type) method
  - [ ] createTempPerson() method
  - [ ] resolvePersons() method

  // services/classificationService.ts
  - [ ] classifyText() method
  - [ ] getClassificationPreview() method
  ```

---

## 🟠 Phase 2: Expression Creation UI (Week 2)

### Person Mention System

- [ ] **2.1 PersonMention Component**

  - [ ] Autocomplete dropdown with search
  - [ ] Person type selector (Physical/Moral/Group)
  - [ ] Create new person option
  - [ ] Display selected persons as chips
  - [ ] Role assignment (target/source/owner)

- [ ] **2.2 PersonSearchModal Component**

  - [ ] Advanced search filters
  - [ ] Recent persons list
  - [ ] Create person form
  - [ ] Person preview cards

- [ ] **2.3 Enhanced Expression Form**
  - [ ] Integrate PersonMention component
  - [ ] Add mentioned_persons to form state
  - [ ] Handle temporary person creation
  - [ ] Show person resolution status

### Classification UI

- [ ] **2.4 ClassificationPreview Component**

  - [ ] Real-time classification as user types
  - [ ] Confidence score visualization
  - [ ] Pillar hierarchy display
  - [ ] Sentiment indicator

- [ ] **2.5 PillarSelector Component**

  - [ ] Hierarchical navigation (Domain → Category → SubCategory → Topic)
  - [ ] Search within pillars
  - [ ] Popular/recent selections
  - [ ] Visual pillar icons

- [ ] **2.6 Manual Override UI**
  - [ ] Override AI classification toggle
  - [ ] Manual pillar selection
  - [ ] Justification text field
  - [ ] Save override preferences

---

## 🟡 Phase 3: Backend Processing (Week 3)

### API Endpoints

- [ ] **3.1 Enhanced Expression Creation**

  ```python
  POST /api/v2/pillarscan/expressions/create-classified/
  - [ ] Accept mentioned_persons array
  - [ ] Trigger classification task
  - [ ] Return expression with temp IDs
  - [ ] Queue background tasks
  ```

- [ ] **3.2 Person Search Endpoint**

  ```python
  GET /api/v2/civicperson/search/
  - [ ] Full-text search
  - [ ] Filter by person type
  - [ ] Fuzzy matching
  - [ ] Pagination
  ```

- [ ] **3.3 Classification Endpoints**

  ```python
  POST /api/v2/pillarscan/classify-preview/
  - [ ] Real-time classification
  - [ ] Return scores and confidence

  PATCH /api/v2/pillarscan/expressions/{id}/classification/
  - [ ] Manual override endpoint
  - [ ] Audit trail
  ```

### Background Tasks (Celery)

- [ ] **3.4 Classification Task**

  ```python
  @shared_task
  def classify_expression(expression_id):
  - [ ] Call Amazon Comprehend
  - [ ] Extract entities
  - [ ] Classify into pillars
  - [ ] Update expression
  - [ ] Send notification
  ```

- [ ] **3.5 Person Resolution Task**

  ```python
  @shared_task
  def resolve_person_mentions(expression_id, person_refs):
  - [ ] Search each person
  - [ ] Apply matching algorithm
  - [ ] Create if needed
  - [ ] Update expression
  - [ ] Track resolution status
  ```

- [ ] **3.6 Enrichment Task**
  ```python
  @shared_task
  def enrich_expression_metadata(expression_id):
  - [ ] Extract key phrases
  - [ ] Generate tags
  - [ ] Calculate impact scores
  - [ ] Update related expressions
  ```

---

## 🟢 Phase 4: Integration & Polish (Week 4)

### Data Flow Integration

- [ ] **4.1 Real-time Updates**

  - [ ] WebSocket for classification status
  - [ ] SSE for person resolution updates
  - [ ] Progress indicators
  - [ ] Error handling

- [ ] **4.2 Offline Support**
  - [ ] Queue person mentions offline
  - [ ] Store classification locally
  - [ ] Sync on reconnection
  - [ ] Conflict resolution

### UI/UX Polish

- [ ] **4.3 Animations & Feedback**

  - [ ] Classification confidence animation
  - [ ] Person resolution progress
  - [ ] Success/error states
  - [ ] Loading skeletons

- [ ] **4.4 Mobile Optimization**
  - [ ] Touch-friendly person selection
  - [ ] Responsive classification preview
  - [ ] Gesture support
  - [ ] Performance optimization

### Testing & QA

- [ ] **4.5 Unit Tests**

  - [ ] Classification service tests
  - [ ] Person resolution tests
  - [ ] Component tests
  - [ ] API endpoint tests

- [ ] **4.6 Integration Tests**

  - [ ] End-to-end expression creation
  - [ ] Background task processing
  - [ ] Error scenarios
  - [ ] Performance tests

- [ ] **4.7 E2E Tests**
  - [ ] Complete user workflows
  - [ ] Cross-browser testing
  - [ ] Mobile testing
  - [ ] Load testing

---

## 📊 Success Metrics

### Technical Metrics

- [ ] Classification accuracy > 85%
- [ ] Person resolution success > 90%
- [ ] API response time < 200ms
- [ ] Background task completion < 30s

### User Metrics

- [ ] Person mention usage > 40% of expressions
- [ ] Manual classification override < 10%
- [ ] User satisfaction > 4.5/5
- [ ] Feature adoption > 60% in first month

---

## 🚀 Quick Wins (Can do immediately)

1. **Create TypeScript interfaces** for person and classification types
2. **Setup Amazon Comprehend** credentials and test connection
3. **Create basic PersonMention** component with mock data
4. **Add classification fields** to expression model
5. **Create person search** API endpoint with basic functionality

---

## 📝 Technical Decisions Needed

1. **Fuzzy Matching Algorithm**: Levenshtein distance vs. more advanced NLP
2. **Classification Model**: Use Comprehend only vs. custom training
3. **Person Deduplication**: Automatic vs. manual review
4. **Caching Strategy**: Redis vs. in-memory vs. CDN
5. **Batch Processing**: Immediate vs. scheduled batches

---

## 🔗 Dependencies

### External Services

- Amazon Comprehend API
- Redis for caching
- Celery for background tasks
- PostgreSQL for person data
- Cassandra for expressions

### Internal Dependencies

- CivicPerson models and APIs
- CivicStore category hierarchy
- Authentication system
- Notification system

---

## 📅 Timeline

**Week 1**: Core infrastructure (Models, Services, Basic UI)
**Week 2**: Complete UI components and integration
**Week 3**: Backend processing and background tasks
**Week 4**: Testing, polish, and deployment

**Total Estimate**: 4 weeks for full implementation

---

## ⚠️ Risks & Mitigations

1. **AI Classification Accuracy**

   - Risk: Low accuracy for French text
   - Mitigation: Custom training data, manual override option

2. **Person Resolution Conflicts**

   - Risk: Multiple matches for same person
   - Mitigation: Confidence scoring, manual review queue

3. **Performance at Scale**

   - Risk: Slow classification with many expressions
   - Mitigation: Batch processing, caching, async tasks

4. **Privacy Concerns**
   - Risk: Exposing private person data
   - Mitigation: Permission system, data anonymization
