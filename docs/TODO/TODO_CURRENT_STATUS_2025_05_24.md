# 📋 TODO Current Status - PillarScan
**Date**: 24 mai 2025
**Status**: Backend AI Multi-Provider ✅ | Frontend Sprint 3.1 En cours

## 🎯 Résumé de l'État Actuel

### ✅ COMPLÉTÉ RÉCEMMENT

#### Backend - Multi-Provider AI Classification (24/05/2025)
- ✅ Architecture multi-provider avec 4 providers AI
  - ✅ Claude (Anthropic) 
  - ✅ OpenAI (ChatGPT)
  - ✅ Ollama (Local AI)
  - ✅ Amazon Comprehend
- ✅ Factory pattern avec fallback automatique
- ✅ API endpoints pour sélection de provider
- ✅ Tests complets (unit + integration)
- ✅ Documentation et AVANCEMENT créés
- ✅ Commit et push sur GitLab

### 🚧 EN COURS - FRONTEND PRIORITAIRE

## Sprint 3.1 : Interface d'Expression (HAUTE PRIORITÉ)

### 3.1.1 Mood Selector
- [ ] **Créer MoodSelector component**
  - [ ] 4 boutons avec emojis: 😊 😔 😡 😨
  - [ ] Animation Framer Motion sur sélection
  - [ ] État contrôlé avec prop `value` et `onChange`
  - [ ] Style hover/active states
  - [ ] Vibration API sur mobile (navigator.vibrate)

### 3.1.2 Text Input Optimisé
- [ ] **Créer ExpressionTextarea component**
  - [ ] Auto-resize basé sur le contenu
  - [ ] Compteur de caractères (max 280)
  - [ ] Placeholder contextuel selon mood
  - [ ] Animation du compteur quand proche limite
  - [ ] Suggestions contextuelles (future)

### 3.1.3 Géolocalisation Fluide
- [ ] **Créer LocationPicker component**
  - [ ] Hook useGeolocation avec permission
  - [ ] UI élégante pour demande permission
  - [ ] Fallback sur saisie manuelle ville
  - [ ] Affichage "📍 Near [Location]"
  - [ ] Loading state pendant géoloc

### 3.1.4 Submit avec Feedback
- [ ] **Améliorer CreateExpression flow**
  - [ ] Animation de succès (confetti?)
  - [ ] Son optionnel (Web Audio API)
  - [ ] Transition smooth vers feed
  - [ ] Toast de confirmation
  - [ ] Gestion erreurs gracieuse

## 📝 TODO Immédiat (Cette semaine)

### Frontend - Finir Sprint 3.1
1. **MoodSelector.tsx** (2h)
   ```tsx
   components/pillarscan/expression/MoodSelector.tsx
   - Props: value, onChange, disabled
   - Animations Framer Motion
   - Tests unitaires
   ```

2. **ExpressionTextarea.tsx** (3h)
   ```tsx
   components/pillarscan/expression/ExpressionTextarea.tsx
   - Auto-resize logic
   - Character counter
   - Validation en temps réel
   ```

3. **LocationPicker.tsx** (4h)
   ```tsx
   components/pillarscan/expression/LocationPicker.tsx
   - useGeolocation hook
   - Permission flow
   - Manual input fallback
   ```

4. **CreateExpressionForm.tsx** (4h)
   ```tsx
   components/pillarscan/expression/CreateExpressionForm.tsx
   - Intégrer tous les composants
   - Submit flow complet
   - Success animations
   ```

### Backend - Intégration AI avec Frontend
1. **Update ExpressionViewSet** (2h)
   - [ ] Ajouter header X-AI-Provider support
   - [ ] Retourner provider utilisé dans response
   - [ ] Logger les métriques par provider

2. **Frontend AI Provider Selection** (3h)
   - [ ] Settings page pour choisir provider
   - [ ] Afficher providers disponibles
   - [ ] Persister choix dans localStorage
   - [ ] Passer header dans API calls

## 🔄 Prochaines Étapes (Semaine prochaine)

### Sprint 3.2 : Gamification Core
- [ ] Système de Streaks
- [ ] Premiers Badges  
- [ ] Impact Score
- [ ] Profil utilisateur enrichi

### Sprint 3.3 : Feed Communautaire
- [ ] Liste expressions nearby
- [ ] Système "Relate" 
- [ ] Filtres basiques

## 🚨 Bloquants Actuels

1. **Tests Frontend** (34 échecs)
   - [ ] Fix Button import dans HomePage
   - [ ] Fix loading state AuthContext
   - [ ] Supprimer warnings Framer Motion

2. **Backend Media Upload**
   - [ ] Endpoint `/api/v2/media/upload/` réel
   - [ ] Remplacer le mock actuel

3. **Person Search API**
   - [ ] `/api/v2/civicperson/persons/search/?q=`
   - [ ] Nécessaire pour mentions

## 📊 Métriques Sprint Actuel

- **Vélocité**: 8 story points / semaine
- **Tests Coverage**: Frontend 30% | Backend 65%
- **Bugs P0**: 0 | P1: 3 | P2: 12
- **Tech Debt**: Moyen (tests à améliorer)

## 🎯 Objectifs Semaine

1. ✅ Multi-provider AI (FAIT)
2. ⏳ Sprint 3.1 Expression Flow (EN COURS)
3. ⏳ Fix tests unitaires
4. ⏳ Préparer Sprint 3.2

## 💡 Notes

- Prioriser l'UX de création d'expression
- Les animations doivent être fluides mais pas excessives
- Mobile-first toujours
- Garder en tête les performances (bundle size)
- Documentation au fur et à mesure

---

**Next Action**: Implémenter MoodSelector.tsx avec animations Framer Motion