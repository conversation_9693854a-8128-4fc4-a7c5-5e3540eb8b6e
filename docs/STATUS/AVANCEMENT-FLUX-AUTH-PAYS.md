# 📊 AVANCEMENT - Refonte du flux d'authentification et de gestion du pays

**Date**: 24 Mai 2025
**Sprint**: Correction du flux pays/authentification
**Changement majeur**: Le pays vient UNIQUEMENT du profil utilisateur après connexion

## 🎯 Objectif

Refondre complètement le flux pour que :

1. Pas de header `X-Country-Code` requis pour l'authentification
2. Le pays vient exclusivement du profil utilisateur
3. Obligation de connexion pour créer une expression
4. Suppression de la détection automatique de pays

## 🔄 Nouveau flux implémenté

### 1. Connexion (Login)

- **Pas de header X-Country-Code** envoyé
- Après login réussi :
  - Appel à `/api/v2/civicperson/auth/profile/` pour récupérer le profil
  - Extraction du pays depuis `response.country`
  - Définition du pays dans le client API

### 2. Vérification d'authentification (checkAuth)

- Au démarrage de l'app :
  - Si token présent → récupération du profil auth
  - Définition automatique du pays depuis le profil
  - Si échec → nettoyage complet (token + pays)

### 3. Création d'expression

- **Obligation de connexion** :
  - Redirection automatique vers `/auth/login` si non connecté
  - Sauvegarde de l'URL pour retour après login
  - Affichage d'un loader pendant la vérification

### 4. Déconnexion (Logout)

- Nettoyage complet :
  - Token d'accès
  - Token de refresh
  - Pays dans le client API
  - LocalStorage

## ✅ Modifications techniques

### Client API (`lib/api/client.ts`)

```typescript
// Endpoints exemptés du header X-Country-Code
private readonly EXEMPT_ENDPOINTS = [
  '/auth/login/',
  '/auth/register/',
  '/auth/refresh/',
  '/auth/password/reset/',
  '/auth/password/reset/confirm/',
];

// Header conditionnel
if (!isExempt && countryCode) {
  headers['X-Country-Code'] = countryCode;
}

// Nouvelle méthode pour récupérer le profil auth
async getAuthProfile(): Promise<any> {
  const response = await this.request<any>(
    `${API_PREFIX}/civicperson/auth/profile/`
  );

  if (response.country) {
    this.setCountryCode(response.country);
  }

  return response;
}
```

### AuthContext (`contexts/AuthContext.tsx`)

```typescript
// Après login
const authProfile = await pillarScanAPI.getAuthProfile();
console.log('Pays utilisateur:', authProfile.country);

// Lors du checkAuth
if (token) {
  const authProfile = await pillarScanAPI.getAuthProfile();
  // Le pays est automatiquement défini dans le client
}

// Lors du logout
pillarScanAPI.setCountryCode(''); // Nettoyage
localStorage.removeItem('selectedCountry');
```

### Protection des pages (`app/create/page.tsx`)

```typescript
// Redirection si non connecté
useEffect(() => {
  if (!authLoading && !isAuthenticated) {
    sessionStorage.setItem('redirectAfterLogin', '/create');
    router.push('/auth/login');
  }
}, [isAuthenticated, authLoading, router]);

// Affichage conditionnel
if (authLoading) return <Loader />;
if (!isAuthenticated) return null;
```

## 🗑️ Suppressions

1. **CountryProvider** : Complètement retiré du layout
2. **useCountryEnforcement** : N'est plus utilisé
3. **Détection automatique par IP** : Désactivée
4. **Modal de sélection de pays** : Supprimée

## 🔐 Sécurité améliorée

- Impossible de créer une expression sans être connecté
- Le pays est toujours celui du profil utilisateur
- Pas de risque d'usurpation de pays
- Isolation stricte des données par pays

## 📝 Impact utilisateur

1. **Plus simple** : Pas de sélection de pays manuelle
2. **Plus sûr** : Le pays est celui du profil vérifié
3. **Plus cohérent** : Un seul flux pour tous

## 🧪 Tests à effectuer

1. ✅ Login sans header X-Country-Code
2. ✅ Récupération automatique du pays après login
3. ✅ Protection de la page de création
4. ✅ Nettoyage complet au logout
5. ✅ Persistance du pays entre sessions

## 📌 TODO restant

1. Retirer complètement les fichiers non utilisés :

   - `contexts/CountryContext.tsx`
   - `hooks/useCountryEnforcement.ts`
   - `components/country/CountrySelectionModal.tsx`

2. Mettre à jour la documentation utilisateur

3. Ajouter le pays dans le header de l'app pour information

## 🎉 Résultat

Le flux est maintenant beaucoup plus simple et sécurisé. Le pays est une propriété intrinsèque du profil utilisateur et ne peut pas être modifié arbitrairement.
