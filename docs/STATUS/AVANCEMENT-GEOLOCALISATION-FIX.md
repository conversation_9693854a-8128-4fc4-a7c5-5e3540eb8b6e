# 📊 AVANCEMENT - Correction de la géolocalisation et du header X-Country-Code

**Date**: 24 Mai 2025
**Sprint**: Correction bugs frontend
**Problème résolu**: Erreur "Country code 'CF' is not supported" lors du login

## 🎯 Objectif

Corriger le problème où le code pays "CF" (République Centrafricaine) est détecté automatiquement par IP et envoyé lors du login, causant une erreur 400.

## 🔍 Analyse du problème

### Origine du "CF"

1. Le hook `useCountryEnforcement` utilise `ipapi.co/json/` pour détecter le pays par IP
2. Votre IP retourne "CF" (République Centrafricaine)
3. Ce pays existe dans la base mais n'est pas **actif**
4. Le header `X-Country-Code: CF` est envoyé sur TOUTES les requêtes, même le login

### Problème de conception

- Le middleware backend exempte `/auth/login/` du header obligatoire
- Mai<PERSON> le frontend envoie quand même le header
- Le middleware rejette CF car non supporté → erreur 400

## ✅ Solutions implémentées

### 1. Frontend - Exemption du header pour certains endpoints

**Fichier**: `lib/api/client.ts`

```typescript
// Endpoints qui ne nécessitent pas de country code
private readonly EXEMPT_ENDPOINTS = [
  '/auth/login/',
  '/auth/register/',
  '/auth/refresh/',
  '/auth/password/reset/',
  '/auth/password/reset/confirm/',
];

// N'ajouter X-Country-Code que si l'endpoint n'est pas exempté
const isExempt = this.EXEMPT_ENDPOINTS.some(exempt => endpoint.includes(exempt));
if (!isExempt) {
  headers['X-Country-Code'] = this.getCountryCode();
}
```

### 2. Amélioration de la détection de pays

**Fichier**: `hooks/useCountryEnforcement.ts`

```typescript
// Vérifier si le pays détecté est supporté
const supportedCountries = ['FR', 'US', 'CA', 'GB', 'DE', 'ES', 'IT', 'BR', ...];

if (data.country_code && supportedCountries.includes(data.country_code)) {
  return data.country_code;
} else {
  console.warn(`Pays détecté ${data.country_code} non supporté, utilisation de FR par défaut`);
  return 'FR'; // Défaut à France si pays non supporté
}
```

### 3. Scripts de nettoyage

**Script Node.js**: `scripts/fix-country.js`

- Ouvre une page HTML pour nettoyer le localStorage
- Affiche le pays actuel et la liste des pays supportés

**Script Bash**: `clear-country.sh`

- Instructions pour nettoyer manuellement
- Bookmarklet pratique

## 📋 Hiérarchie de sélection du pays

1. **Utilisateur connecté** : Utilise `user.primaryCountry` du profil
2. **LocalStorage** : Pays précédemment sélectionné
3. **Géolocalisation** :
   - Par GPS (non implémenté)
   - Par IP avec `ipapi.co` (fallback)
4. **Modal obligatoire** : Si aucune méthode ne fonctionne

## 🚀 Actions pour l'utilisateur

1. **Nettoyer le localStorage** :

   ```javascript
   localStorage.removeItem('selectedCountry');
   ```

2. **Relancer l'application** pour sélectionner un nouveau pays

3. **Pays recommandés** : FR, US, CA, GB, etc.

## 🔐 Sécurité et souveraineté

- Les données sont strictement isolées par pays
- Impossible d'accéder aux données d'un autre pays
- Le pays suit l'utilisateur dans toute l'application

## 📌 TODO restant

1. **Implémenter la géolocalisation GPS** pour plus de précision
2. **Ajouter un sélecteur de pays** dans le header pour changer facilement
3. **Synchroniser le pays** entre frontend et backend après login
4. **Activer plus de pays** dans la base de données

## 🧪 Tests

1. Login sans header X-Country-Code ✅
2. Détection automatique avec fallback FR ✅
3. Modal de sélection si pas de pays ✅
4. Persistence dans localStorage ✅

## 📝 Notes importantes

- CF existe dans la base mais n'est pas actif
- Les endpoints d'authentification n'ont pas besoin du pays
- Le pays est obligatoire pour toutes les autres requêtes
- La géolocalisation par IP n'est pas toujours fiable
