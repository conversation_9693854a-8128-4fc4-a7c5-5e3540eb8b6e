# Spécification : Système de Manifeste Anti-Régression

## Vision

Créer un système de manifeste qui capture l'état stable de l'application à un moment donné, permettant de :

- Détecter et prévenir les régressions
- Nettoyer les fichiers temporaires
- Revenir rapidement à une version stable
- Documenter l'évolution du code

## Structure du Manifeste

### Format

- **Fichier** : `/docs/manifest/app-manifest-v{VERSION}.json`
- **Versioning** : Chaque version stable génère un nouveau manifeste
- **Historique** : Garder les 5 derniers manifestes

### Schema JSON

```json
{
  "manifest_version": "1.0.0",
  "app_version": "0.1.0",
  "generated_at": "2025-05-25T14:30:00Z",
  "git_commit": "034296d",
  "stability_level": "STABLE",
  "statistics": {
    "total_files": 150,
    "total_lines": 25000,
    "total_size_mb": 12.5,
    "test_coverage": 39.5
  },
  "files": {
    "/lib/api/client.ts": {
      "hash": "sha256:abc123...",
      "size": 15234,
      "lines": 523,
      "criticality": "CORE",
      "category": "api",
      "dependencies": ["/lib/types/pillarscan.ts", "/lib/utils/auth-cleanup.ts"],
      "tests": [
        "/__tests__/media-upload.test.tsx",
        "/__tests__/integration/createExpressionWithMedia.test.tsx"
      ],
      "last_stable_commit": "034296d",
      "stability_score": 95,
      "known_issues": []
    }
  },
  "test_scenarios": {
    "/scripts/test-media-pipeline-complete.js": {
      "purpose": "E2E test for media upload",
      "status": "PASSING",
      "critical_path": true,
      "dependencies": ["/lib/api/client.ts", "/components/upload/SimpleImageUpload.tsx"]
    }
  },
  "temporary_files": ["/scripts/debug-*.js", "/tmp-*.tsx"]
}
```

## Niveaux de Criticité

1. **CORE** : Fichiers essentiels (API client, types, auth)
2. **FEATURE** : Fonctionnalités principales
3. **UI** : Composants d'interface
4. **TEST** : Tests unitaires/intégration
5. **UTILITY** : Scripts et utilitaires
6. **TEMPORARY** : Fichiers temporaires à nettoyer

## Processus

### 1. Génération du Manifeste

Commande : `npm run manifest:generate`

Actions :

- Scanner tous les fichiers du projet
- Calculer les hash SHA-256
- Analyser les imports/dépendances
- Identifier les tests associés
- Marquer la criticité
- Générer le JSON

### 2. Validation de Stabilité

Commande : `npm run manifest:validate`

Critères :

- Tous les tests passent
- Build réussi
- Pas d'erreurs TypeScript
- Coverage > seuil minimum

### 3. Comparaison avec Version Stable

Commande : `npm run manifest:compare`

Détecte :

- Fichiers modifiés (hash différent)
- Fichiers ajoutés/supprimés
- Régressions potentielles
- Fichiers temporaires à nettoyer

### 4. Restauration d'un Fichier Stable

Commande : `npm run manifest:restore <file-path>`

Actions :

- Chercher dans le dernier manifeste stable
- Récupérer depuis Git au commit stable
- Créer une branche de fix si nécessaire

## Règles de Gestion

### Fichiers Temporaires

- Pattern : `debug-*`, `test-*`, `tmp-*`
- Si absent du manifeste → candidat à suppression
- Confirmation avant suppression

### Détection de Régression

- Hash différent + tests échouent = régression probable
- Alerte si fichier CORE modifié sans tests
- Suggestion de rollback au dernier stable

### Évolution du Manifeste

- Nouveau manifeste à chaque release
- Archive des anciens dans `/docs/manifest/archive/`
- Changelog automatique entre versions

## Intégration CI/CD

### Pre-commit Hook

```bash
# Vérifier si des fichiers CORE sont modifiés
npm run manifest:check-core
```

### Post-merge

```bash
# Comparer avec le manifeste stable
npm run manifest:compare --fail-on-regression
```

### Release

```bash
# Générer nouveau manifeste stable
npm run manifest:generate --stability-level=STABLE
```

## Avantages

1. **Prévention Active** : Détection précoce des régressions
2. **Nettoyage Automatisé** : Identification des fichiers obsolètes
3. **Traçabilité Complète** : Historique de chaque fichier
4. **Rollback Rapide** : Retour facile à une version stable
5. **Documentation Vivante** : État réel du projet

## Implémentation Progressive

### Phase 1 : Structure de base

- Schema JSON minimal
- Génération manuelle
- Hash et taille des fichiers

### Phase 2 : Automatisation

- Script de génération
- Détection des dépendances
- Liens avec les tests

### Phase 3 : Intelligence

- Score de stabilité
- Détection de régression
- Suggestions de fix

### Phase 4 : Intégration complète

- Hooks Git
- CI/CD pipeline
- Dashboard de santé

## Exemple d'Utilisation

```bash
# Après avoir fixé le bug média
npm run manifest:generate --tag="media-upload-fixed"

# Plus tard, si régression
npm run manifest:compare --with="media-upload-fixed"
# Output: client.ts modifié, risque de régression ligne 420

# Restaurer version stable
npm run manifest:restore lib/api/client.ts --from="media-upload-fixed"
```

## Notes d'Implémentation

- Utiliser `crypto` pour les hash
- Parser les imports avec AST
- Stocker en Git LFS si > 1MB
- Compresser les anciens manifestes
- API pour query le manifeste

Ce système transforme la gestion des régressions de réactive à proactive !
