# Prochaines Étapes - PillarScan

Date : 23 mai 2025

## Vue d'ensemble

Ce document présente les prochaines étapes de développement pour PillarScan, organisées par ordre de priorité décroissante. Chaque item inclut l'effort estimé et les dépendances.

## 🔴 Priorité CRITIQUE (À faire immédiatement)

### 1. Intégration Backend Django

**Effort** : 1-2 semaines | **Bloquant** : Oui

- [ ] **Endpoints SSE pour notifications temps réel**

  - Implémenter `/api/notifications/stream/`
  - Gérer heartbeat et reconnexion
  - Format des messages SSE

- [ ] **Upload MinIO réel**

  - Remplacer le mock `/api/upload`
  - Configuration MinIO Django
  - Pipeline d'enrichissement Celery

- [ ] **API Expressions complète**

  - CRUD avec pagination
  - Filtres par pilier/émotion
  - Gestion des relations

- [ ] **Synchronisation offline**

  - Endpoint `/api/sync/expressions/`
  - Gestion des conflits
  - Batch operations

- [ ] **Auth JWT avec refresh**
  - Token refresh automatique
  - Interceptor Axios
  - Gestion expiration

## 🟠 Priorité HAUTE (Sprint 1-2)

### 2. Tests & Qualité du Code

**Effort** : 1 semaine | **ROI** : Très élevé

- [ ] **Tests E2E avec Playwright**

  - Workflow création expression
  - Installation PWA
  - Mode offline
  - Authentification

- [ ] **Tests unitaires manquants**

  - Hooks (useNotifications, useOfflineStorage)
  - Services (NotificationService, MediaEnrichment)
  - Composants critiques

- [ ] **Couverture 70%+**

  - Actuellement : ~30%
  - Focus sur code critique
  - Intégration CI/CD

- [ ] **Performance**
  - Lighthouse CI
  - Bundle analysis
  - Optimisation images

### 3. Corrections Bugs Existants

**Effort** : 2-3 jours | **Impact** : Élevé

- [ ] **Fix tests ExpressionFeed**

  - 6 tests échouent actuellement
  - Props motion non reconnues

- [ ] **Warnings Next.js viewport**

  - Migrer vers export viewport
  - Nettoyer metadata

- [ ] **ESLint Service Worker**
  - Erreurs dans sw.js
  - Types manquants

## 🟡 Priorité MOYENNE (Sprint 3-4)

### 4. Push Notifications Natives

**Effort** : 1 semaine | **Impact** : Engagement ++

- [ ] **Configuration VAPID**

  ```bash
  npm install web-push
  npx web-push generate-vapid-keys
  ```

- [ ] **Backend Django**

  - Model PushSubscription
  - Service d'envoi Celery
  - Templates notifications

- [ ] **Frontend**

  - UI demande permission
  - Gestion subscription
  - Préférences utilisateur

- [ ] **Types de notifications**
  - Nouvelle relation
  - Mention dans commentaire
  - Milestone atteint
  - Message système

### 5. Éditeur Riche d'Expressions

**Effort** : 1 semaine | **Impact** : UX ++

- [ ] **Markdown Editor**

  - react-markdown + remark
  - Preview temps réel
  - Toolbar personnalisée

- [ ] **Fonctionnalités sociales**

  - Mentions @utilisateur
  - Hashtags #pilier
  - Liens automatiques

- [ ] **Médias améliorés**

  - Drag & drop multiple
  - Réorganisation
  - Légendes

- [ ] **Templates**
  - Modèles prédéfinis
  - Sauvegarde brouillons
  - Historique versions

### 6. Système de Commentaires

**Effort** : 1-2 semaines | **Impact** : Engagement ++

- [ ] **Architecture**

  - Modèle Django Comment
  - API REST/GraphQL
  - Temps réel optionnel

- [ ] **UI Commentaires**

  - Thread imbriqués
  - Réactions emoji
  - Markdown support

- [ ] **Modération**

  - Signalement
  - Auto-modération
  - Dashboard admin

- [ ] **Notifications**
  - Réponse à commentaire
  - Mention dans commentaire
  - Nouveau follower

## 🟢 Priorité BASSE (Sprint 5+)

### 7. Analytics Dashboard Personnel

**Effort** : 1 semaine | **Nice to have**

- [ ] **Statistiques**

  - Vues/Relations/Impact
  - Évolution temporelle
  - Top expressions

- [ ] **Visualisations**

  - Chart.js ou Recharts
  - Graphiques interactifs
  - Export PDF/CSV

- [ ] **Insights**
  - Meilleurs horaires
  - Piliers performants
  - Suggestions contenu

### 8. Internationalisation (i18n)

**Effort** : 1 semaine | **Expansion**

- [ ] **Framework**

  - next-intl setup
  - Structure dossiers
  - Routing i18n

- [ ] **Traductions**

  - Français (défaut)
  - Anglais
  - Espagnol
  - Allemand

- [ ] **UI**
  - Language switcher
  - Détection auto
  - Persistance choix

### 9. Recherche Avancée

**Effort** : 1 semaine | **Scale**

- [ ] **Filtres avancés**

  - Multi-critères
  - Sauvegarde filtres
  - Filtres rapides

- [ ] **Elasticsearch**

  - Setup backend
  - Indexation
  - Recherche fuzzy

- [ ] **UX Recherche**
  - Autocomplete
  - Historique
  - Suggestions

### 10. Fonctionnalités Sociales Avancées

**Effort** : 2 semaines | **Croissance**

- [ ] **Profils enrichis**

  - Bio markdown
  - Liens sociaux
  - Badges/Achievements

- [ ] **Follow system**

  - Suivre utilisateurs
  - Feed personnalisé
  - Notifications follows

- [ ] **Groupes/Communautés**
  - Créer des groupes
  - Modération groupe
  - Events groupe

## 📊 Métriques de Succès

Pour chaque fonctionnalité, mesurer :

- **Adoption** : % utilisateurs qui l'utilisent
- **Rétention** : Impact sur rétention J7/J30
- **Engagement** : Actions/utilisateur
- **Performance** : Temps de chargement
- **Bugs** : Taux d'erreur

## 🚀 Quick Wins (< 1 jour)

1. **Améliorer le README**

   - Screenshots
   - Guide installation
   - Architecture

2. **GitHub Actions**

   - Tests automatiques
   - Build check
   - Lighthouse

3. **Sentry Integration**

   - Error tracking
   - Performance monitoring
   - User feedback

4. **Meta tags SEO**

   - Open Graph complet
   - Twitter Cards
   - Schema.org

5. **Animations polish**
   - Loading states
   - Skeleton screens
   - Micro-interactions

## 📅 Planning Suggéré

**Semaine 1-2** : Backend Integration + Tests
**Semaine 3-4** : Push Notifications + Editor
**Semaine 5-6** : Comments + Analytics
**Semaine 7-8** : i18n + Search
**Semaine 9+** : Social Features + Scale

## 🎯 Objectifs Business

1. **Rétention J7** : 40% → 60%
2. **DAU/MAU** : 0.15 → 0.25
3. **Expressions/User** : 2 → 5
4. **Time on Site** : 5min → 10min
5. **Install Rate** : 5% → 15%

## 💡 Notes

- Prioriser les features qui augmentent l'engagement
- Tester chaque feature avec un subset d'utilisateurs
- Mesurer l'impact avant de scaler
- Garder la simplicité de l'UX
- Mobile-first toujours
