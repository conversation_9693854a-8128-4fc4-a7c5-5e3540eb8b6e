# 📋 Avancement - Implémentation URLs Présignées MinIO

**Date**: 25 mai 2025  
**Module**: Backend PillarScan - Media URLs  
**Status**: 🔧 En cours d'implémentation

## 🎯 Objectif

Implémenter la génération d'URLs présignées MinIO pour les médias des expressions PillarScan, similaire à ce qui est fait dans CivicStore.

## 📝 Analyse de la Solution CivicStore

Dans CivicStore, le processus est :

```python
# civicstore/services/expression/read/media_read_service.py
def get_presigned_urls(self, media_list):
    """Génère des URLs présignées pour une liste de médias"""
    for media in media_list:
        media['url'] = self.minio_service.get_presigned_url(
            bucket_name=media['bucket_name'],
            object_name=media['object_name'],
            expires=timedelta(hours=1)
        )
    return media_list
```

## 🔧 Implémentation Requise pour PillarScan

### 1. Modifier le Serializer

Dans `pillar_scan/serializers.py`, la méthode `get_media_urls()` doit :

```python
def get_media_urls(self, obj):
    """Génère les URLs présignées pour les médias"""
    if not obj.media_refs:
        return {}
    
    media_service = MediaReadService()
    media_urls = {}
    
    for role, media_id in obj.media_refs.items():
        try:
            # Récupérer les métadonnées du média
            media = MediaMetadata.objects.get(media_id=media_id)
            
            # Générer l'URL présignée
            presigned_url = media_service.get_presigned_url(
                bucket_name=media.bucket_name,
                object_name=media.object_name,
                expires=timedelta(hours=24)
            )
            
            media_urls[role] = {
                'id': str(media_id),
                'url': presigned_url,
                'media_type': media.media_type,
                'thumbnail_url': presigned_url  # TODO: Générer une vraie miniature
            }
        except MediaMetadata.DoesNotExist:
            continue
            
    return media_urls
```

### 2. Optimiser les Requêtes

Pour éviter les N+1 queries :

```python
# Dans la vue ExpressionListView
def get_queryset(self):
    queryset = super().get_queryset()
    if self.request.query_params.get('with_media') == 'true':
        # Précharger les médias
        media_ids = []
        for expr in queryset:
            if expr.media_refs:
                media_ids.extend(expr.media_refs.values())
        
        # Une seule requête pour tous les médias
        medias = MediaMetadata.objects.filter(
            media_id__in=media_ids
        ).select_related('created_by')
        
        # Cache pour éviter les requêtes répétées
        self._media_cache = {str(m.media_id): m for m in medias}
    
    return queryset
```

## 🐛 Problème Actuel

Le test montre que :
- L'upload fonctionne ✅
- L'expression est créée avec media_refs ✅
- Mais media_urls retourne `[]` au lieu d'un objet ❌

### Diagnostic

1. **Format incorrect** : `media_urls` devrait être un objet `{}`, pas un tableau `[]`
2. **Transformation manquante** : Les media_refs ne sont pas transformés en URLs
3. **Service non connecté** : MediaReadService n'est peut-être pas utilisé

## 📋 Actions Requises

### Backend (Django)

1. [ ] Vérifier que MediaReadService est importé dans le serializer
2. [ ] Implémenter la méthode get_media_urls correctement
3. [ ] S'assurer que MinIOService est configuré
4. [ ] Tester la génération d'URLs présignées
5. [ ] Optimiser avec prefetch_related si nécessaire

### Frontend (Next.js)

1. [ ] Adapter ExpressionCard pour gérer le nouveau format
2. [ ] Ajouter un fallback si media_urls est vide
3. [ ] Gérer l'expiration des URLs (recharger si nécessaire)

## 🚀 Prochaines Étapes

1. **Vérifier l'implémentation backend**
   - Le serializer utilise-t-il MediaReadService ?
   - Les imports sont-ils corrects ?
   - Y a-t-il des erreurs dans les logs Django ?

2. **Tester avec des expressions existantes**
   - Récupérer une expression qui a déjà des médias
   - Vérifier le format de media_urls retourné

3. **Implémenter le fallback frontend**
   - Si media_urls est vide mais media_refs existe
   - Construire des URLs temporaires ou afficher un placeholder

## 📝 Code de Test

```javascript
// Test pour vérifier le format attendu
if (expression.media_urls && typeof expression.media_urls === 'object') {
  Object.entries(expression.media_urls).forEach(([role, media]) => {
    console.log(`${role}:`, {
      hasUrl: !!media.url,
      isPresigned: media.url?.includes('X-Amz-Signature'),
      hasThumbnail: !!media.thumbnail_url
    });
  });
}
```

## 🎯 Résultat Attendu

```json
{
  "media_urls": {
    "image_0": {
      "id": "uuid-here",
      "url": "https://minio.example.com/bucket/path?X-Amz-Algorithm=...",
      "media_type": "image",
      "thumbnail_url": "https://minio.example.com/bucket/thumb?X-Amz-Algorithm=..."
    }
  }
}
```