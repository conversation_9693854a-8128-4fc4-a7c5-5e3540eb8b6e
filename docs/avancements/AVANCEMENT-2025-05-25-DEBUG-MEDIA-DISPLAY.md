# 📋 Avancement - Debug Affichage Médias dans Expressions

**Date**: 25 mai 2025  
**Module**: Affichage des médias uploadés  
**Status**: 🔍 En investigation

## 🐛 Problème Identifié

Les images uploadées avec les expressions ne s'affichent pas, même si l'upload semble réussir.

## 🔍 Investigation Pipeline Complet

### 1. Frontend - Upload ✅
- Le composant `SimpleImageUpload` capture correctement les fichiers
- `useCreateExpression` appelle `createExpressionWithMedia` avec les fichiers
- Le header `X-Country-Code` est maintenant correctement envoyé

### 2. API Client - Envoi ✅  
```typescript
// lib/api/client.ts - createExpressionWithMedia
- Crée l'expression d'abord
- Upload les médias avec FormData
- Structure correcte: file, media_type, media_role, context, context_id
```

### 3. Backend - Réception ❓
Tests effectués :
- Via Node.js : Erreur "Aucun fichier fourni" 
- Via navigateur : À tester avec test-media-browser.html

### 4. Backend - Transformation media_refs → media_urls ❌

**Problème principal identifié** : Le backend n'implémente pas complètement la transformation :

```python
# pillar_scan/serializers.py
def get_media_urls(self, obj):
    """Génère les URLs des médias"""
    # TODO: Générer les vraies URLs depuis MediaHub
    return {
        role: f"/api/v2/media/{media_id}"
        for role, media_id in obj.media_refs.items()
    }
```

Les URLs générées sont des placeholders, pas des vraies URLs MinIO.

### 5. Frontend - Affichage ⚠️
Le composant `ExpressionCard` attend :
```typescript
expression.media_urls: {
  "image_0": {
    id: string,
    url: string,        // URL MinIO présignée
    thumbnail_url: string
  }
}
```

Mais reçoit actuellement :
```typescript
expression.media_urls: undefined
// ou
expression.media_refs: ["uuid1", "uuid2"]
```

## 📊 Diagnostic

### Flux attendu :
1. Upload fichier → MediaHub → MinIO (stockage)
2. Expression.media_refs = [media_id1, media_id2]
3. Serializer transforme : media_refs → media_urls avec URLs présignées
4. Frontend affiche les images via les URLs

### Flux actuel :
1. Upload fichier → ❓ (peut-être échoue)
2. Expression.media_refs = [] (vide)
3. media_urls = undefined
4. Pas d'affichage

## 🔧 Solutions Proposées

### Court terme (Frontend only) :
1. Vérifier que l'upload réussit vraiment
2. Implémenter un fallback pour media_refs si media_urls absent
3. Ajouter des logs détaillés pour tracer le problème

### Long terme (Backend requis) :
1. Implémenter `with_media=true` dans l'endpoint expressions
2. Utiliser MediaReadService pour générer les vraies URLs MinIO
3. Optimiser avec select_related/prefetch_related

## 📝 Prochaines Étapes

1. **Immédiat** : Tester avec test-media-browser.html depuis un navigateur
2. **Debug** : Vérifier les logs backend lors de l'upload
3. **Vérifier** : État dans MinIO après upload
4. **Backend** : Implémenter la transformation media_refs → media_urls

## 🚀 Actions à faire

- [ ] Test navigateur avec test-media-browser.html
- [ ] Vérifier logs Django lors de l'upload
- [ ] Vérifier présence des fichiers dans MinIO
- [ ] Créer ticket backend pour implémenter media_urls
- [ ] Implémenter workaround frontend si nécessaire