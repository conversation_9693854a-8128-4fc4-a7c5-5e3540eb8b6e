# 📊 AVANCEMENT : CLASSIFICATION AI AVEC AMAZON COMPREHEND

> Date : 24 mai 2025
> Au<PERSON><PERSON> : Assistant <PERSON>
> Tâche : Implémentation du système de classification AI des expressions

## 🎯 OBJECTIF

Implémenter un système de classification automatique des expressions citoyennes utilisant Amazon Comprehend pour les classer dans la hiérarchie des 12 piliers du développement humain.

## ✅ RÉALISATIONS

### 1. Service de Classification AI (Backend Django)

#### PillarScanAIClassificationService
- **Fichier** : `pillar_scan/services/ai_classification_service.py`
- **Fonctionnalités** :
  - Classification en 2 passes (structure + entités)
  - Support multi-langues (fr, en, es, de, it, pt)
  - Analyse de sentiment avec intensité
  - Extraction d'entités et phrases clés
  - Cache de la hiérarchie des piliers
  - Gestion des erreurs avec classification par défaut

#### Architecture de Classification
```python
# Hiérarchie à 4 niveaux
DOMAIN → CATEGORY → SUBCATEGORY → TOPIC

# Exemple de chemin complet
"BIEN_ETRE/SANTE_PHYSIQUE/ACCES_SOINS/HOPITAUX_CLINIQUES"
```

### 2. Tâches Celery Asynchrones

#### classification_task
- **Fichier** : `pillar_scan/tasks/classification_tasks.py`
- **Fonctionnalités** :
  - Classification asynchrone post-création
  - Sauvegarde dans ExpressionImputationPath
  - Mise à jour de l'expression avec résultats
  - Notification SSE après completion
  - Retry automatique en cas d'échec (max 3 fois)

#### Tâches Additionnelles
- `notify_classification_complete` : Notification temps réel
- `batch_classify_expressions` : Classification en batch
- `reprocess_failed_classifications` : Retraitement périodique

### 3. API REST Endpoints

#### ViewSets Créés
- **PillarScanExpressionViewSet** : CRUD avec classification
- **ClassificationCategoryViewSet** : Consultation hiérarchie

#### Endpoints Implémentés
```
POST   /api/v2/pillarscan/classification/expressions/
GET    /api/v2/pillarscan/classification/expressions/{id}/classification/
POST   /api/v2/pillarscan/classification/expressions/classify_batch/
GET    /api/v2/pillarscan/classification/expressions/classification_stats/
POST   /api/v2/pillarscan/classification/expressions/{id}/override_classification/
GET    /api/v2/pillarscan/classification/categories/
GET    /api/v2/pillarscan/classification/categories/search/
```

### 4. Frontend React Components

#### Hook useClassification
- **Fichier** : `hooks/useClassification.ts`
- **Fonctionnalités** :
  - Récupération résultat classification avec polling
  - Correction manuelle de classification
  - Gestion hiérarchie des piliers
  - Statistiques de classification
  - Preview en temps réel

#### ClassificationPreview Component
- **Fichier** : `components/classification/ClassificationPreview.tsx`
- **Fonctionnalités** :
  - Affichage temps réel pendant la saisie
  - Visualisation scores de confiance
  - Affichage sentiment avec intensité
  - Status badges (pending, completed, failed, manual)
  - Phrases clés extraites

### 5. Tests Unitaires

#### Tests Backend
- **Fichier** : `pillar_scan/tests/test_ai_classification.py`
- Tests du service de classification
- Tests des endpoints API
- Tests des tâches Celery
- Mocks complets d'Amazon Comprehend

## 📊 STRUCTURE DE DONNÉES

### ExpressionImputationPath (Modèle Existant)
```python
{
    expression_id: UUID
    category_code: str
    subcategory_code: str
    topic_code: str
    expression_type_code: str
    category_confidence: float
    subcategory_confidence: float
    topic_confidence: float
    sentiment_value: str
    sentiment_intensity: float
}
```

### Résultat de Classification
```json
{
  "classification": {
    "domain": {"code": "BIEN_ETRE", "confidence": 0.92},
    "category": {"code": "SANTE_PHYSIQUE", "name": "Santé physique", "confidence": 0.90},
    "subcategory": {"code": "ACCES_SOINS", "name": "Accès aux soins", "confidence": 0.85},
    "topic": {"code": "HOPITAUX_CLINIQUES", "name": "Infrastructure hospitalière", "confidence": 0.80}
  },
  "sentiment": {
    "value": "NEGATIVE",
    "intensity": 0.75,
    "scores": {"Positive": 0.1, "Negative": 0.75, "Neutral": 0.1, "Mixed": 0.05}
  },
  "entities": [
    {"text": "hôpital", "type": "MORAL", "score": 0.95, "person_id": null}
  ],
  "key_phrases": [
    {"text": "manque de médicaments", "score": 0.88}
  ],
  "languages": ["fr"],
  "metadata": {
    "model_version": "1.0.0",
    "classification_date": "2025-05-24T10:30:00Z",
    "text_length": 35,
    "word_count": 5
  }
}
```

## 🔧 CONFIGURATION REQUISE

### Variables d'Environnement
```bash
# Amazon Comprehend
AWS_REGION=eu-west-1
AWS_ACCESS_KEY_ID=xxx
AWS_SECRET_ACCESS_KEY=xxx
COMPREHEND_PILLAR_ENDPOINT=arn:aws:comprehend:eu-west-1:xxx:document-classifier-endpoint/pillarscan-classifier
COMPREHEND_MODEL_VERSION=1.0.0
```

### Settings Django
```python
# Celery pour tâches asynchrones
CELERY_BROKER_URL = 'redis://localhost:6379'
CELERY_RESULT_BACKEND = 'redis://localhost:6379'

# Langues supportées
SUPPORTED_LANGUAGES = ['fr', 'en', 'es', 'de', 'it', 'pt']
```

## 🚀 INTÉGRATION AVEC SYSTÈMES EXISTANTS

### 1. Souveraineté des Données
- Toutes les classifications respectent le filtre par pays
- Header `X-Country-Code` obligatoire sur toutes les requêtes
- Données isolées par pays dans Cassandra

### 2. Elasticsearch
- Indexation automatique après classification
- Recherche enrichie avec catégories et sentiment
- Facettes par pilier dans les résultats

### 3. Notifications SSE
- Notification temps réel quand classification terminée
- Intégration avec NotificationService existant

## 📈 MÉTRIQUES ET MONITORING

### KPIs Définis
- **Accuracy** : Target > 85%
- **Coverage** : Target > 95%
- **Confidence moyenne** : Target > 75%
- **Temps de traitement** : Target < 2s (p95)

### Monitoring Service
- `ClassificationMonitoringService` pour stats par pays
- Dashboard temps réel des classifications
- Tracking des erreurs et retraitement automatique

## 🔍 PROCHAINES ÉTAPES

1. **Entraînement Modèle Custom**
   - Dataset annoté par experts domaine
   - Validation croisée multi-langues
   - Déploiement endpoint Amazon Comprehend

2. **Optimisations**
   - Cache Redis pour classifications récurrentes
   - Batch processing pour imports massifs
   - Preview léger côté client

3. **Évolutions Fonctionnelles**
   - Classification multi-labels
   - Détection d'urgence dans les expressions
   - Clustering d'expressions similaires
   - Recommandations basées sur classification

## ⚠️ POINTS D'ATTENTION

1. **Coûts AWS** : Surveiller l'usage d'Amazon Comprehend
2. **Latence** : Classification asynchrone pour ne pas bloquer l'UX
3. **Biais culturels** : Adapter les modèles par région
4. **Transparence** : Permettre aux users de voir et contester

## 📝 DOCUMENTATION CRÉÉE

- Service AI Classification : `ai_classification_service.py`
- Tâches Celery : `classification_tasks.py`
- API Views : `views.py` + `serializers.py`
- Tests : `test_ai_classification.py`
- Frontend Hook : `useClassification.ts`
- Component : `ClassificationPreview.tsx`

## ✅ STATUT FINAL

✅ Service backend implémenté
✅ Tâches Celery configurées
✅ API endpoints créés
✅ Tests unitaires écrits
✅ Frontend components développés
✅ Documentation complète

La classification AI est maintenant **opérationnelle** et prête pour l'entraînement du modèle custom Amazon Comprehend.