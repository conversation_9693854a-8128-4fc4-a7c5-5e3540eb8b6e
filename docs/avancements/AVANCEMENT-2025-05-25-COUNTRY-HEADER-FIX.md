# 📋 Avancement - Correction Header X-Country-Code

**Date**: 25 mai 2025  
**Module**: API Client - Upload de médias  
**Status**: ✅ Corrigé

## 🐛 Problème Identifié

L'upload de médias échouait avec l'erreur :
```
{"error": "Country selection required", "detail": "X-Country-Code header is missing. Please select a country.", "code": "COUNTRY_REQUIRED"}
```

### Cause
- L'endpoint `/api/v2/media/upload/` nécessite le header `X-Country-Code`
- Ce header n'était pas envoyé lors de l'upload de médias dans `createExpressionWithMedia`

## ✅ Solution Implémentée

### 1. Correction dans `lib/api/client.ts`

Ajout du header `X-Country-Code` lors de l'upload :

```typescript
const uploadHeaders: Record<string, string> = {
  'Authorization': `Bearer ${token}`,
  // Ne pas définir Content-Type pour FormData - le navigateur le fait automatiquement
};

// Ajouter le country code si disponible
const countryCode = this.getCountryCode();
if (countryCode) {
  uploadHeaders['X-Country-Code'] = countryCode;
  console.log('  X-Country-Code ajouté:', countryCode);
}

const uploadResponse = await fetch(uploadUrl, {
  method: 'POST',
  headers: uploadHeaders,
  body: uploadFormData
});
```

## 📝 Points Techniques

### Flux du Country Code
1. **Connexion** : L'utilisateur se connecte
2. **Récupération du profil** : `getAuthProfile()` récupère le pays depuis le backend
3. **Stockage** : Le pays est stocké via `setCountryCode()` dans le client API
4. **Utilisation** : Toutes les requêtes (sauf les exemptées) incluent automatiquement le header

### Endpoints Exemptés (pas besoin de X-Country-Code)
- `/api/v2/auth/*`
- `/api/v2/civicperson/auth/*`
- `/media/*` (accès aux fichiers)
- `/static/*`
- `/admin/*`

### Endpoints Nécessitant X-Country-Code
- `/api/v2/media/upload/` ✅
- `/api/v2/pillarscan/expressions/` (création)
- Tous les autres endpoints de l'API

## 🔍 Tests

### Build
```bash
npm run build
✓ Compiled successfully
✓ Linting and checking validity of types
✓ Generating static pages (14/14)
```

## 📊 Impact

- ✅ L'upload de médias fonctionne maintenant correctement
- ✅ Le country code est récupéré automatiquement du profil utilisateur
- ✅ Pas de régression sur les autres fonctionnalités

## 🚀 Next Steps

1. Tester l'upload de médias en conditions réelles
2. Vérifier que le country code est bien transmis dans tous les cas
3. S'assurer que les utilisateurs sans pays défini ont un message clair