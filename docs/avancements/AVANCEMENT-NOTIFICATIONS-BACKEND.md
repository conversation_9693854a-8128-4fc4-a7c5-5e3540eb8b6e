# Avancement - Notifications Backend

**Date**: 25 Mai 2025  
**Sprint**: Intégration notifications temps réel

## 🎯 Objectif

Configurer le backend Django pour supporter les notifications temps réel via SSE (Server-Sent Events) et connecter les événements métier aux notifications.

## ✅ Travaux réalisés

### 1. Configuration ASGI pour WebSocket/SSE

#### Fichiers créés/modifiés :

- ✅ `/notifycore/routing.py` - Routes WebSocket pour notifications
- ✅ `/asgi.py` - Ajout des routes notifycore et AuthMiddlewareStack

#### Code ajouté :

```python
# notifycore/routing.py
websocket_urlpatterns = [
    re_path(r'ws/notifications/$', consumers.NotificationSSEConsumer.as_asgi()),
]

# asgi.py
from notifycore.routing import websocket_urlpatterns as notifycore_ws
all_websocket_urlpatterns = civicstore_ws + notifycore_ws
```

### 2. Signaux Django pour événements métier

#### Fichiers créés :

- ✅ `/pillarscan/signals.py` - Signaux pour expressions et relates

#### Signaux implémentés :

- `notify_expression_created` - Log lors de création (préparé pour futures notifications)
- `notify_expression_related` - Notification à l'auteur lors d'un relate
- `notify_expression_classified` - Notification lors de classification AI

#### Configuration :

- ✅ Ajout dans `pillarscan/apps.py` pour charger les signaux au démarrage

### 3. Correction UUID dans SSENotificationView

#### Problème identifié :

- L'ID utilisateur Django est un UUID object, pas une string
- PersonUserService attend une string

#### Solution :

```python
# notifycore/api/sse_views.py ligne 60
persons = service.get_persons_for_user(str(user.id))
```

### 4. Tests créés

#### Tests unitaires :

- `/notifycore/tests/test_asgi_config.py` - Tests configuration ASGI
- `/pillarscan/tests/test_signals.py` - Tests signaux notifications
- `/notifycore/tests/test_sse_endpoint.py` - Tests endpoint SSE

#### Résultats tests :

- **ASGI Config**: 1/3 tests passent (problèmes avec structure ASGI)
- **Signaux**: Non exécutés (dépendances Cassandra)
- **SSE Endpoint**: 5/8 tests passent

## ⚠️ Problèmes identifiés

### 1. Erreur UUID via HTTP

- **Symptôme**: "badly formed hexadecimal UUID string" uniquement via HTTP
- **Cause**: Possible conflit avec middleware country ou contexte requête
- **Impact**: SSE fonctionne en test direct mais pas via serveur

### 2. Tests incomplets

- Dépendances manquantes (daphne pour WebSocket tests)
- Base de données de test existante
- Certains mocks incomplets

### 3. Authentification SSE

- L'endpoint attend un token en query param ou Bearer header
- PersonUserService nécessite une Person associée à l'utilisateur

## 📊 État actuel

### Backend notifications :

- ✅ Modèles Cassandra (déjà existants)
- ✅ SSENotificationView (fonctionnel en test direct)
- ✅ WebSocket Consumer configuré
- ✅ API REST complète
- ✅ NotificationService CRUD
- ✅ Configuration ASGI
- ✅ Signaux métier connectés
- ⚠️ Erreur UUID à résoudre pour production

### Frontend notifications :

- ✅ NotificationService avec SSE complet
- ✅ Store Zustand persistant
- ✅ Hook useNotifications
- ✅ NotificationCenter UI
- ✅ Service Worker
- ✅ Intégration layout

## 🔧 Prochaines étapes

1. **Résoudre l'erreur UUID** en production
2. **Améliorer les tests** avec mocks appropriés
3. **Tester E2E** avec un serveur ASGI (daphne/uvicorn)
4. **Monitoring** des connexions SSE en production

## 📝 Notes techniques

- L'utilisateur `<EMAIL>` a 4 Persons associées
- Le token JWT fonctionne correctement
- SSE répond en 200 lors des tests directs
- Le problème semble lié au contexte HTTP/middleware
