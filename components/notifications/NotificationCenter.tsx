'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, Check, X, CheckCheck } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { useNotifications } from '@/hooks/useNotifications';
import type { Notification, NotificationType } from '@/lib/services/NotificationService';

// Configuration des icônes et couleurs par type
const NOTIFICATION_CONFIG: Record<NotificationType, {
  icon: string;
  color: string;
  bgColor: string;
}> = {
  'badge_earned': {
    icon: '🏆',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50'
  },
  'streak_milestone': {
    icon: '🔥',
    color: 'text-orange-600',
    bgColor: 'bg-orange-50'
  },
  'expression_related': {
    icon: '💬',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50'
  },
  'pillar_expert': {
    icon: '👑',
    color: 'text-purple-600',
    bgColor: 'bg-purple-50'
  },
  'weekly_summary': {
    icon: '📊',
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-50'
  },
  'expression_classified': {
    icon: '🎯',
    color: 'text-green-600',
    bgColor: 'bg-green-50'
  },
  'community_milestone': {
    icon: '🎉',
    color: 'text-pink-600',
    bgColor: 'bg-pink-50'
  },
  'system.announcement': {
    icon: '📢',
    color: 'text-gray-800',
    bgColor: 'bg-gray-50'
  }
};

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onDismiss: (id: string) => void;
  onAction?: (notification: Notification) => void;
}

function NotificationItem({ 
  notification, 
  onMarkAsRead, 
  onDismiss,
  onAction 
}: NotificationItemProps) {
  const config = NOTIFICATION_CONFIG[notification.type];
  const timeAgo = formatDistanceToNow(new Date(notification.created_at), {
    addSuffix: true,
    locale: fr
  });

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, x: -100 }}
      className={cn(
        'relative p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors',
        !notification.is_read && 'bg-blue-50/30'
      )}
    >
      <div className="flex items-start gap-3">
        {/* Icône */}
        <div className={cn(
          'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center text-lg',
          config.bgColor
        )}>
          {config.icon}
        </div>

        {/* Contenu */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1">
              <h4 className={cn(
                'font-medium text-sm',
                !notification.is_read ? 'text-gray-900' : 'text-gray-700'
              )}>
                {notification.title}
              </h4>
              <p className="text-sm text-gray-800 mt-1">
                {notification.content || notification.message}
              </p>
              {notification.entity_id && notification.entity_type === 'expression' && (
                <Button
                  size="sm"
                  variant="primary"
                  className="mt-2"
                  onClick={() => onAction?.(notification)}
                >
                  Voir
                </Button>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-1">
              {!notification.is_read && (
                <button
                  onClick={() => onMarkAsRead(notification.notification_id || notification.id || '')}
                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                  aria-label="Marquer comme lu"
                >
                  <Check className="w-4 h-4 text-gray-700" />
                </button>
              )}
              <button
                onClick={() => onDismiss(notification.notification_id || notification.id || '')}
                className="p-1 hover:bg-gray-200 rounded transition-colors"
                aria-label="Masquer"
              >
                <X className="w-4 h-4 text-gray-700" />
              </button>
            </div>
          </div>

          {/* Timestamp */}
          <p className="text-xs text-gray-700 mt-2">{timeAgo}</p>
        </div>
      </div>

      {/* Indicateur non lu */}
      {!notification.is_read && (
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-500" />
      )}
    </motion.div>
  );
}

export function NotificationCenter() {
  const [isOpen, setIsOpen] = useState(false);
  const { isAuthenticated } = useAuth();
  const {
    notifications,
    unreadCount,
    isConnected,
    markAsRead,
    dismiss,
    markAllAsRead
  } = useNotifications();
  
  // Ne pas afficher le centre de notifications si non authentifié
  if (!isAuthenticated) {
    return null;
  }

  const visibleNotifications = notifications
    .filter(n => !n.is_dismissed)
    .slice(0, 10); // Limiter à 10 notifications visibles

  const handleAction = (notification: Notification) => {
    if (notification.entity_id && notification.entity_type === 'expression') {
      // Naviguer vers l'expression
      window.location.href = `/expressions/${notification.entity_id}`;
    }
    markAsRead(notification.notification_id || notification.id || '');
    setIsOpen(false);
  };

  return (
    <div className="relative">
      {/* Bouton cloche */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2"
        aria-label={`Notifications${unreadCount > 0 ? ` (${unreadCount} non lues)` : ''}`}
      >
        <Bell className={cn(
          'w-5 h-5',
          unreadCount > 0 ? 'text-blue-600' : 'text-gray-800'
        )} />
        
        {/* Badge de compteur */}
        {unreadCount > 0 && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-1 -right-1"
          >
            <Badge 
              variant="info" 
              size="sm"
              className="min-w-[20px] h-5 px-1 flex items-center justify-center"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          </motion.div>
        )}

        {/* Indicateur de connexion */}
        <div className={cn(
          'absolute bottom-0 right-0 w-2 h-2 rounded-full',
          isConnected ? 'bg-green-500' : 'bg-gray-400'
        )} />
      </Button>

      {/* Dropdown des notifications */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Overlay pour fermer en cliquant dehors */}
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Panel de notifications */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 overflow-hidden"
            >
              {/* Header */}
              <div className="p-4 border-b border-gray-200 bg-gray-50">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900">
                    Notifications
                  </h3>
                  {unreadCount > 0 && (
                    <button
                      onClick={markAllAsRead}
                      className="text-sm text-blue-600 hover:text-blue-700 flex items-center gap-1"
                    >
                      <CheckCheck className="w-4 h-4" />
                      Tout marquer comme lu
                    </button>
                  )}
                </div>
              </div>

              {/* Liste des notifications */}
              <div className="max-h-96 overflow-y-auto">
                {visibleNotifications.length > 0 ? (
                  <AnimatePresence>
                    {visibleNotifications.map((notification) => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                        onMarkAsRead={markAsRead}
                        onDismiss={dismiss}
                        onAction={handleAction}
                      />
                    ))}
                  </AnimatePresence>
                ) : (
                  <div className="p-8 text-center text-gray-700">
                    <Bell className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p className="text-sm">Aucune notification</p>
                  </div>
                )}
              </div>

              {/* Footer */}
              {visibleNotifications.length > 0 && (
                <div className="p-3 border-t border-gray-200 bg-gray-50">
                  <button
                    onClick={() => {
                      // TODO: Naviguer vers la page de toutes les notifications
                      setIsOpen(false);
                    }}
                    className="text-sm text-blue-600 hover:text-blue-700 w-full text-center"
                  >
                    Voir toutes les notifications
                  </button>
                </div>
              )}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}