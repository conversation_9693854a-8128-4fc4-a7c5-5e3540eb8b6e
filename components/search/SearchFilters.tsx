'use client';

import React from 'react';
import { Filter, X } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PILLARS } from '@/lib/types/pillarscan';
import type { SearchFilters as Filters } from '@/hooks/useExpressionSearch';

interface SearchFiltersProps {
  filters: Filters;
  onFiltersChange: (filters: Filters) => void;
  onReset: () => void;
}

export function SearchFilters({ filters, onFiltersChange, onReset }: SearchFiltersProps) {
  const handleFilterChange = (key: keyof Filters, value: string | number | undefined) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  return (
    <div className="space-y-4 p-4 bg-muted/30 rounded-lg">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-semibold flex items-center gap-2">
          <Filter className="h-4 w-4" />
          Filtres avancés
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={onReset}
          className="text-xs"
        >
          <X className="h-3 w-3 mr-1" />
          Réinitialiser
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Mood Filter */}
        <div className="space-y-2">
          <Label htmlFor="mood-filter">Humeur</Label>
          <Select
            value={filters.mood || ''}
            onValueChange={(value: string) => handleFilterChange('mood', value || undefined)}
          >
            <SelectTrigger id="mood-filter">
              <SelectValue placeholder="Toutes les humeurs" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Toutes</SelectItem>
              <SelectItem value="happy">😊 Heureux</SelectItem>
              <SelectItem value="frustrated">😤 Frustré</SelectItem>
              <SelectItem value="idea">💡 Idée</SelectItem>
              <SelectItem value="question">❓ Question</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Pillar Filter */}
        <div className="space-y-2">
          <Label htmlFor="pillar-filter">Pilier</Label>
          <Select
            value={filters.pillar || ''}
            onValueChange={(value: string) => handleFilterChange('pillar', value || undefined)}
          >
            <SelectTrigger id="pillar-filter">
              <SelectValue placeholder="Tous les piliers" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Tous</SelectItem>
              {PILLARS.map((pillar) => (
                <SelectItem key={pillar.id.toString()} value={pillar.id.toString()}>
                  {pillar.emoji} {pillar.name_fr}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Date From */}
        <div className="space-y-2">
          <Label htmlFor="date-from">Date de début</Label>
          <Input
            id="date-from"
            type="date"
            value={filters.date_from || ''}
            onChange={(e) => handleFilterChange('date_from', e.target.value || undefined)}
          />
        </div>

        {/* Date To */}
        <div className="space-y-2">
          <Label htmlFor="date-to">Date de fin</Label>
          <Input
            id="date-to"
            type="date"
            value={filters.date_to || ''}
            onChange={(e) => handleFilterChange('date_to', e.target.value || undefined)}
          />
        </div>
      </div>
    </div>
  );
}