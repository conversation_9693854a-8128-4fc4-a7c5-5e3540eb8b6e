'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Heart, MapPin, User } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import type { SearchResult } from '@/hooks/useExpressionSearch';

interface SearchResultCardProps {
  result: SearchResult;
  onRelate?: (id: string) => void;
}

export function SearchResultCard({ result, onRelate }: SearchResultCardProps) {
  const moodEmoji: Record<string, string> = {
    happy: '😊',
    frustrated: '😤',
    idea: '💡',
    question: '❓',
  };

  return (
    <Card className="hover:shadow-lg transition-shadow cursor-pointer">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <span className="text-2xl">{moodEmoji[result.mood] || '🎭'}</span>
            {result.user && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <User className="h-3 w-3" />
                <span>{result.user.nickname}</span>
                <Badge variant="secondary" size="sm">
                  Niv. {result.user.level}
                </Badge>
              </div>
            )}
          </div>
          <time className="text-xs text-muted-foreground">
            {formatDistanceToNow(new Date(result.created_at), { 
              addSuffix: true, 
              locale: fr 
            })}
          </time>
        </div>
        
        <p className="text-sm mb-3 line-clamp-3" 
           dangerouslySetInnerHTML={{ 
             __html: result.highlight || result.content 
           }} 
        />
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 text-xs text-muted-foreground">
            {result.location.city && (
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{result.location.city}</span>
              </div>
            )}
            <span>Score: {result.score.toFixed(2)}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onRelate?.(result.id);
              }}
              className="flex items-center gap-1 text-sm hover:text-primary transition-colors"
            >
              <Heart className="h-4 w-4" />
              <span>{result.engagement.relates}</span>
            </button>
            <Badge variant="secondary" size="sm">
              Impact: {result.engagement.impact_score}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}