'use client';

import { useState } from 'react';
import { Search, Filter, X, MapPin } from 'lucide-react';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useExpressionSearch } from '@/hooks/useExpressionSearch';
import { SearchResultCard } from './SearchResultCard';
import { SearchFilters } from './SearchFilters';
import { cn } from '@/lib/utils';

export function ExpressionSearch() {
  const {
    searchTerm,
    filters,
    results,
    total,
    page,
    totalPages,
    aggregations,
    isLoading,
    isFetching,
    hasNextPage,
    hasPreviousPage,
    sortBy,
    updateSearchTerm,
    updateFilters,
    clearFilters,
    clearSearch,
    nextPage,
    previousPage,
    changeSortBy,
    // prefetchNextPage, // TODO: Utiliser pour le préchargement
  } = useExpressionSearch();

  const [showFilters, setShowFilters] = useState(false);

  // Compter les filtres actifs
  const activeFiltersCount = Object.keys(filters).filter(
    key => filters[key as keyof typeof filters] !== undefined
  ).length;

  return (
    <div className="w-full space-y-4">
      {/* Barre de recherche */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            value={searchTerm}
            onChange={(e) => updateSearchTerm(e.target.value)}
            placeholder="Rechercher des expressions..."
            className="pl-10 pr-4"
          />
          {searchTerm && (
            <button
              onClick={clearSearch}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        
        <Button
          variant="secondary"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className={cn(
            "relative",
            activeFiltersCount > 0 && "ring-2 ring-primary"
          )}
        >
          <Filter className="h-4 w-4" />
          {activeFiltersCount > 0 && (
            <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center">
              {activeFiltersCount}
            </span>
          )}
        </Button>

        <Select value={sortBy} onValueChange={changeSortBy}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Trier par" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="relevance">Pertinence</SelectItem>
            <SelectItem value="date">Plus récent</SelectItem>
            <SelectItem value="engagement">Populaire</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Filtres */}
      {showFilters && (
        <SearchFilters
          filters={filters}
          onFiltersChange={updateFilters}
          onReset={clearFilters}
        />
      )}

      {/* Résultats */}
      <div>
        {/* Header des résultats */}
        <div className="flex items-center justify-between mb-4">
          <p className="text-sm text-muted-foreground">
            {isLoading ? (
              <Skeleton className="h-4 w-32" />
            ) : (
              <>
                {total > 0 ? (
                  <>
                    <span className="font-medium">{total}</span> résultat{total > 1 ? 's' : ''} trouvé{total > 1 ? 's' : ''}
                  </>
                ) : (
                  'Aucun résultat trouvé'
                )}
              </>
            )}
          </p>
          
          {isFetching && !isLoading && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <div className="h-3 w-3 rounded-full bg-primary animate-pulse" />
              Mise à jour...
            </div>
          )}
        </div>

        {/* Liste des résultats */}
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <SearchResultSkeleton key={i} />
            ))}
          </div>
        ) : results.length > 0 ? (
          <div className="space-y-3">
            {results.map((expression) => (
              <SearchResultCard
                key={expression.id}
                result={expression}
                onRelate={(id) => {
                  // TODO: Implémenter la fonction relate
                  console.log('Relate to expression:', id);
                }}
              />
            ))}
          </div>
        ) : searchTerm || activeFiltersCount > 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground mb-4">
              Aucune expression ne correspond à votre recherche.
            </p>
            <Button variant="secondary" onClick={clearSearch}>
              Effacer la recherche
            </Button>
          </div>
        ) : null}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <Button
              variant="secondary"
              onClick={previousPage}
              disabled={!hasPreviousPage}
            >
              Précédent
            </Button>
            
            <span className="text-sm text-muted-foreground">
              Page {page} sur {totalPages}
            </span>
            
            <Button
              variant="secondary"
              onClick={nextPage}
              disabled={!hasNextPage}
            >
              Suivant
            </Button>
          </div>
        )}
      </div>

      {/* Agrégations (si disponibles et pas de recherche) */}
      {!searchTerm && !isLoading && aggregations && (
        <div className="mt-8 space-y-4">
          {/* Top moods */}
          {aggregations.moods.length > 0 && (
            <div>
              <h3 className="text-sm font-medium mb-2">Humeurs populaires</h3>
              <div className="flex flex-wrap gap-2">
                {aggregations.moods.map((mood) => (
                  <Badge
                    key={mood.key}
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => updateFilters({ mood: mood.key })}
                  >
                    {getMoodEmoji(mood.key)} {mood.count}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Top villes */}
          {aggregations.cities.length > 0 && (
            <div>
              <h3 className="text-sm font-medium mb-2">Villes actives</h3>
              <div className="flex flex-wrap gap-2">
                {aggregations.cities.slice(0, 10).map((city) => (
                  <Badge
                    key={city.key}
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => updateSearchTerm(city.key)}
                  >
                    <MapPin className="h-3 w-3 mr-1" />
                    {city.key} ({city.count})
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

function SearchResultSkeleton() {
  return (
    <div className="p-4 border rounded-lg">
      <div className="flex items-start gap-3">
        <Skeleton className="h-10 w-10 rounded-full" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <div className="flex gap-4 mt-2">
            <Skeleton className="h-3 w-20" />
            <Skeleton className="h-3 w-24" />
            <Skeleton className="h-3 w-16" />
          </div>
        </div>
      </div>
    </div>
  );
}

function getMoodEmoji(mood: string): string {
  const moods: Record<string, string> = {
    happy: '😊',
    neutral: '😐',
    sad: '😢',
    angry: '😠',
  };
  return moods[mood] || '😐';
}