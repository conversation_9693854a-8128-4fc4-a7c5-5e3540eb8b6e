'use client';

import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

export type Mood = 'frustrated' | 'happy' | 'idea' | 'question';

interface MoodOption {
  value: Mood;
  emoji: string;
  label: string;
  color: string;
  bgColor: string;
  description: string;
}

const moods: MoodOption[] = [
  {
    value: 'frustrated',
    emoji: '😤',
    label: 'Frustré',
    color: 'text-red-600',
    bgColor: 'bg-red-50 hover:bg-red-100',
    description: 'Signaler un problème'
  },
  {
    value: 'happy',
    emoji: '😊',
    label: 'Heureux',
    color: 'text-green-600',
    bgColor: 'bg-green-50 hover:bg-green-100',
    description: 'Partager du positif'
  },
  {
    value: 'idea',
    emoji: '💡',
    label: 'Idée',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50 hover:bg-yellow-100',
    description: 'Proposer une solution'
  },
  {
    value: 'question',
    emoji: '❓',
    label: 'Question',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 hover:bg-blue-100',
    description: 'Demander des avis'
  }
];

interface MoodSelectorProps {
  value?: Mood;
  onChange: (mood: Mood) => void;
  className?: string;
}

export function MoodSelector({ value, onChange, className }: MoodSelectorProps) {
  return (
    <div className={cn('space-y-3', className)}>
      <h3 className="text-sm font-medium text-gray-700">
        Comment vous sentez-vous ?
      </h3>
      
      <div className="grid grid-cols-2 gap-3 sm:grid-cols-4">
        {moods.map((mood) => {
          const isSelected = value === mood.value;
          
          return (
            <motion.button
              key={mood.value}
              type="button"
              onClick={() => onChange(mood.value)}
              className={cn(
                'relative flex flex-col items-center justify-center p-4 rounded-xl border-2 transition-all',
                'focus:outline-none focus:ring-2 focus:ring-offset-2',
                isSelected ? [
                  'border-current shadow-md',
                  mood.color,
                  mood.bgColor.replace('hover:', '')
                ] : [
                  'border-gray-200 hover:border-gray-300',
                  mood.bgColor
                ]
              )}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              aria-label={`Sélectionner l'humeur ${mood.label}`}
              aria-pressed={isSelected}
            >
              {/* Selected indicator */}
              {isSelected && (
                <motion.div
                  className="absolute -top-1 -right-1 w-5 h-5 bg-current rounded-full flex items-center justify-center"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: 'spring', stiffness: 500 }}
                >
                  <svg className="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </motion.div>
              )}
              
              {/* Emoji */}
              <motion.span 
                className="text-3xl mb-2"
                animate={{ scale: isSelected ? 1.1 : 1 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                {mood.emoji}
              </motion.span>
              
              {/* Label */}
              <span className={cn(
                'font-medium text-sm',
                isSelected ? mood.color : 'text-gray-700'
              )}>
                {mood.label}
              </span>
              
              {/* Description (hidden on mobile) */}
              <span className={cn(
                'hidden sm:block text-xs mt-1',
                isSelected ? mood.color : 'text-gray-700'
              )}>
                {mood.description}
              </span>
            </motion.button>
          );
        })}
      </div>
    </div>
  );
}