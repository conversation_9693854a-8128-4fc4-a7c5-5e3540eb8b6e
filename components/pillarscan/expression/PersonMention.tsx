'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { PersonType, PersonReference, Person } from '@/types/person';
import { useDebounce } from '@/hooks/useDebounce';

interface PersonMentionProps {
  value: PersonReference[];
  onChange: (persons: PersonReference[]) => void;
  onSearch?: (query: string, type?: PersonType) => Promise<Person[]>;
  maxPersons?: number;
  placeholder?: string;
  className?: string;
}

// Icônes pour chaque type de personne
const personTypeIcons: Record<PersonType, string> = {
  [PersonType.HUMAN]: '👤',
  [PersonType.MORAL]: '🏢',
  [PersonType.GROUP]: '👥'
};

// Labels pour chaque type
const personTypeLabels: Record<PersonType, string> = {
  [PersonType.HUMAN]: 'Personne physique',
  [PersonType.MORAL]: 'Organisation',
  [PersonType.GROUP]: 'Groupe'
};

export function PersonMention({
  value = [],
  onChange,
  onSearch,
  maxPersons = 5,
  placeholder = 'Mentionner une personne ou organisation...',
  className
}: PersonMentionProps) {
  const [inputValue, setInputValue] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedType, setSelectedType] = useState<PersonType>(PersonType.HUMAN);
  const [searchResults, setSearchResults] = useState<Person[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  const debouncedSearch = useDebounce(inputValue, 300);

  // Recherche des personnes
  useEffect(() => {
    const searchPersons = async () => {
      if (!onSearch || !debouncedSearch.trim()) {
        setSearchResults([]);
        return;
      }

      setIsSearching(true);
      try {
        const results = await onSearch(debouncedSearch, selectedType);
        setSearchResults(results);
        setSelectedIndex(0);
      } catch (error) {
        console.error('Erreur lors de la recherche:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    searchPersons();
  }, [debouncedSearch, selectedType, onSearch]);

  // Gestion du clic externe
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Ajouter une personne
  const addPerson = useCallback((person: Person | null) => {
    if (value.length >= maxPersons) return;

    const newReference: PersonReference = person ? {
      person_id: person.person_id,
      person_code: person.person_code,
      person_name: person.display_name,
      person_type: person.person_type,
      role: 'target', // Par défaut
      needs_resolution: false
    } : {
      temp_id: `temp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      person_name: inputValue.trim(),
      person_type: selectedType,
      role: 'target',
      needs_resolution: true
    };

    onChange([...value, newReference]);
    setInputValue('');
    setShowDropdown(false);
    setSearchResults([]);
  }, [value, onChange, maxPersons, inputValue, selectedType]);

  // Supprimer une personne
  const removePerson = useCallback((index: number) => {
    onChange(value.filter((_, i) => i !== index));
  }, [value, onChange]);

  // Gestion du clavier
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (searchResults.length > 0) {
        addPerson(searchResults[selectedIndex]);
      } else if (inputValue.trim()) {
        // Créer une nouvelle personne temporaire
        addPerson(null);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < searchResults.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev > 0 ? prev - 1 : 0);
    } else if (e.key === 'Escape') {
      setShowDropdown(false);
    }
  };

  // Changer le rôle d'une personne
  const changeRole = (index: number, role: 'target' | 'source' | 'owner') => {
    const updated = [...value];
    updated[index].role = role;
    onChange(updated);
  };

  return (
    <div className={cn('space-y-3', className)}>
      {/* Personnes sélectionnées */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {value.map((person, index) => (
            <motion.div
              key={person.person_id || person.temp_id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className={cn(
                'inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm',
                'border transition-colors',
                person.needs_resolution
                  ? 'bg-orange-50 border-orange-200 text-orange-700'
                  : 'bg-blue-50 border-blue-200 text-blue-700'
              )}
            >
              <span>{personTypeIcons[person.person_type]}</span>
              <span className="font-medium">{person.person_name}</span>
              
              {/* Sélecteur de rôle */}
              <select
                value={person.role}
                onChange={(e) => changeRole(index, e.target.value as 'target' | 'source' | 'owner')}
                className="text-xs bg-transparent border-0 focus:outline-none cursor-pointer"
                onClick={(e) => e.stopPropagation()}
              >
                <option value="target">Cible</option>
                <option value="source">Source</option>
                <option value="owner">Propriétaire</option>
              </select>

              {/* Bouton supprimer */}
              <button
                type="button"
                onClick={() => removePerson(index)}
                className="ml-1 hover:text-red-600 transition-colors"
                aria-label="Supprimer"
              >
                ×
              </button>

              {/* Indicateur de résolution */}
              {person.needs_resolution && (
                <span className="text-xs text-orange-600" title="Sera résolu après publication">
                  ⏳
                </span>
              )}
            </motion.div>
          ))}
        </div>
      )}

      {/* Input de recherche */}
      {value.length < maxPersons && (
        <div className="relative">
          <div className="flex gap-2">
            {/* Sélecteur de type */}
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value as PersonType)}
              className="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {Object.entries(personTypeLabels).map(([type, label]) => (
                <option key={type} value={type}>
                  {personTypeIcons[type as PersonType]} {label}
                </option>
              ))}
            </select>

            {/* Input de recherche */}
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={(e) => {
                setInputValue(e.target.value);
                setShowDropdown(true);
              }}
              onFocus={() => setShowDropdown(true)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className="flex-1 px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Dropdown de résultats */}
          <AnimatePresence>
            {showDropdown && (inputValue.trim() || searchResults.length > 0) && (
              <motion.div
                ref={dropdownRef}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-auto"
              >
                {isSearching ? (
                  <div className="p-4 text-center text-gray-700">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                      className="inline-block"
                    >
                      ⏳
                    </motion.div>
                    <span className="ml-2">Recherche...</span>
                  </div>
                ) : searchResults.length > 0 ? (
                  <ul>
                    {searchResults.map((person, index) => (
                      <li key={person.person_id}>
                        <button
                          type="button"
                          onClick={() => addPerson(person)}
                          className={cn(
                            'w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors',
                            'flex items-center gap-3',
                            index === selectedIndex && 'bg-blue-50'
                          )}
                        >
                          <span className="text-2xl">
                            {personTypeIcons[person.person_type]}
                          </span>
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">
                              {person.display_name}
                            </div>
                            {person.moral_industry && (
                              <div className="text-xs text-gray-700">
                                {person.moral_industry}
                              </div>
                            )}
                            {person.verified && (
                              <span className="text-xs text-green-600">✓ Vérifié</span>
                            )}
                          </div>
                        </button>
                      </li>
                    ))}
                  </ul>
                ) : inputValue.trim() ? (
                  <button
                    type="button"
                    onClick={() => addPerson(null)}
                    className="w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">➕</span>
                      <div>
                        <div className="font-medium text-gray-900">
                          Créer &quot;{inputValue}&quot;
                        </div>
                        <div className="text-xs text-gray-700">
                          Nouvelle {personTypeLabels[selectedType].toLowerCase()}
                        </div>
                      </div>
                    </div>
                  </button>
                ) : null}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}

      {/* Aide */}
      {value.length === 0 && (
        <p className="text-xs text-gray-700">
          Mentionnez des personnes, organisations ou groupes concernés par votre expression
        </p>
      )}
    </div>
  );
}