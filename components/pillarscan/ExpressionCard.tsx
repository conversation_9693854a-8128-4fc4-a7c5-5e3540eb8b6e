/**
 * Composant ExpressionCard amélioré
 * Affiche une expression avec son contexte et actions
 */

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { PillarScanAvatar } from '@/components/ui/Avatar';
import { Badge, MoodBadge } from '@/components/ui/Badge';
import { PillarScanExpression, PILLARS } from '@/lib/types/pillarscan';
import { formatRelativeTime, cn } from '@/lib/utils';

interface ExpressionCardProps {
  expression: PillarScanExpression;
  onRelate?: () => void;
  showActions?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
}

const MOOD_CONFIG = {
  frustrated: {
    emoji: '😤',
    label: 'Frustré',
    gradient: 'from-red-50 to-red-100/50',
    borderColor: 'border-red-200',
    badgeColor: 'bg-red-100 text-red-700',
  },
  happy: {
    emoji: '😊',
    label: 'Heureux',
    gradient: 'from-green-50 to-green-100/50',
    borderColor: 'border-green-200',
    badgeColor: 'bg-green-100 text-green-700',
  },
  idea: {
    emoji: '💡',
    label: 'Idée',
    gradient: 'from-yellow-50 to-amber-100/50',
    borderColor: 'border-yellow-200',
    badgeColor: 'bg-yellow-100 text-yellow-700',
  },
  question: {
    emoji: '❓',
    label: 'Question',
    gradient: 'from-blue-50 to-blue-100/50',
    borderColor: 'border-blue-200',
    badgeColor: 'bg-blue-100 text-blue-700',
  },
};

export function ExpressionCard({ 
  expression, 
  onRelate,
  showActions = true,
  variant = 'default' 
}: ExpressionCardProps) {
  
  const moodConfig = MOOD_CONFIG[expression.mood];
  const [isRelating, setIsRelating] = React.useState(false);

  const handleRelate = async () => {
    if (onRelate && !isRelating) {
      setIsRelating(true);
      onRelate();
      setIsRelating(false);
    }
  };

  const timeAgo = formatRelativeTime(expression.created_at);
  
  // Trouve le pilier suggéré
  const suggestedPillar = expression.suggested_pillar 
    ? PILLARS.find(p => p.id === expression.suggested_pillar)
    : null;

  if (variant === 'compact') {
    return (
      <Card 
        variant="gradient" 
        padding="sm"
        className={cn(
          'bg-gradient-to-br',
          moodConfig.gradient,
          moodConfig.borderColor,
          'border-2'
        )}
      >
        <div className="flex items-start gap-3">
          <span className="text-2xl">{moodConfig.emoji}</span>
          <div className="flex-1 min-w-0">
            <p className="text-gray-800 text-sm line-clamp-2">{expression.text}</p>
            <div className="flex items-center gap-2 mt-2">
              <Button
                size="xs"
                variant={expression.user_has_related ? 'primary' : 'ghost'}
                onClick={handleRelate}
                className="h-6"
              >
                <span className="text-sm">💙</span>
                <span className="text-xs">{expression.relate_count || 0}</span>
              </Button>
              <span className="text-xs text-gray-700">{timeAgo}</span>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      layout
    >
      <Card 
        variant="default" 
        className={cn(
          'overflow-hidden transition-all duration-300',
          'hover:shadow-lg'
        )}
      >
        {/* Bande de mood en haut */}
        <div className={cn(
          'h-2 bg-gradient-to-r',
          expression.mood === 'frustrated' && 'from-red-400 to-red-600',
          expression.mood === 'happy' && 'from-green-400 to-green-600',
          expression.mood === 'idea' && 'from-yellow-400 to-amber-600',
          expression.mood === 'question' && 'from-blue-400 to-blue-600'
        )} />
        
        <CardContent className="p-5">
          {/* Header avec avatar et infos */}
          <div className="flex items-start gap-4 mb-4">
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <PillarScanAvatar
                avatarStyle={expression.user_avatar}
                nickname={expression.user_nickname || 'Anonyme'}
                size="md"
              />
            </motion.div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 flex-wrap">
                <h3 className="font-semibold text-gray-900">
                  {expression.user_nickname || 'Anonyme'}
                </h3>
                <MoodBadge mood={expression.mood} />
              </div>
              
              <div className="flex items-center gap-3 mt-1 text-sm text-gray-700">
                <span>{timeAgo}</span>
                {expression.location_display_name && (
                  <>
                    <span>•</span>
                    <span className="flex items-center gap-1">
                      <span className="text-xs">📍</span>
                      {expression.location_display_name}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Contenu de l'expression */}
          <motion.p 
            className="text-gray-800 text-lg leading-relaxed mb-4 whitespace-pre-wrap break-words"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            {expression.text}
          </motion.p>

          {/* Images attachées avec URLs MinIO */}
          {expression.media_urls && (
            <div className="mb-4">
              {(() => {
                // Adapter pour gérer les deux formats : objet ou tableau
                let mediaItems: Array<{ id: string; url: string; role?: string }> = [];
                
                if (Array.isArray(expression.media_urls)) {
                  // Format actuel du backend : tableau
                  mediaItems = expression.media_urls.slice(0, 3).map((media, index) => ({
                    ...media,
                    role: `image_${index}`
                  }));
                } else if (typeof expression.media_urls === 'object') {
                  // Format attendu : objet
                  const imageEntries = Object.entries(expression.media_urls)
                    .filter(([role]) => role.startsWith('image_'))
                    .slice(0, 3);
                  mediaItems = imageEntries.map(([role, media]) => ({
                    ...media,
                    role
                  }));
                }
                
                if (mediaItems.length === 0) return null;
                
                return (
                  <>
                    <div className={cn(
                      "grid gap-2",
                      mediaItems.length === 1 ? "grid-cols-1" : "grid-cols-2",
                      mediaItems.length > 2 && "lg:grid-cols-3"
                    )}>
                      {mediaItems.map((media, index) => (
                        <motion.div
                          key={media.id || index}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.1 * (index + 1) }}
                          className="relative aspect-square rounded-lg overflow-hidden bg-gray-100"
                        >
                          <Image
                            src={media.url}
                            alt={`Image ${media.role || index}`}
                            fill
                            className="object-cover hover:scale-105 transition-transform duration-300"
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            unoptimized // Les URLs MinIO sont déjà optimisées
                          />
                        </motion.div>
                      ))}
                    </div>
                    {(Array.isArray(expression.media_urls) ? expression.media_urls.length : Object.keys(expression.media_urls).filter(role => role.startsWith('image_')).length) > 3 && (
                      <p className="text-sm text-gray-700 mt-2">
                        +{(Array.isArray(expression.media_urls) ? expression.media_urls.length : Object.keys(expression.media_urls).filter(role => role.startsWith('image_')).length) - 3} autre{((Array.isArray(expression.media_urls) ? expression.media_urls.length : Object.keys(expression.media_urls).filter(role => role.startsWith('image_')).length) - 3) > 1 ? 's' : ''} image{((Array.isArray(expression.media_urls) ? expression.media_urls.length : Object.keys(expression.media_urls).filter(role => role.startsWith('image_')).length) - 3) > 1 ? 's' : ''}
                      </p>
                    )}
                  </>
                );
              })()}
            </div>
          )}

          {/* Pilier suggéré */}
          {suggestedPillar && (
            <div className="mb-4">
              <Badge variant="secondary" size="sm" icon={<span>{suggestedPillar.emoji}</span>}>
                {suggestedPillar.name_fr}
              </Badge>
            </div>
          )}

          {/* Actions */}
          {showActions && (
            <div className="flex items-center justify-between pt-4 border-t border-gray-100">
              <div className="flex items-center gap-3">
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    size="sm"
                    variant={expression.user_has_related ? 'primary' : 'secondary'}
                    onClick={handleRelate}
                    loading={isRelating}
                    leftIcon={
                      <motion.span
                        animate={expression.user_has_related ? {
                          scale: [1, 1.2, 1],
                        } : {}}
                        transition={{ duration: 0.3 }}
                      >
                        💙
                      </motion.span>
                    }
                    className={cn(
                      expression.user_has_related && 'shadow-md'
                    )}
                  >
                    <AnimatePresence mode="wait">
                      <motion.span
                        key={expression.relate_count}
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        transition={{ duration: 0.2 }}
                      >
                        {expression.relate_count || 0}
                      </motion.span>
                    </AnimatePresence>
                    <span className="ml-1 hidden sm:inline">
                      {expression.user_has_related ? 'Soutenu' : 'Soutenir'}
                    </span>
                  </Button>
                </motion.div>

                <Button
                  size="sm"
                  variant="ghost"
                  leftIcon={<span>💬</span>}
                  className="text-gray-800"
                >
                  Commenter
                </Button>

                <Button
                  size="sm"
                  variant="ghost"
                  leftIcon={<span>🔗</span>}
                  className="text-gray-800"
                >
                  Partager
                </Button>
              </div>

              {/* Badges de gamification */}
              {expression.earned_badges && expression.earned_badges.length > 0 && (
                <div className="flex items-center gap-1">
                  {expression.earned_badges.map((badge, index) => (
                    <motion.span
                      key={badge}
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                      className="text-sm"
                      title={badge}
                    >
                      🏆
                    </motion.span>
                  ))}
                </div>
              )}

              {/* Visibilité */}
              <span className="text-xs text-gray-700 capitalize">
                {expression.visibility_level === 'public' && '🌍 Public'}
                {expression.visibility_level === 'anonymous' && '👤 Anonyme'}
                {expression.visibility_level === 'private' && '🔒 Privé'}
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}