'use client';

import React, { useState, useCallback } from 'react';
import { Upload, X, AlertCircle } from 'lucide-react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

interface ImageUploadProps {
  onUpload: (file: File) => Promise<{ id: string; url: string }>;
  onRemove?: (id: string) => void;
  maxFiles?: number;
  maxSizeInMB?: number;
  acceptedTypes?: string[];
  className?: string;
  disabled?: boolean;
}

interface UploadedImage {
  id: string;
  url: string;
  file: File;
}

export function ImageUpload({
  onUpload,
  onRemove,
  maxFiles = 5,
  maxSizeInMB = 10,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  className,
  disabled = false,
}: ImageUploadProps) {
  const [images, setImages] = useState<UploadedImage[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const validateFile = useCallback((file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `Type de fichier non supporté. Types acceptés: ${acceptedTypes.join(', ')}`;
    }
    
    const sizeInMB = file.size / 1024 / 1024;
    if (sizeInMB > maxSizeInMB) {
      return `La taille du fichier dépasse ${maxSizeInMB}MB`;
    }
    
    return null;
  }, [acceptedTypes, maxSizeInMB]);

  const handleFiles = useCallback(async (files: FileList) => {
    if (disabled || uploading) return;
    
    const filesToUpload = Array.from(files).slice(0, maxFiles - images.length);
    
    if (filesToUpload.length === 0) {
      setError(`Limite de ${maxFiles} images atteinte`);
      return;
    }
    
    setError(null);
    setUploading(true);
    
    try {
      for (const file of filesToUpload) {
        const validationError = validateFile(file);
        if (validationError) {
          setError(validationError);
          continue;
        }
        
        const result = await onUpload(file);
        setImages(prev => [...prev, { ...result, file }]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de l\'upload');
    } finally {
      setUploading(false);
    }
  }, [disabled, uploading, maxFiles, images.length, onUpload, validateFile]);

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  };

  const handleRemove = (image: UploadedImage) => {
    setImages(prev => prev.filter(img => img.id !== image.id));
    onRemove?.(image.id);
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Zone de drop */}
      <div
        className={cn(
          'relative border-2 border-dashed rounded-lg p-6 transition-colors',
          dragActive ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' : 'border-gray-300 dark:border-gray-700',
          disabled ? 'opacity-90 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400 dark:hover:border-gray-600',
          error && 'border-red-500'
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={() => !disabled && document.getElementById('file-input')?.click()}
      >
        <input
          id="file-input"
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileInput}
          className="hidden"
          disabled={disabled || uploading}
        />
        
        <div className="flex flex-col items-center justify-center space-y-2">
          <Upload className={cn(
            'w-12 h-12',
            dragActive ? 'text-blue-500' : 'text-gray-700 dark:text-gray-200'
          )} />
          <p className="text-sm text-gray-800 dark:text-gray-300 text-center">
            {uploading ? 'Upload en cours...' : 'Glissez vos images ici ou cliquez pour sélectionner'}
          </p>
          <p className="text-xs text-gray-700 dark:text-gray-300">
            Max {maxFiles} images • {maxSizeInMB}MB par image
          </p>
        </div>
      </div>

      {/* Erreur */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg">
          <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
          <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Images uploadées */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              <div className="aspect-square relative rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
                <Image
                  src={image.url}
                  alt="Image uploadée"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                />
              </div>
              {!disabled && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemove(image);
                  }}
                  className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label="Supprimer l'image"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}