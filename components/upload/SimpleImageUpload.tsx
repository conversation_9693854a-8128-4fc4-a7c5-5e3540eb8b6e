'use client';

import React, { useRef, useState } from 'react';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';
import { motion, AnimatePresence } from 'framer-motion';

interface SimpleImageUploadProps {
  files: File[];
  onFilesChange: (files: File[]) => void;
  maxFiles?: number;
  maxSize?: number; // in MB
}

export const SimpleImageUpload: React.FC<SimpleImageUploadProps> = ({
  files,
  onFilesChange,
  maxFiles = 4,
  maxSize = 5
}) => {
  const { theme } = useTheme();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const handleFileSelect = (selectedFiles: FileList | null) => {
    if (!selectedFiles) return;

    const newFiles = Array.from(selectedFiles);
    const validFiles: File[] = [];
    const newErrors: string[] = [];

    // Validation
    newFiles.forEach(file => {
      // Check file type
      if (!file.type.startsWith('image/')) {
        newErrors.push(`${file.name} n'est pas une image`);
        return;
      }

      // Check file size
      if (file.size > maxSize * 1024 * 1024) {
        newErrors.push(`${file.name} dépasse ${maxSize}MB`);
        return;
      }

      validFiles.push(file);
    });

    // Check max files
    if (files.length + validFiles.length > maxFiles) {
      newErrors.push(`Maximum ${maxFiles} images autorisées`);
      validFiles.splice(maxFiles - files.length);
    }

    setErrors(newErrors);
    if (validFiles.length > 0) {
      onFilesChange([...files, ...validFiles]);
    }

    // Clear errors after 3 seconds
    if (newErrors.length > 0) {
      setTimeout(() => setErrors([]), 3000);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    onFilesChange(newFiles);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-4">
      {/* Upload Zone */}
      <div
        onClick={openFileDialog}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
          transition-all duration-200
          ${isDragging 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
            : `${theme.ui.border} hover:border-gray-400 dark:hover:border-gray-600`
          }
        `}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />

        <Upload className={`w-12 h-12 mx-auto ${theme.text.secondary} mb-4`} />
        <p className={`${theme.text.primary} font-medium mb-2`}>
          Glissez-déposez vos images ici
        </p>
        <p className={`text-sm ${theme.text.secondary}`}>
          ou cliquez pour sélectionner des fichiers
        </p>
        <p className={`text-xs ${theme.text.muted} mt-2`}>
          Max {maxFiles} images • {maxSize}MB par fichier
        </p>
      </div>

      {/* Error Messages */}
      <AnimatePresence>
        {errors.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg"
          >
            {errors.map((error, index) => (
              <p key={index} className="text-sm text-red-600 dark:text-red-400">
                {error}
              </p>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Image Previews */}
      {files.length > 0 && (
        <div className="grid grid-cols-2 gap-4">
          {files.map((file, index) => {
            const imageUrl = URL.createObjectURL(file);
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="relative group"
              >
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  src={imageUrl}
                  alt={`Preview ${index + 1}`}
                  className="w-full h-32 object-cover rounded-lg"
                  onLoad={() => URL.revokeObjectURL(imageUrl)}
                />
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile(index);
                    }}
                    className="p-2 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
                <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  {(file.size / 1024 / 1024).toFixed(1)}MB
                </div>
              </motion.div>
            );
          })}
          
          {/* Add more button */}
          {files.length < maxFiles && (
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              onClick={openFileDialog}
              className={`
                h-32 border-2 border-dashed ${theme.ui.border} rounded-lg
                flex flex-col items-center justify-center gap-2
                hover:border-gray-400 dark:hover:border-gray-600 transition-colors
              `}
            >
              <ImageIcon className={`w-8 h-8 ${theme.text.secondary}`} />
              <span className={`text-sm ${theme.text.secondary}`}>
                Ajouter
              </span>
            </motion.button>
          )}
        </div>
      )}
    </div>
  );
};