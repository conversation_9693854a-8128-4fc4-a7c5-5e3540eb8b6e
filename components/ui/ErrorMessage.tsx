import React from 'react';
import { Alert<PERSON>ircle, WifiOff, RefreshCw } from 'lucide-react';
import { Button } from './Button';
import { motion } from 'framer-motion';

interface ErrorMessageProps {
  error: string;
  onRetry?: () => void;
  className?: string;
}

export function ErrorMessage({ error, onRetry, className = '' }: ErrorMessageProps) {
  // Déterminer le type d'erreur pour afficher l'icône appropriée
  const isNetworkError = error.toLowerCase().includes('connexion') || 
                       error.toLowerCase().includes('serveur');
  
  const Icon = isNetworkError ? WifiOff : AlertCircle;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex flex-col items-center justify-center p-8 text-center ${className}`}
    >
      <Icon className="w-16 h-16 text-gray-400 mb-4" />
      
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {isNetworkError ? 'Problème de connexion' : 'Une erreur est survenue'}
      </h3>
      
      <p className="text-gray-600 dark:text-gray-400 max-w-md mb-6">
        {error}
      </p>
      
      {onRetry && (
        <Button
          onClick={onRetry}
          variant="primary"
          size="sm"
          className="flex items-center gap-2"
        >
          <RefreshCw className="w-4 h-4" />
          Réessayer
        </Button>
      )}
    </motion.div>
  );
}

// Composant pour les erreurs inline (plus compact)
export function InlineError({ error, className = '' }: { error: string; className?: string }) {
  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      className={`flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg ${className}`}
    >
      <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0" />
      <p className="text-sm text-red-700 dark:text-red-300">
        {error}
      </p>
    </motion.div>
  );
}