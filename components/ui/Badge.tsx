import React from 'react';
import { cn } from '@/lib/utils';

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
}

export const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className, variant = 'default', size = 'md', icon, children, ...props }, ref) => {
    const variants = {
      default: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
      secondary: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      success: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      info: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200',
    };

    const sizes = {
      sm: 'text-xs px-2 py-0.5',
      md: 'text-sm px-2.5 py-1',
      lg: 'text-base px-3 py-1.5',
    };

    return (
      <span
        ref={ref}
        className={cn(
          'inline-flex items-center gap-1 rounded-full font-medium',
          variants[variant],
          sizes[size],
          className
        )}
        {...props}
      >
        {icon && <span className="flex-shrink-0">{icon}</span>}
        {children}
      </span>
    );
  }
);

Badge.displayName = 'Badge';

// Badge spécialisé pour les achievements PillarScan
export interface AchievementBadgeProps {
  badge: {
    id: string;
    name: string;
    description: string;
    emoji: string;
    xp_reward: number;
  };
  size?: 'sm' | 'md' | 'lg';
  showDescription?: boolean;
}

export const AchievementBadge: React.FC<AchievementBadgeProps> = ({
  badge,
  size = 'md',
  showDescription = false,
}) => {
  return (
    <div className="inline-flex items-center">
      <Badge
        variant="warning"
        size={size}
        icon={<span className="text-lg">{badge.emoji}</span>}
      >
        <span>{badge.name}</span>
        {size !== 'sm' && (
          <span className="opacity-75">+{badge.xp_reward} XP</span>
        )}
      </Badge>
      {showDescription && (
        <span className="ml-2 text-sm text-gray-800 dark:text-gray-300">
          {badge.description}
        </span>
      )}
    </div>
  );
};

// Badge pour les moods
export interface MoodBadgeProps {
  mood: 'frustrated' | 'happy' | 'idea' | 'question';
  size?: 'sm' | 'md' | 'lg';
}

const MOOD_CONFIG = {
  frustrated: {
    emoji: '😤',
    variant: 'error' as const,
    label: 'Frustré',
  },
  happy: {
    emoji: '😊',
    variant: 'success' as const,
    label: 'Heureux',
  },
  idea: {
    emoji: '💡',
    variant: 'warning' as const,
    label: 'Idée',
  },
  question: {
    emoji: '❓',
    variant: 'info' as const,
    label: 'Question',
  },
};

export const MoodBadge: React.FC<MoodBadgeProps> = ({ mood, size = 'md' }) => {
  const config = MOOD_CONFIG[mood];
  
  return (
    <Badge
      variant={config.variant}
      size={size}
      icon={<span>{config.emoji}</span>}
    >
      {config.label}
    </Badge>
  );
};