'use client';

import { forwardRef, HTMLAttributes } from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { cva, type VariantProps } from 'class-variance-authority';
import { useTheme } from '@/contexts/ThemeContext';

const cardVariants = cva(
  'rounded-xl transition-all duration-300',
  {
    variants: {
      variant: {
        default: '',
        bordered: '', // Pour la rétrocompatibilité
        shadow: '', // Pour la rétrocompatibilité
        elevated: '',
        filled: '',
        gradient: '',
        glass: 'backdrop-blur-md',
      },
      padding: {
        none: '',
        sm: 'p-4',
        md: 'p-6',
        lg: 'p-8',
      },
      interactive: {
        true: 'cursor-pointer hover:scale-[1.02] active:scale-[0.98]',
      },
    },
    defaultVariants: {
      variant: 'default',
      padding: 'md',
    },
  }
);

interface CardProps
  extends HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  hoverable?: boolean;
  onClick?: () => void;
  animated?: boolean;
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, padding, interactive, hoverable, animated = true, children, onClick, ...props }, ref) => {
    const isInteractive = interactive || hoverable || !!onClick;
    const { theme } = useTheme();
    
    // Extraire les props qui peuvent causer des conflits avec Framer Motion
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
    const { onDrag, onDragStart, onDragEnd, ...safeProps } = props as any;
    
    if (animated) {
      // Déterminer les classes basées sur le thème et la variante
      const themeClasses = [
        theme.ui.card,
        theme.ui.border,
        variant === 'elevated' || variant === 'shadow' ? theme.ui.shadow : 'shadow-sm',
        variant === 'filled' ? (theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-gray-800' : 'bg-gray-50') : '',
        variant === 'gradient' ? `bg-gradient-to-br ${theme.background.accent}` : '',
        variant === 'glass' ? 'bg-opacity-80' : '',
        isInteractive ? theme.ui.cardHover : ''
      ].filter(Boolean).join(' ');
      
      return (
        <motion.div
          ref={ref}
          className={cn(cardVariants({ variant, padding, interactive: isInteractive }), themeClasses, className)}
          onClick={onClick}
          whileHover={isInteractive ? { y: -2 } : undefined}
          transition={{ duration: 0.2 }}
          {...safeProps}
        >
          {children}
        </motion.div>
      );
    }
    
    // Déterminer les classes basées sur le thème et la variante
    const themeClasses = [
      theme.ui.card,
      theme.ui.border,
      variant === 'elevated' || variant === 'shadow' ? theme.ui.shadow : 'shadow-sm',
      variant === 'filled' ? (theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-gray-800' : 'bg-gray-50') : '',
      variant === 'gradient' ? `bg-gradient-to-br ${theme.background.accent}` : '',
      variant === 'glass' ? 'bg-opacity-80' : '',
      isInteractive ? theme.ui.cardHover : ''
    ].filter(Boolean).join(' ');
    
    return (
      <div
        ref={ref}
        className={cn(cardVariants({ variant, padding, interactive: isInteractive }), themeClasses, className)}
        onClick={onClick}
        {...safeProps}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

const CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('mb-4 space-y-1.5', className)}
      {...props}
    />
  )
);

CardHeader.displayName = 'CardHeader';

interface CardTitleProps extends HTMLAttributes<HTMLHeadingElement> {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

const CardTitle = forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ className, as: Component = 'h3', ...props }, ref) => {
    const Comp = Component;
    return (
      <Comp
        ref={ref}
        className={cn('text-lg font-semibold leading-none tracking-tight', className)}
        {...props}
      />
    );
  }
);

CardTitle.displayName = 'CardTitle';

const CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => (
    <p
      ref={ref}
      className={cn('text-sm text-gray-800', className)}
      {...props}
    />
  )
);

CardDescription.displayName = 'CardDescription';

const CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('', className)} {...props} />
  )
);

CardContent.displayName = 'CardContent';

const CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('mt-4 flex items-center pt-4', className)}
      {...props}
    />
  )
);

CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };