'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from './Button';
import { Globe } from 'lucide-react';

interface Country {
  code: string;
  name: string;
  flag: string;
}

// Liste des pays supportés
const SUPPORTED_COUNTRIES: Country[] = [
  { code: 'FR', name: 'France', flag: '🇫🇷' },
  { code: 'US', name: 'États-Unis', flag: '🇺🇸' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦' },
  { code: 'GB', name: 'Royaume-Uni', flag: '🇬🇧' },
  { code: 'DE', name: '<PERSON>emagne', flag: '🇩🇪' },
  { code: 'ES', name: '<PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
  { code: 'IT', name: 'It<PERSON><PERSON>', flag: '🇮🇹' },
  { code: 'BR', name: 'B<PERSON>sil', flag: '🇧🇷' },
  { code: 'JP', name: 'Japon', flag: '🇯🇵' },
  { code: 'C<PERSON>', name: '<PERSON><PERSON>', flag: '🇨🇳' },
  { code: 'IN', name: 'Inde', flag: '🇮🇳' },
  { code: 'MX', name: 'Mexique', flag: '🇲🇽' },
  { code: 'AU', name: 'Australie', flag: '🇦🇺' },
  { code: 'ZA', name: 'Afrique du Sud', flag: '🇿🇦' },
  { code: 'NG', name: 'Nigeria', flag: '🇳🇬' },
  { code: 'EG', name: 'Égypte', flag: '🇪🇬' },
  { code: 'MA', name: 'Maroc', flag: '🇲🇦' },
  { code: 'TN', name: 'Tunisie', flag: '🇹🇳' },
  { code: 'SN', name: 'Sénégal', flag: '🇸🇳' },
  { code: 'CI', name: 'Côte d\'Ivoire', flag: '🇨🇮' },
];

interface CountrySelectorProps {
  onCountrySelect: (countryCode: string) => void;
  currentCountry?: string | null;
}

export function CountrySelector({ onCountrySelect, currentCountry }: CountrySelectorProps) {
  const [selectedCountry, setSelectedCountry] = useState<string | null>(currentCountry || null);
  const [showSelector, setShowSelector] = useState(false);

  useEffect(() => {
    // Vérifier si un pays est déjà sauvegardé
    const savedCountry = localStorage.getItem('selectedCountry');
    if (savedCountry && !selectedCountry) {
      setSelectedCountry(savedCountry);
      onCountrySelect(savedCountry);
    }
  }, [selectedCountry, onCountrySelect]);

  const handleCountrySelect = (countryCode: string) => {
    setSelectedCountry(countryCode);
    localStorage.setItem('selectedCountry', countryCode);
    onCountrySelect(countryCode);
    setShowSelector(false);
  };

  const currentCountryData = SUPPORTED_COUNTRIES.find(c => c.code === selectedCountry);

  return (
    <>
      {/* Bouton pour afficher le sélecteur */}
      <button
        onClick={() => setShowSelector(true)}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
      >
        {currentCountryData ? (
          <>
            <span className="text-lg">{currentCountryData.flag}</span>
            <span>{currentCountryData.name}</span>
          </>
        ) : (
          <>
            <Globe className="w-4 h-4" />
            <span>Sélectionner un pays</span>
          </>
        )}
      </button>

      {/* Modal de sélection */}
      {showSelector && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4"
          onClick={() => setShowSelector(false)}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900">Sélectionnez votre pays</h2>
              <p className="mt-2 text-gray-800">
                Choisissez le pays pour voir les expressions locales
              </p>
            </div>

            <div className="p-6 overflow-y-auto max-h-[60vh]">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {SUPPORTED_COUNTRIES.map((country) => (
                  <motion.button
                    key={country.code}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleCountrySelect(country.code)}
                    className={`flex items-center gap-3 p-4 rounded-lg border transition-all ${
                      selectedCountry === country.code
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <span className="text-2xl">{country.flag}</span>
                    <span className="font-medium">{country.name}</span>
                  </motion.button>
                ))}
              </div>
            </div>

            <div className="p-6 border-t border-gray-200">
              <Button
                variant="secondary"
                onClick={() => setShowSelector(false)}
                className="w-full sm:w-auto"
              >
                Fermer
              </Button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </>
  );
}

// Hook pour gérer le pays sélectionné
export function useCountrySelection() {
  const [country, setCountry] = useState<string | null>(null);

  useEffect(() => {
    const savedCountry = localStorage.getItem('selectedCountry');
    if (savedCountry) {
      setCountry(savedCountry);
    }
  }, []);

  const selectCountry = (countryCode: string) => {
    setCountry(countryCode);
    localStorage.setItem('selectedCountry', countryCode);
  };

  return { country, selectCountry };
}