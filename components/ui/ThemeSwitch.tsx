'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme, ThemeMode } from '@/contexts/ThemeContext';
import { cn } from '@/lib/utils';

const themeIcons: Record<ThemeMode, { icon: string; label: string }> = {
  light: { icon: '☀️', label: 'Clair' },
  dark: { icon: '🌙', label: 'Sombre' },
  ocean: { icon: '🌊', label: 'Océan' },
  sunset: { icon: '🌅', label: 'Coucher' },
  aurora: { icon: '🌌', label: 'Aurore' },
};

interface ThemeSwitchProps {
  variant?: 'compact' | 'full';
  className?: string;
}

export function ThemeSwitch({ variant = 'compact', className }: ThemeSwitchProps) {
  const { themeMode, setThemeMode, theme } = useTheme();
  const [isOpen, setIsOpen] = React.useState(false);
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Éviter les problèmes d'hydratation
  if (!mounted) {
    return (
      <div className={cn(
        variant === 'compact' ? "w-12 h-12 rounded-xl" : "grid grid-cols-5 gap-2",
        className
      )} />
    );
  }

  if (variant === 'compact') {
    return (
      <div className="relative">
        <motion.button
          onClick={() => setIsOpen(!isOpen)}
          className={cn(
            'relative w-12 h-12 rounded-xl flex items-center justify-center transition-all',
            'bg-gradient-to-br shadow-lg',
            theme.mode === 'dark' ? 'from-slate-700 to-slate-800' : 'from-white to-gray-100',
            'hover:scale-105 active:scale-95',
            className
          )}
          whileHover={{ rotate: 15 }}
          whileTap={{ rotate: -15 }}
        >
          <motion.span
            key={themeMode}
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            exit={{ scale: 0, rotate: 180 }}
            className="text-2xl"
          >
            {themeIcons[themeMode].icon}
          </motion.span>
        </motion.button>

        <AnimatePresence>
          {isOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-40"
                onClick={() => setIsOpen(false)}
              />

              {/* Menu */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9, y: -10 }}
                transition={{ type: 'spring', bounce: 0.3 }}
                className={cn(
                  'absolute right-0 mt-2 p-2 rounded-2xl shadow-2xl z-50',
                  'backdrop-blur-xl border',
                  theme.ui.card,
                  theme.ui.border
                )}
              >
                <div className="flex flex-col space-y-1">
                  {Object.entries(themeIcons).map(([mode, { icon, label }]) => (
                    <motion.button
                      key={mode}
                      onClick={() => {
                        setThemeMode(mode as ThemeMode);
                        setIsOpen(false);
                      }}
                      className={cn(
                        'flex items-center space-x-3 px-4 py-2 rounded-xl transition-all',
                        'hover:scale-105 active:scale-95',
                        themeMode === mode
                          ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                          : cn(theme.ui.cardHover, theme.text.secondary)
                      )}
                      whileHover={{ x: 5 }}
                      whileTap={{ x: 0 }}
                    >
                      <span className="text-xl">{icon}</span>
                      <span className="text-sm font-medium">{label}</span>
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    );
  }

  // Variant "full" pour les paramètres
  return (
    <div className={cn('space-y-4', className)}>
      <h3 className={cn('text-lg font-semibold', theme.text.primary)}>
        Apparence
      </h3>
      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
        {Object.entries(themeIcons).map(([mode, { icon, label }]) => (
          <motion.button
            key={mode}
            onClick={() => setThemeMode(mode as ThemeMode)}
            className={cn(
              'relative p-4 rounded-2xl transition-all group',
              'border-2',
              themeMode === mode
                ? 'border-blue-500 bg-gradient-to-br from-blue-500/10 to-purple-500/10'
                : cn('border-transparent', theme.ui.card, theme.ui.border)
            )}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="flex flex-col items-center space-y-2">
              <motion.div
                className="text-3xl"
                animate={themeMode === mode ? { rotate: [0, 15, -15, 0] } : {}}
                transition={{ duration: 0.5 }}
              >
                {icon}
              </motion.div>
              <span className={cn('text-sm font-medium', theme.text.secondary)}>
                {label}
              </span>
            </div>
            {themeMode === mode && (
              <motion.div
                layoutId="theme-indicator"
                className="absolute inset-0 rounded-2xl border-2 border-blue-500"
                transition={{ type: 'spring', bounce: 0.2 }}
              />
            )}
          </motion.button>
        ))}
      </div>
    </div>
  );
}