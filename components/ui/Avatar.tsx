import React from 'react';
import { cn, stringToColor } from '@/lib/utils';

export interface AvatarProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  src?: string;
  alt?: string;
  fallback?: string;
  emoji?: string;
  color?: string;
}

const sizeClasses = {
  xs: 'w-6 h-6 text-xs',
  sm: 'w-8 h-8 text-sm',
  md: 'w-10 h-10 text-base',
  lg: 'w-12 h-12 text-lg',
  xl: 'w-16 h-16 text-2xl',
};

export const Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(
  ({ className, size = 'md', src, alt, fallback, emoji, color, style, ...props }, ref) => {
    const [imageError, setImageError] = React.useState(false);

    // Générer une couleur basée sur le fallback si pas de couleur fournie
    const backgroundColor = color || (fallback ? stringToColor(fallback) : '#E5E7EB');

    const renderContent = () => {
      // Si emoji est fourni, l'afficher en priorité
      if (emoji) {
        return <span className="select-none">{emoji}</span>;
      }

      // Si image et pas d'erreur, afficher l'image
      if (src && !imageError) {
        return (
          // eslint-disable-next-line @next/next/no-img-element
          <img
            src={src}
            alt={alt || fallback || 'Avatar'}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        );
      }

      // Sinon, afficher le fallback ou les initiales
      if (fallback) {
        const words = fallback.trim().split(' ').filter(word => word.length > 0);
        let initials = '';
        
        if (words.length === 1) {
          // Pour un seul mot, prendre les 2 premiers caractères
          initials = words[0].slice(0, 2).toUpperCase();
        } else {
          // Pour plusieurs mots, prendre la première lettre de chaque mot (max 2)
          initials = words
            .map(word => word[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
        }
        
        return <span className="font-medium select-none">{initials}</span>;
      }

      // Par défaut, icône utilisateur
      return (
        <svg
          className="w-3/4 h-3/4 text-gray-700"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
        </svg>
      );
    };

    return (
      <div
        ref={ref}
        className={cn(
          'relative inline-flex items-center justify-center rounded-full overflow-hidden',
          'bg-gray-100 dark:bg-gray-700',
          sizeClasses[size],
          className
        )}
        style={{
          backgroundColor: !src || imageError ? backgroundColor : undefined,
          ...style,
        }}
        {...props}
      >
        {renderContent()}
      </div>
    );
  }
);

Avatar.displayName = 'Avatar';

// Groupe d'avatars avec chevauchement
export interface AvatarGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  max?: number;
  size?: AvatarProps['size'];
  spacing?: 'tight' | 'normal' | 'loose';
}

export const AvatarGroup = React.forwardRef<HTMLDivElement, AvatarGroupProps>(
  ({ className, children, max = 3, size = 'sm', spacing = 'tight', ...props }, ref) => {
    const childrenArray = React.Children.toArray(children);
    const displayedChildren = max ? childrenArray.slice(0, max) : childrenArray;
    const remainingCount = childrenArray.length - displayedChildren.length;

    const spacingClasses = {
      tight: '-space-x-2',
      normal: '-space-x-1',
      loose: 'space-x-1',
    };

    return (
      <div
        ref={ref}
        className={cn('flex items-center', spacingClasses[spacing], className)}
        {...props}
      >
        {displayedChildren.map((child, index) => (
          <div
            key={index}
            className="relative ring-2 ring-white dark:ring-gray-800 rounded-full"
            style={{ zIndex: displayedChildren.length - index }}
          >
            {React.isValidElement(child) && child.type === Avatar
              ? React.cloneElement(child as React.ReactElement<AvatarProps>, { size })
              : child}
          </div>
        ))}
        
        {remainingCount > 0 && (
          <div
            className="relative ring-2 ring-white dark:ring-gray-800 rounded-full"
            style={{ zIndex: 0 }}
          >
            <Avatar
              size={size}
              className="bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200"
              fallback={`+${remainingCount}`}
            />
          </div>
        )}
      </div>
    );
  }
);

AvatarGroup.displayName = 'AvatarGroup';

// Avatar PillarScan avec style personnalisé
export interface PillarScanAvatarProps {
  avatarStyle?: {
    emoji: string;
    color: string;
  };
  nickname?: string;
  size?: AvatarProps['size'];
}

export const PillarScanAvatar: React.FC<PillarScanAvatarProps> = ({
  avatarStyle,
  nickname,
  size = 'md',
}) => {
  if (avatarStyle) {
    return (
      <Avatar
        size={size}
        emoji={avatarStyle.emoji}
        color={avatarStyle.color}
        aria-label={nickname || 'Utilisateur'}
      />
    );
  }

  return (
    <Avatar
      size={size}
      fallback={nickname || 'Anonyme'}
      aria-label={nickname || 'Utilisateur anonyme'}
    />
  );
};