import React from 'react';
import { motion } from 'framer-motion';

interface ImpactScoreProps {
  score: number;
  variant?: 'compact' | 'full';
  showTrend?: boolean;
  previousScore?: number;
}

export function ImpactScore({ 
  score, 
  variant = 'compact',
  showTrend = false,
  previousScore 
}: ImpactScoreProps) {
  
  // Calcule la couleur en fonction du score
  const getScoreColor = (s: number) => {
    if (s >= 80) return 'from-green-400 to-emerald-600';
    if (s >= 60) return 'from-blue-400 to-blue-600';
    if (s >= 40) return 'from-yellow-400 to-orange-500';
    if (s >= 20) return 'from-orange-400 to-red-500';
    return 'from-gray-400 to-gray-600';
  };

  // Calcule le niveau d'impact
  const getImpactLevel = (s: number) => {
    if (s >= 80) return { label: 'Extraordinaire', emoji: '🌟' };
    if (s >= 60) return { label: 'Excellent', emoji: '⭐' };
    if (s >= 40) return { label: 'Bon', emoji: '👍' };
    if (s >= 20) return { label: 'En progression', emoji: '📈' };
    return { label: 'Débutant', emoji: '🌱' };
  };

  const impactLevel = getImpactLevel(score);
  const trend = previousScore ? score - previousScore : 0;

  if (variant === 'compact') {
    return (
      <motion.div
        className="inline-flex items-center gap-2 px-3 py-1.5 bg-white rounded-full shadow-sm border border-gray-200"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <div className="relative">
          <motion.div
            className={`w-8 h-8 rounded-full bg-gradient-to-br ${getScoreColor(score)} flex items-center justify-center`}
            animate={{
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatDelay: 3,
            }}
          >
            <span className="text-white font-bold text-xs">{score}</span>
          </motion.div>
          {showTrend && trend !== 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center text-xs ${
                trend > 0 ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
              }`}
            >
              {trend > 0 ? '↑' : '↓'}
            </motion.div>
          )}
        </div>
        <span className="text-sm font-medium text-gray-700">Impact</span>
      </motion.div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="text-center space-y-4">
        {/* Score principal */}
        <motion.div
          className="relative inline-block"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: 'spring', stiffness: 200, damping: 20 }}
        >
          <motion.div
            className={`w-32 h-32 rounded-full bg-gradient-to-br ${getScoreColor(score)} flex items-center justify-center shadow-lg`}
            animate={{
              rotate: [0, 5, -5, 5, 0],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatDelay: 2,
            }}
          >
            <div className="text-center">
              <motion.div
                className="text-4xl font-bold text-white"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                {score}
              </motion.div>
              <div className="text-xs text-white/80 uppercase tracking-wider">
                Impact
              </div>
            </div>
          </motion.div>
          
          {/* Cercle de progression */}
          <svg className="absolute inset-0 w-32 h-32 -rotate-90">
            <circle
              cx="64"
              cy="64"
              r="60"
              stroke="currentColor"
              strokeWidth="8"
              fill="none"
              className="text-gray-200"
            />
            <motion.circle
              cx="64"
              cy="64"
              r="60"
              stroke="url(#gradient)"
              strokeWidth="8"
              fill="none"
              strokeLinecap="round"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: score / 100 }}
              transition={{ duration: 1, ease: 'easeOut' }}
              style={{
                strokeDasharray: '377',
                strokeDashoffset: '0',
              }}
            />
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#60a5fa" />
                <stop offset="100%" stopColor="#3b82f6" />
              </linearGradient>
            </defs>
          </svg>
        </motion.div>

        {/* Niveau d'impact */}
        <div>
          <motion.div
            className="text-2xl mb-1"
            animate={{
              rotate: [0, -10, 10, -10, 0],
            }}
            transition={{
              duration: 0.5,
              repeat: score >= 80 ? Infinity : 0,
              repeatDelay: 3,
            }}
          >
            {impactLevel.emoji}
          </motion.div>
          <h3 className="text-lg font-semibold text-gray-900">
            {impactLevel.label}
          </h3>
        </div>

        {/* Tendance */}
        {showTrend && trend !== 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${
              trend > 0
                ? 'bg-green-100 text-green-700'
                : 'bg-red-100 text-red-700'
            }`}
          >
            <span>{trend > 0 ? '↑' : '↓'}</span>
            <span>{Math.abs(trend)} points</span>
          </motion.div>
        )}

        {/* Description */}
        <p className="text-sm text-gray-800 max-w-xs mx-auto">
          Votre score d&apos;impact reflète l&apos;engagement de la communauté avec vos expressions
        </p>
      </div>
    </div>
  );
}

// Composant pour afficher l'impact sur une expression
interface ExpressionImpactProps {
  relates: number;
  views?: number;
  shares?: number;
}

export function ExpressionImpact({ relates, views = 0, shares = 0 }: ExpressionImpactProps) {
  // Calcul simple du score d'impact pour une expression
  const impactScore = Math.min(100, relates * 10 + views * 0.1 + shares * 5);
  
  return (
    <div className="flex items-center gap-4 text-sm">
      <motion.div
        className="flex items-center gap-1.5"
        whileHover={{ scale: 1.05 }}
      >
        <span className="text-blue-600">💙</span>
        <span className="font-medium text-gray-700">{relates}</span>
      </motion.div>
      
      {views > 0 && (
        <motion.div
          className="flex items-center gap-1.5"
          whileHover={{ scale: 1.05 }}
        >
          <span className="text-gray-700">👁</span>
          <span className="text-gray-800">{views}</span>
        </motion.div>
      )}
      
      {shares > 0 && (
        <motion.div
          className="flex items-center gap-1.5"
          whileHover={{ scale: 1.05 }}
        >
          <span className="text-green-600">🔄</span>
          <span className="text-gray-800">{shares}</span>
        </motion.div>
      )}
      
      {impactScore > 20 && (
        <motion.div
          className="ml-auto"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3 }}
        >
          <ImpactScore score={Math.round(impactScore)} variant="compact" />
        </motion.div>
      )}
    </div>
  );
}