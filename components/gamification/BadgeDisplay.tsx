import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from '@/lib/types/pillarscan';
import { useBadges } from '@/hooks/useBadges';

interface BadgeItemProps {
  badge: Badge;
  isUnlocked: boolean;
  progress?: number;
  earnedAt?: string;
}

export function BadgeItem({ badge, isUnlocked, progress = 0, earnedAt }: BadgeItemProps) {
  return (
    <motion.div
      className={`relative p-4 rounded-xl border-2 transition-all ${
        isUnlocked
          ? 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-400'
          : 'bg-gray-50 border-gray-200'
      }`}
      whileHover={isUnlocked ? { scale: 1.05 } : {}}
      whileTap={isUnlocked ? { scale: 0.95 } : {}}
    >
      {/* Badge Icon */}
      <div className="text-center mb-2">
        <motion.span
          className={`text-4xl inline-block ${!isUnlocked ? 'opacity-30 grayscale' : ''}`}
          animate={isUnlocked ? {
            rotate: [0, -10, 10, -10, 0],
          } : {}}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {badge.icon || '🏆'}
        </motion.span>
      </div>

      {/* Badge Info */}
      <h3 className={`font-semibold text-sm mb-1 ${
        isUnlocked ? 'text-gray-900' : 'text-gray-700'
      }`}>
        {badge.name}
      </h3>
      <p className={`text-xs ${
        isUnlocked ? 'text-gray-800' : 'text-gray-700'
      }`}>
        {badge.description}
      </p>

      {/* XP Reward */}
      <div className={`mt-2 text-xs font-medium ${
        isUnlocked ? 'text-orange-600' : 'text-gray-700'
      }`}>
        +{badge.xp} XP
      </div>

      {/* Progress Bar (for locked badges) */}
      {!isUnlocked && progress > 0 && (
        <div className="mt-2">
          <div className="h-1 bg-gray-200 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-yellow-400 to-orange-400"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
          <p className="text-xs text-gray-700 mt-1">{Math.round(progress)}%</p>
        </div>
      )}

      {/* Earned Date */}
      {isUnlocked && earnedAt && (
        <p className="text-xs text-gray-700 mt-2">
          Obtenu le {new Date(earnedAt).toLocaleDateString('fr-FR')}
        </p>
      )}

      {/* Lock Overlay */}
      {!isUnlocked && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <span className="text-3xl opacity-20">🔒</span>
        </div>
      )}
    </motion.div>
  );
}

interface LevelDisplayProps {
  level: number;
  totalXP: number;
  levelProgress: number;
  xpForNextLevel: number;
}

export function LevelDisplay({ level, totalXP, levelProgress, xpForNextLevel }: LevelDisplayProps) {
  return (
    <div className="bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-xl p-6 shadow-lg">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-2xl font-bold">Niveau {level}</h3>
          <p className="text-purple-100">{totalXP} XP total</p>
        </div>
        <motion.div
          className="text-5xl"
          animate={{
            rotate: [0, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: 'linear',
          }}
        >
          ⭐
        </motion.div>
      </div>

      {/* Progress to next level */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm text-purple-100">
          <span>Prochain niveau</span>
          <span>{Math.round(levelProgress)}%</span>
        </div>
        <div className="h-3 bg-purple-700 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-yellow-400 to-yellow-300"
            initial={{ width: 0 }}
            animate={{ width: `${levelProgress}%` }}
            transition={{ duration: 0.5, ease: 'easeOut' }}
          />
        </div>
        <p className="text-xs text-purple-200">
          {Math.round((levelProgress / 100) * xpForNextLevel)} / {xpForNextLevel} XP
        </p>
      </div>
    </div>
  );
}

// Notification de nouveau badge
export function BadgeNotification() {
  const { newBadges } = useBadges();

  return (
    <AnimatePresence>
      {newBadges.map((badge, index) => (
        <motion.div
          key={`${badge.id}-${index}`}
          initial={{ opacity: 0, y: -50, scale: 0.8 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50"
          style={{ top: `${80 + index * 100}px` }}
        >
          <div className="bg-white rounded-xl shadow-2xl border-2 border-yellow-400 p-6 min-w-[300px]">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: [0, 1.2, 1] }}
              transition={{ duration: 0.5 }}
              className="text-center mb-3"
            >
              <span className="text-5xl">{badge.icon || '🏆'}</span>
            </motion.div>
            <h3 className="text-xl font-bold text-center text-gray-900 mb-1">
              Nouveau badge débloqué !
            </h3>
            <p className="text-center text-gray-700 font-medium">{badge.name}</p>
            <p className="text-center text-sm text-gray-800 mt-1">{badge.description}</p>
            <div className="text-center mt-3">
              <span className="inline-flex items-center gap-1 px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-sm font-medium">
                <span>+{badge.xp} XP</span>
                <motion.span
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity }}
                >
                  ✨
                </motion.span>
              </span>
            </div>
          </div>
        </motion.div>
      ))}
    </AnimatePresence>
  );
}

// Animation de gain d'XP
interface XPAnimationProps {
  amount: number;
  x: number;
  y: number;
}

export function XPAnimation({ amount, x, y }: XPAnimationProps) {
  return (
    <motion.div
      initial={{ opacity: 1, y: 0 }}
      animate={{ opacity: 0, y: -50 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 1.5 }}
      className="fixed pointer-events-none z-50 font-bold text-2xl text-yellow-500"
      style={{ left: x, top: y }}
    >
      +{amount} XP
    </motion.div>
  );
}