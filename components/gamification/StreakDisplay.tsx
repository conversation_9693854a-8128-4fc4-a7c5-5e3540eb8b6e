import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useStreak, useStreakMilestones } from '@/hooks/useStreak';

interface StreakDisplayProps {
  variant?: 'compact' | 'full';
  showMilestone?: boolean;
}

export function StreakDisplay({ 
  variant = 'compact', 
  showMilestone = true 
}: StreakDisplayProps) {
  const { 
    currentStreak, 
    isStreakActive, 
    daysUntilStreakLoss,
    longestStreak 
  } = useStreak();
  
  const { nextMilestone, progress, isAtMilestone } = useStreakMilestones(currentStreak);

  if (variant === 'compact') {
    return (
      <motion.div
        className={`flex items-center gap-2 px-3 py-1.5 rounded-full ${
          isStreakActive 
            ? 'bg-orange-100 text-orange-700' 
            : 'bg-gray-100 text-gray-800'
        }`}
        animate={{
          scale: isAtMilestone ? [1, 1.1, 1] : 1,
        }}
        transition={{ duration: 0.3 }}
      >
        <motion.span
          className="text-xl"
          animate={{
            rotate: isStreakActive ? [0, -10, 10, -10, 0] : 0,
          }}
          transition={{ 
            duration: 0.5,
            repeat: isStreakActive ? Infinity : 0,
            repeatDelay: 3
          }}
        >
          🔥
        </motion.span>
        <span className="font-semibold">{currentStreak}</span>
        {daysUntilStreakLoss === 0 && currentStreak > 0 && (
          <span className="text-xs">⚠️</span>
        )}
      </motion.div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <motion.div
            className="text-3xl"
            animate={{
              rotate: isStreakActive ? [0, -10, 10, -10, 0] : 0,
              scale: isStreakActive ? [1, 1.1, 1] : 1,
            }}
            transition={{ 
              duration: 0.5,
              repeat: isStreakActive ? Infinity : 0,
              repeatDelay: 3
            }}
          >
            🔥
          </motion.div>
          <div>
            <div className="flex items-baseline gap-1">
              <span className="text-2xl font-bold text-gray-900">
                {currentStreak}
              </span>
              <span className="text-sm text-gray-700">
                jour{currentStreak > 1 ? 's' : ''}
              </span>
            </div>
            <p className="text-xs text-gray-700">
              Record : {longestStreak} jours
            </p>
          </div>
        </div>
        
        {isStreakActive && daysUntilStreakLoss === 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs font-medium"
          >
            Exprimez-vous aujourd&apos;hui !
          </motion.div>
        )}
      </div>

      {showMilestone && nextMilestone && (
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs text-gray-800">
            <span>Prochain objectif</span>
            <span className="font-medium">{nextMilestone} jours</span>
          </div>
          <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-orange-400 to-orange-600"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5, ease: 'easeOut' }}
            />
          </div>
        </div>
      )}

      <AnimatePresence>
        {isAtMilestone && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-3 text-center"
          >
            <p className="text-sm font-medium text-orange-700">
              🎉 Félicitations ! {currentStreak} jours de suite !
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Composant pour afficher une alerte de streak
export function StreakAlert() {
  const { isStreakActive, daysUntilStreakLoss, currentStreak } = useStreak();
  
  if (!isStreakActive || daysUntilStreakLoss > 0 || currentStreak === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="fixed top-4 right-4 z-50 bg-orange-600 text-white rounded-lg shadow-lg p-4 max-w-sm"
    >
      <div className="flex items-center gap-3">
        <motion.span
          className="text-2xl"
          animate={{
            rotate: [0, -10, 10, -10, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{ duration: 0.5, repeat: Infinity, repeatDelay: 1 }}
        >
          🔥
        </motion.span>
        <div className="flex-1">
          <p className="font-semibold">Gardez votre streak !</p>
          <p className="text-sm opacity-90">
            Exprimez-vous aujourd&apos;hui pour maintenir vos {currentStreak} jours
          </p>
        </div>
      </div>
    </motion.div>
  );
}