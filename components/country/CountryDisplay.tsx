'use client';

import { Globe } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useEffect, useState } from 'react';
import { pillarScanAPI } from '@/lib/api/client';

const COUNTRY_FLAGS: Record<string, string> = {
  FR: '🇫🇷',
  US: '🇺🇸',
  CA: '🇨🇦',
  GB: '🇬🇧',
  DE: '🇩🇪',
  ES: '🇪🇸',
  IT: '🇮🇹',
  BR: '🇧🇷',
  // Ajouter plus de drapeaux...
};

const COUNTRY_NAMES: Record<string, string> = {
  FR: 'France',
  US: 'États-Unis',
  CA: 'Canada',
  GB: 'Royaume-Uni',
  DE: 'Allemagne',
  ES: 'Espagne',
  IT: 'Italie',
  BR: 'Brésil',
  // Ajouter plus de noms...
};

export function CountryDisplay() {
  const { isAuthenticated } = useAuth();
  const [country, setCountry] = useState<string | null>(null);

  useEffect(() => {
    // Récupérer le profil auth si connecté
    const loadCountry = async () => {
      if (isAuthenticated) {
        try {
          const profile = await pillarScanAPI.getAuthProfile();
          setCountry(profile.country);
        } catch (error) {
          console.error('Erreur récupération profil:', error);
        }
      }
    };
    
    loadCountry();
  }, [isAuthenticated]);

  // Si pas connecté ou pas de pays, ne rien afficher
  if (!isAuthenticated || !country) return null;

  const flag = COUNTRY_FLAGS[country] || '🌍';
  const name = COUNTRY_NAMES[country] || country;

  return (
    <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-100 dark:bg-gray-800 rounded-lg">
      <Globe className="h-4 w-4 text-gray-800 dark:text-gray-300" />
      <span className="text-lg">{flag}</span>
      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
        {name}
      </span>
    </div>
  );
}