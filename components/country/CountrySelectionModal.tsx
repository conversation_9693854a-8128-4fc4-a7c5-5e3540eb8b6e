'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/Button';
import { Label } from '@/components/ui/label';
import { Globe, MapPin, User, Search } from 'lucide-react';
import { Input } from '@/components/ui/Input';
import { ScrollArea } from '@/components/ui/scroll-area';

interface CountrySelectionModalProps {
  isOpen: boolean;
  onCountrySelect: (countryCode: string) => void;
}

interface Country {
  code: string;
  name: string;
  flag: string;
}

// Liste des pays principaux (à étendre)
const COUNTRIES: Country[] = [
  { code: 'FR', name: 'France', flag: '🇫🇷' },
  { code: 'US', name: 'États-Unis', flag: '🇺🇸' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦' },
  { code: 'GB', name: 'Royaume-Uni', flag: '🇬🇧' },
  { code: 'DE', name: 'Allemagne', flag: '🇩🇪' },
  { code: 'ES', name: '<PERSON><PERSON>ag<PERSON>', flag: '🇪🇸' },
  { code: 'IT', name: 'Italie', flag: '🇮🇹' },
  { code: 'BR', name: 'Brésil', flag: '🇧🇷' },
  { code: 'JP', name: 'Japon', flag: '🇯🇵' },
  { code: 'CN', name: 'Chine', flag: '🇨🇳' },
  { code: 'IN', name: 'Inde', flag: '🇮🇳' },
  { code: 'MX', name: 'Mexique', flag: '🇲🇽' },
  { code: 'AU', name: 'Australie', flag: '🇦🇺' },
  { code: 'ZA', name: 'Afrique du Sud', flag: '🇿🇦' },
  { code: 'NG', name: 'Nigeria', flag: '🇳🇬' },
  { code: 'EG', name: 'Égypte', flag: '🇪🇬' },
  { code: 'MA', name: 'Maroc', flag: '🇲🇦' },
  { code: 'TN', name: 'Tunisie', flag: '🇹🇳' },
  { code: 'SN', name: 'Sénégal', flag: '🇸🇳' },
  { code: 'CI', name: "Côte d'Ivoire", flag: '🇨🇮' },
  { code: 'CM', name: 'Cameroun', flag: '🇨🇲' },
  // Ajouter tous les pays...
];

export function CountrySelectionModal({
  isOpen,
  onCountrySelect
}: CountrySelectionModalProps) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);

  const filteredCountries = COUNTRIES.filter(country =>
    country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    country.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleGeolocation = async () => {
    if ('geolocation' in navigator) {
      try {
        // Demander l'autorisation de géolocalisation
        await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject);
        });

        // En production, utiliser un service de géocodage inverse
        // Pour l'instant, simuler avec l'API IP
        const response = await fetch('https://ipapi.co/json/');
        const data = await response.json();
        
        if (data.country_code) {
          onCountrySelect(data.country_code);
        }
      } catch (error) {
        console.error('Géolocalisation refusée:', error);
        alert('Veuillez autoriser la géolocalisation ou sélectionner manuellement votre pays.');
      }
    }
  };

  const handleLogin = () => {
    // Sauvegarder l'URL actuelle pour redirection après login
    sessionStorage.setItem('redirectAfterLogin', window.location.pathname);
    router.push('/login');
  };

  const handleCountrySelect = (countryCode: string) => {
    setSelectedCountry(countryCode);
    onCountrySelect(countryCode);
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent 
        className="sm:max-w-[500px]"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold flex items-center gap-2">
            <Globe className="w-6 h-6" />
            Sélection du pays obligatoire
          </DialogTitle>
          <DialogDescription className="text-base mt-2">
            PillarScan respecte la souveraineté des données. 
            Chaque pays gère ses propres données citoyennes.
            Vous devez sélectionner votre pays pour continuer.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-6">
          {/* Option 1: Géolocalisation automatique */}
          <div>
            <Button 
              onClick={handleGeolocation}
              variant="primary"
              className="w-full h-12 text-base"
            >
              <MapPin className="mr-2 h-5 w-5" />
              Utiliser ma position actuelle
            </Button>
            <p className="text-xs text-muted-foreground mt-1 text-center">
              Détection automatique du pays (recommandé)
            </p>
          </div>

          {/* Option 2: Se connecter */}
          <div>
            <Button 
              onClick={handleLogin}
              variant="secondary"
              className="w-full h-12 text-base"
            >
              <User className="mr-2 h-5 w-5" />
              Se connecter à mon compte
            </Button>
            <p className="text-xs text-muted-foreground mt-1 text-center">
              Votre profil contient votre pays principal
            </p>
          </div>

          {/* Séparateur */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Ou sélectionner manuellement
              </span>
            </div>
          </div>

          {/* Option 3: Sélection manuelle */}
          <div className="space-y-2">
            <Label htmlFor="country-search">Rechercher un pays</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="country-search"
                placeholder="France, Canada, Sénégal..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {/* Liste des pays */}
          <ScrollArea className="h-[200px] rounded-md border p-4">
            <div className="space-y-2">
              {filteredCountries.map((country) => (
                <button
                  key={country.code}
                  onClick={() => handleCountrySelect(country.code)}
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors hover:bg-accent hover:text-accent-foreground ${
                    selectedCountry === country.code ? 'bg-accent' : ''
                  }`}
                >
                  <span className="text-2xl mr-3">{country.flag}</span>
                  <span className="font-medium">{country.name}</span>
                  <span className="text-muted-foreground ml-2">({country.code})</span>
                </button>
              ))}
              {filteredCountries.length === 0 && (
                <p className="text-center text-muted-foreground py-4">
                  Aucun pays trouvé
                </p>
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Note importante */}
        <div className="mt-4 p-3 bg-muted rounded-md">
          <p className="text-sm text-muted-foreground">
            <strong>Note:</strong> Vous pourrez changer de pays plus tard dans les paramètres, 
            mais vous ne verrez que les expressions du pays sélectionné.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}