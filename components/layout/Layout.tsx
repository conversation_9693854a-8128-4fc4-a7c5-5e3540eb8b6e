'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { PillarScanAvatar } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const pathname = usePathname();
  const { isAuthenticated, profile } = useAuth();

  // Navigation minimale pour le MVP
  const navigation = [
    { name: 'Exprimer', href: '/create', icon: '✍️' },
    { name: 'Explorer', href: '/', icon: '🔍' },
  ];

  const isActive = (href: string) => {
    return pathname === href;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo et titre */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-2xl">🎯</span>
                </div>
                <span className="text-xl font-bold text-gray-900">PillarScan</span>
              </Link>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-1">
              {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      'flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                      isActive(item.href)
                        ? 'bg-blue-50 text-blue-700'
                        : 'text-gray-800 hover:bg-gray-100 hover:text-gray-900'
                    )}
                  >
                    <span>{item.icon}</span>
                    <span>{item.name}</span>
                  </Link>
                ))}
            </nav>

            {/* Actions utilisateur */}
            <div className="flex items-center space-x-4">
              {isAuthenticated && profile ? (
                <>
                  {/* Badges et XP */}
                  <div className="hidden sm:flex items-center space-x-2">
                    <Badge variant="info" icon={<span>⭐</span>}>
                      Niveau {profile.level || 1}
                    </Badge>
                    <Badge variant="warning" icon={<span>💎</span>}>
                      {profile.experience_points || 0} XP
                    </Badge>
                  </div>

                  {/* Menu utilisateur */}
                  <div className="relative">
                    <button className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors">
                      <PillarScanAvatar
                        avatarStyle={profile.avatar_style}
                        nickname={profile.nickname}
                        size="sm"
                      />
                      <span className="hidden sm:block text-sm font-medium text-gray-700">
                        {profile.nickname || 'Utilisateur'}
                      </span>
                      <svg className="w-4 h-4 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                    
                    {/* Dropdown menu (à implémenter) */}
                  </div>
                </>
              ) : (
                <div className="flex items-center space-x-2">
                  <Link
                    href="/auth/login"
                    className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900"
                  >
                    Connexion
                  </Link>
                  <Link
                    href="/auth/register"
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                  >
                    S&apos;inscrire
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Navigation mobile */}
      <nav className="md:hidden bg-white border-b border-gray-200">
        <div className="px-2 py-2 flex space-x-1 overflow-x-auto">
          {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap',
                  isActive(item.href)
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-800 hover:bg-gray-100 hover:text-gray-900'
                )}
              >
                <span>{item.icon}</span>
                <span>{item.name}</span>
              </Link>
            ))}
        </div>
      </nav>

      {/* Contenu principal */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-800">
                © 2025 PillarScan - SMATFLOW
              </span>
              <Link href="/about" className="text-sm text-gray-800 hover:text-gray-900">
                À propos
              </Link>
              <Link href="/privacy" className="text-sm text-gray-800 hover:text-gray-900">
                Confidentialité
              </Link>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-800">
                Fait avec ❤️ pour un monde meilleur
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}