'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { PillarScanAvatar } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';
import { motion, useScroll, useMotionValueEvent } from 'framer-motion';

interface ModernLayoutProps {
  children: React.ReactNode;
}

export function ModernLayout({ children }: ModernLayoutProps) {
  const pathname = usePathname();
  const { isAuthenticated, profile } = useAuth();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const { scrollY } = useScroll();

  useMotionValueEvent(scrollY, "change", (latest) => {
    setIsScrolled(latest > 10);
  });

  // Navigation avec icônes modernes
  const navigation = [
    { 
      name: 'Explorer', 
      href: '/', 
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      )
    },
    { 
      name: 'Exprimer', 
      href: '/create', 
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
        </svg>
      )
    },
  ];

  const isActive = (href: string) => pathname === href;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Background Pattern */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px] opacity-20" />
        <div className="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob" />
        <div className="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000" />
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000" />
      </div>

      {/* Header avec sticky et effets */}
      <motion.header 
        className={cn(
          "sticky top-0 z-50 transition-all duration-300",
          isScrolled 
            ? "bg-white/80 backdrop-blur-lg shadow-lg border-b border-gray-200/50" 
            : "bg-white/60 backdrop-blur-sm"
        )}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo avec animation */}
            <motion.div 
              className="flex items-center"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link href="/" className="flex items-center space-x-3 group">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl blur-lg opacity-80 group-hover:opacity-100 transition-opacity" />
                  <div className="relative w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <span className="text-white text-2xl">🎯</span>
                  </div>
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  PillarScan
                </span>
              </Link>
            </motion.div>

            {/* Navigation centrale avec effets */}
            <nav className="hidden md:flex items-center space-x-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="group relative"
                >
                  <motion.div
                    className={cn(
                      'flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium transition-all',
                      isActive(item.href)
                        ? 'text-white'
                        : 'text-gray-800 hover:text-gray-900'
                    )}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {isActive(item.href) && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl"
                        layoutId="navbar-active"
                        transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                      />
                    )}
                    <span className="relative z-10">{item.icon}</span>
                    <span className="relative z-10">{item.name}</span>
                  </motion.div>
                </Link>
              ))}
            </nav>

            {/* Actions utilisateur avec animations */}
            <div className="flex items-center space-x-4">
              {isAuthenticated && profile ? (
                <>
                  {/* Badges animés */}
                  <motion.div 
                    className="hidden sm:flex items-center space-x-2"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <Badge 
                        variant="info" 
                        icon={<span>⭐</span>}
                        className="bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-blue-200"
                      >
                        Niveau {profile.level || 1}
                      </Badge>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <Badge 
                        variant="warning" 
                        icon={<span>💎</span>}
                        className="bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-700 border-yellow-200"
                      >
                        {profile.experience_points || 0} XP
                      </Badge>
                    </motion.div>
                  </motion.div>

                  {/* Menu utilisateur amélioré */}
                  <div className="relative">
                    <motion.button 
                      className="flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100/80 transition-all group"
                      onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <div className="relative">
                        <PillarScanAvatar
                          avatarStyle={profile.avatar_style}
                          nickname={profile.nickname}
                          size="sm"
                        />
                        {profile.current_streak > 0 && (
                          <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg">
                            {profile.current_streak}
                          </div>
                        )}
                      </div>
                      <span className="hidden sm:block text-sm font-medium text-gray-700 group-hover:text-gray-900">
                        {profile.nickname || 'Utilisateur'}
                      </span>
                      <motion.svg 
                        className="w-4 h-4 text-gray-700"
                        animate={{ rotate: isProfileMenuOpen ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </motion.svg>
                    </motion.button>
                    
                    {/* Dropdown menu avec animations */}
                    {isProfileMenuOpen && (
                      <motion.div
                        className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-xl border border-gray-100 overflow-hidden"
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Link href="/profile" className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                          <div className="flex items-center space-x-2">
                            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <span>Mon profil</span>
                          </div>
                        </Link>
                        <div className="border-t border-gray-100" />
                        <button className="w-full text-left px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors">
                          <div className="flex items-center space-x-2">
                            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            <span>Déconnexion</span>
                          </div>
                        </button>
                      </motion.div>
                    )}
                  </div>
                </>
              ) : (
                <motion.div 
                  className="flex items-center space-x-2"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                >
                  <Link href="/auth/login">
                    <motion.button
                      className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Connexion
                    </motion.button>
                  </Link>
                  <Link href="/onboarding">
                    <motion.button
                      className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl hover:shadow-lg transition-all"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Commencer
                    </motion.button>
                  </Link>
                </motion.div>
              )}
            </div>
          </div>
        </div>

        {/* Navigation mobile avec animations */}
        <motion.nav 
          className="md:hidden bg-white/80 backdrop-blur-sm border-t border-gray-100"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
        >
          <div className="px-2 py-2 flex space-x-1 overflow-x-auto scrollbar-hide">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="relative"
              >
                <motion.div
                  className={cn(
                    'flex items-center space-x-2 px-3 py-2 rounded-xl text-sm font-medium whitespace-nowrap transition-all',
                    isActive(item.href)
                      ? 'text-white'
                      : 'text-gray-800'
                  )}
                  whileTap={{ scale: 0.95 }}
                >
                  {isActive(item.href) && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl"
                      layoutId="mobile-navbar-active"
                    />
                  )}
                  <span className="relative z-10">{item.icon}</span>
                  <span className="relative z-10">{item.name}</span>
                </motion.div>
              </Link>
            ))}
          </div>
        </motion.nav>
      </motion.header>

      {/* Contenu principal avec z-index approprié */}
      <main className="relative z-10 flex-1">
        {children}
      </main>

      {/* Footer moderne */}
      <footer className="relative z-10 bg-gradient-to-r from-gray-900 to-gray-800 text-white mt-20">
        <div className="absolute inset-0 bg-[radial-gradient(#4a5568_1px,transparent_1px)] [background-size:16px_16px] opacity-10" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl flex items-center justify-center">
                  <span className="text-white text-2xl">🎯</span>
                </div>
                <span className="text-xl font-bold">PillarScan</span>
              </div>
              <p className="text-gray-700 text-sm">
                Explorez les piliers de la démocratie et exprimez vos idées pour un monde meilleur.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Liens rapides</h3>
              <div className="space-y-2">
                <Link href="/about" className="block text-gray-700 hover:text-white text-sm transition-colors">
                  À propos
                </Link>
                <Link href="/privacy" className="block text-gray-700 hover:text-white text-sm transition-colors">
                  Confidentialité
                </Link>
                <Link href="/terms" className="block text-gray-700 hover:text-white text-sm transition-colors">
                  Conditions d&apos;utilisation
                </Link>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Communauté</h3>
              <p className="text-gray-700 text-sm mb-4">
                Rejoignez notre communauté grandissante d&apos;acteurs du changement.
              </p>
              <div className="flex space-x-4">
                <motion.a 
                  href="#" 
                  className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <span className="text-lg">🐦</span>
                </motion.a>
                <motion.a 
                  href="#" 
                  className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <span className="text-lg">📧</span>
                </motion.a>
              </div>
            </div>
          </div>
          
          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-sm text-gray-700">
            <p>© 2025 PillarScan - SMATFLOW. Fait avec ❤️ pour un monde meilleur.</p>
          </div>
        </div>
      </footer>

      {/* Styles pour animations */}
      <style jsx global>{`
        @keyframes blob {
          0% {
            transform: translate(0px, 0px) scale(1);
          }
          33% {
            transform: translate(30px, -50px) scale(1.1);
          }
          66% {
            transform: translate(-20px, 20px) scale(0.9);
          }
          100% {
            transform: translate(0px, 0px) scale(1);
          }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
}