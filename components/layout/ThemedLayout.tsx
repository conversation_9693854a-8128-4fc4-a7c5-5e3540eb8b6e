'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { PillarScanAvatar } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { ThemeSwitch } from '@/components/ui/ThemeSwitch';
import { NotificationCenter } from '@/components/notifications/NotificationCenter';
import { CountryDisplay } from '@/components/country/CountryDisplay';
import { cn } from '@/lib/utils';
import { motion, useScroll, useMotionValueEvent } from 'framer-motion';
import { Toaster } from 'react-hot-toast';

interface ThemedLayoutProps {
  children: React.ReactNode;
}

export function ThemedLayout({ children }: ThemedLayoutProps) {
  const pathname = usePathname();
  const { isAuthenticated, profile, logout } = useAuth();
  const { theme, themeMode } = useTheme();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { scrollY } = useScroll();

  React.useEffect(() => {
    setMounted(true);
  }, []);

  useMotionValueEvent(scrollY, "change", (latest) => {
    setIsScrolled(latest > 10);
  });

  // Fermer le menu quand on clique ailleurs
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.profile-menu-container')) {
        setIsProfileMenuOpen(false);
      }
    };

    if (isProfileMenuOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isProfileMenuOpen]);

  const navigation = [
    { 
      name: 'Explorer', 
      href: '/', 
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      )
    },
    { 
      name: 'Exprimer', 
      href: '/create', 
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
        </svg>
      )
    },
  ];

  const isActive = (href: string) => pathname === href;

  // Utilise des classes stables côté serveur
  const containerClasses = mounted 
    ? cn('min-h-screen transition-colors duration-500 relative', `bg-gradient-to-br ${theme.background.primary}`)
    : 'min-h-screen transition-colors duration-500 relative bg-gradient-to-br from-white to-gray-50';

  const blobClasses = {
    blob1: mounted ? theme.animation.blob1 : 'bg-blue-500',
    blob2: mounted ? theme.animation.blob2 : 'bg-purple-500', 
    blob3: mounted ? theme.animation.blob3 : 'bg-green-500',
  };

  return (
    <div className={cn(containerClasses, "flex flex-col")}>
      {/* Animated Background */}
      <div className="fixed inset-0 z-0 overflow-hidden pointer-events-none">
        {/* Pattern de fond dynamique */}
        <div className={cn(
          "absolute inset-0 opacity-20 transition-opacity duration-1000",
          themeMode === 'ocean' && "bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cpath d=\"M0 30c0-11.046 8.954-20 20-20s20 8.954 20 20-8.954 20-20 20-20-8.954-20-20z\" fill=\"%234ade80\" fill-opacity=\"0.1\"/%3E%3C/svg%3E')]",
          themeMode === 'aurora' && "bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))]"
        )} />
        
        {/* Animated blobs - TEMPORAIREMENT DÉSACTIVÉ POUR DEBUG */}
        {false && (
          <>
            <motion.div 
              className={cn("absolute top-0 -left-4 w-72 h-72 rounded-full mix-blend-multiply filter blur-xl opacity-30", blobClasses.blob1)}
              animate={{
                x: [0, 50, 0],
                y: [0, 30, 0],
              }}
              transition={{
                duration: 10,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div 
              className={cn("absolute top-0 -right-4 w-72 h-72 rounded-full mix-blend-multiply filter blur-xl opacity-30", blobClasses.blob2)}
              animate={{
                x: [0, -50, 0],
                y: [0, 50, 0],
              }}
              transition={{
                duration: 12,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2
              }}
            />
            <motion.div 
              className={cn("absolute -bottom-8 left-20 w-72 h-72 rounded-full mix-blend-multiply filter blur-xl opacity-30", blobClasses.blob3)}
              animate={{
                x: [0, 30, 0],
                y: [0, -30, 0],
              }}
              transition={{
                duration: 15,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 4
              }}
            />
          </>
        )}

        {/* Effet aurore boréale pour le thème aurora */}
        {themeMode === 'aurora' && (
          <>
            <motion.div 
              className="absolute inset-0 bg-gradient-to-t from-transparent via-green-500/10 to-transparent"
              animate={{
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div 
              className="absolute inset-0 bg-gradient-to-tr from-transparent via-purple-500/10 to-transparent"
              animate={{
                opacity: [0.1, 0.2, 0.1],
              }}
              transition={{
                duration: 10,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2
              }}
            />
          </>
        )}
      </div>

      {/* Header avec sticky et effets */}
      <header 
        className={cn(
          "sticky top-0 left-0 right-0 z-50 w-full transition-all duration-300",
          themeMode === 'dark' || themeMode === 'aurora' 
            ? "bg-gradient-to-r from-gray-900/95 to-gray-800/95" 
            : "bg-gradient-to-r from-gray-800/95 to-gray-700/95",
          isScrolled 
            ? "backdrop-blur-lg shadow-2xl border-b border-gray-700/50" 
            : "backdrop-blur-md",
          "text-white"
        )}
        style={{ position: 'sticky', top: 0 }}
      >
        {/* Pattern de fond subtil */}
        <div className="absolute inset-0 bg-[radial-gradient(#4a5568_1px,transparent_1px)] [background-size:16px_16px] opacity-10" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo avec animation */}
            <motion.div 
              className="flex items-center"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link href="/" className="flex items-center space-x-3 group">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-xl blur-lg opacity-80 group-hover:opacity-100 transition-opacity" />
                  <div className="relative w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
                    <span className="text-white text-2xl">🎯</span>
                  </div>
                </div>
                <span className="text-xl font-bold text-white">
                  PillarScan
                </span>
              </Link>
            </motion.div>

            {/* Navigation centrale */}
            <nav className="hidden md:flex items-center space-x-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="group relative"
                >
                  <motion.div
                    className={cn(
                      'flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium transition-all',
                      isActive(item.href)
                        ? 'text-white'
                        : 'text-gray-300 hover:text-white'
                    )}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {isActive(item.href) && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl"
                        layoutId="navbar-active"
                        transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                      />
                    )}
                    <span className="relative z-10">{item.icon}</span>
                    <span className="relative z-10">{item.name}</span>
                  </motion.div>
                </Link>
              ))}
            </nav>

            {/* Actions utilisateur avec ThemeSwitch */}
            <div className="flex items-center space-x-4">
              {/* Country Display */}
              <CountryDisplay />
              
              {/* Theme Switch */}
              <ThemeSwitch variant="compact" />

              {/* Notifications (si authentifié) */}
              {isAuthenticated && <NotificationCenter />}

              {isAuthenticated && profile ? (
                <>
                  {/* Badges animés */}
                  <motion.div 
                    className="hidden sm:flex items-center space-x-2"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <Badge 
                        variant="info" 
                        icon={<span>⭐</span>}
                        className="bg-gray-700/50 text-white border-gray-600"
                      >
                        Niveau {profile.level || 1}
                      </Badge>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <Badge 
                        variant="warning" 
                        icon={<span>💎</span>}
                        className="bg-gray-700/50 text-white border-gray-600"
                      >
                        {profile.experience_points || 0} XP
                      </Badge>
                    </motion.div>
                  </motion.div>

                  {/* Menu utilisateur */}
                  <div className="relative profile-menu-container">
                    <motion.button 
                      className="flex items-center space-x-2 p-2 rounded-xl transition-all group hover:bg-gray-700/50"
                      onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <div className="relative">
                        <PillarScanAvatar
                          avatarStyle={profile.avatar_style}
                          nickname={profile.nickname}
                          size="sm"
                        />
                        {profile.current_streak > 0 && (
                          <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg">
                            {profile.current_streak}
                          </div>
                        )}
                      </div>
                      <span className="hidden sm:block text-sm font-medium text-gray-200">
                        {profile.nickname || 'Utilisateur'}
                      </span>
                      <motion.svg 
                        className="w-4 h-4 text-gray-300"
                        animate={{ rotate: isProfileMenuOpen ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </motion.svg>
                    </motion.button>
                    
                    {/* Dropdown menu */}
                    {isProfileMenuOpen && (
                      <motion.div
                        className={cn(
                          "absolute right-0 mt-2 w-56 rounded-xl shadow-xl border overflow-hidden z-[60]",
                          theme.ui.card,
                          theme.ui.border
                        )}
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Link 
                          href="/profile" 
                          className={cn(
                            "block px-4 py-3 text-sm transition-colors",
                            theme.text.secondary,
                            theme.ui.cardHover
                          )}
                        >
                          <div className="flex items-center space-x-2">
                            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <span>Mon profil</span>
                          </div>
                        </Link>
                        <div className={cn("border-t", theme.ui.border)} />
                        <button 
                          onClick={() => {
                            setIsProfileMenuOpen(false);
                            logout();
                          }}
                          className={cn(
                            "w-full text-left px-4 py-3 text-sm text-red-600 transition-colors",
                            "hover:bg-red-50 dark:hover:bg-red-900/20"
                          )}
                        >
                          <div className="flex items-center space-x-2">
                            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            <span>Déconnexion</span>
                          </div>
                        </button>
                      </motion.div>
                    )}
                  </div>
                </>
              ) : (
                <motion.div 
                  className="flex items-center space-x-2"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                >
                  <Link href="/auth/login">
                    <motion.button
                      className="px-4 py-2 text-sm font-medium text-gray-300 hover:text-white transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Connexion
                    </motion.button>
                  </Link>
                  <Link href="/onboarding">
                    <motion.button
                      className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl hover:shadow-lg transition-all"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Commencer
                    </motion.button>
                  </Link>
                </motion.div>
              )}
            </div>
          </div>
        </div>

        {/* Navigation mobile */}
        <motion.nav 
          className="md:hidden bg-gray-800/80 backdrop-blur-sm border-t border-gray-700"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
        >
          <div className="px-2 py-2 flex space-x-1 overflow-x-auto scrollbar-hide">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="relative"
              >
                <motion.div
                  className={cn(
                    'flex items-center space-x-2 px-3 py-2 rounded-xl text-sm font-medium whitespace-nowrap transition-all',
                    isActive(item.href)
                      ? 'text-white'
                      : 'text-gray-300'
                  )}
                  whileTap={{ scale: 0.95 }}
                >
                  {isActive(item.href) && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl"
                      layoutId="mobile-navbar-active"
                    />
                  )}
                  <span className="relative z-10">{item.icon}</span>
                  <span className="relative z-10">{item.name}</span>
                </motion.div>
              </Link>
            ))}
          </div>
        </motion.nav>
      </header>

      {/* Contenu principal */}
      <main className="relative z-10 flex-1 min-h-0">
        {children}
      </main>

      {/* Footer moderne avec thème */}
      <footer className={cn(
        "relative z-10 mt-auto transition-colors duration-500",
        themeMode === 'dark' || themeMode === 'aurora' 
          ? "bg-gradient-to-r from-gray-900 to-gray-800" 
          : "bg-gradient-to-r from-gray-800 to-gray-700",
        "text-white"
      )}>
        <div className="absolute inset-0 bg-[radial-gradient(#4a5568_1px,transparent_1px)] [background-size:16px_16px] opacity-10" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl flex items-center justify-center">
                  <span className="text-white text-2xl">🎯</span>
                </div>
                <span className="text-xl font-bold">PillarScan</span>
              </div>
              <p className="text-gray-300 text-sm">
                Explorez les piliers de la démocratie et exprimez vos idées pour un monde meilleur.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Liens rapides</h3>
              <div className="space-y-2">
                <Link href="/about" className="block text-gray-300 hover:text-white text-sm transition-colors">
                  À propos
                </Link>
                <Link href="/privacy" className="block text-gray-300 hover:text-white text-sm transition-colors">
                  Confidentialité
                </Link>
                <Link href="/terms" className="block text-gray-300 hover:text-white text-sm transition-colors">
                  Conditions d&apos;utilisation
                </Link>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Communauté</h3>
              <p className="text-gray-300 text-sm mb-4">
                Rejoignez notre communauté grandissante d&apos;acteurs du changement.
              </p>
              <div className="flex space-x-4">
                <motion.a 
                  href="#" 
                  className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <span className="text-lg">🐦</span>
                </motion.a>
                <motion.a 
                  href="#" 
                  className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <span className="text-lg">📧</span>
                </motion.a>
              </div>
            </div>
          </div>
          
          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-sm text-gray-300">
            <p>© 2025 PillarScan - SMATFLOW. Fait avec ❤️ pour un monde meilleur.</p>
          </div>
        </div>
      </footer>

      {/* Styles pour animations */}
      <style jsx global>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>

      {/* Toaster pour les notifications */}
      <Toaster 
        position="bottom-right"
        toastOptions={{
          duration: 5000,
          style: {
            background: themeMode === 'dark' || themeMode === 'aurora' ? '#1f2937' : '#ffffff',
            color: themeMode === 'dark' || themeMode === 'aurora' ? '#f3f4f6' : '#111827',
            borderRadius: '0.75rem',
            fontSize: '0.875rem',
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#f3f4f6',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#f3f4f6',
            },
          },
        }}
      />
    </div>
  );
}