'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { ExpressionCard } from '@/components/pillarscan/ExpressionCard';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { ErrorMessage } from '@/components/ui/ErrorMessage';
import { pillarScanAPI } from '@/lib/api/client';
import { PillarScanExpression, Mood } from '@/lib/types/pillarscan';
import { useAuth } from '@/contexts/AuthContext';
import { useDebounce } from '@/hooks/useDebounce';
import { Search, X } from 'lucide-react';
// Country selection removed - now comes from user profile

interface ExpressionFeedProps {
  initialMood?: Mood | 'all';
  showFilters?: boolean;
}

type SortOption = 'recent' | 'popular' | 'impact';

const MOOD_FILTERS: { value: Mood | 'all'; label: string; icon: string }[] = [
  { value: 'all', label: 'Tous', icon: '🎭' },
  { value: 'frustrated', label: 'Frustré', icon: '😤' },
  { value: 'happy', label: 'Content', icon: '😊' },
  { value: 'idea', label: 'Idée', icon: '💡' },
  { value: 'question', label: 'Question', icon: '❓' },
];

const SORT_OPTIONS: { value: SortOption; label: string; icon: string }[] = [
  { value: 'recent', label: 'Récentes', icon: '🕐' },
  { value: 'popular', label: 'Populaires', icon: '🔥' },
  { value: 'impact', label: 'Impact', icon: '💫' },
];

export function ExpressionFeed({ initialMood = 'all', showFilters = true }: ExpressionFeedProps) {
  const { isAuthenticated } = useAuth();
  const [expressions, setExpressions] = useState<PillarScanExpression[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>('');
  const [selectedMood, setSelectedMood] = useState<Mood | 'all'>(initialMood);
  const [sortBy, setSortBy] = useState<SortOption>('recent');
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');

  // Débounce la recherche pour éviter trop de requêtes
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  // Intersection observer pour le scroll infini - DÉSACTIVÉ TEMPORAIREMENT
  // const { ref: loadMoreRef, inView } = useInView({
  //   threshold: 0,
  //   rootMargin: '100px',
  // });

  // Charge les expressions
  const loadExpressions = useCallback(
    async (reset = false) => {
      if (!reset && (!hasMore || loadingMore)) return;

      try {
        if (reset) {
          setLoading(true);
          setPage(1);
        } else {
          setLoadingMore(true);
        }
        setError('');

        const params: Record<string, string | number | boolean> = {
          limit: 20,
          offset: reset ? 0 : (page - 1) * 20,
          with_media: true, // Charger les URLs MinIO pour l'affichage
        };

        if (selectedMood !== 'all') {
          params.mood = selectedMood;
        }

        if (debouncedSearchQuery.trim()) {
          params.search = debouncedSearchQuery.trim();
        }

        // TODO: Implémenter le tri côté backend
        // Pour l'instant on utilise juste les expressions récentes

        const response = await pillarScanAPI.getExpressions(
          params as Parameters<typeof pillarScanAPI.getExpressions>[0]
        );
        const newExpressions = response.results || [];

        if (reset) {
          setExpressions(newExpressions);
        } else {
          // Deduplicate expressions by expression_id to avoid duplicate keys
          setExpressions((prev) => {
            const existingIds = new Set(prev.map((e) => e.expression_id));
            const uniqueNewExpressions = newExpressions.filter(
              (e) => !existingIds.has(e.expression_id)
            );
            return [...prev, ...uniqueNewExpressions];
          });
        }

        setHasMore(newExpressions.length === 20);
        if (!reset) setPage((prev) => prev + 1);
      } catch (err) {
        console.error('Erreur chargement expressions:', err);

        // Les erreurs sont déjà formatées avec des messages user-friendly par le client API
        const errorMessage =
          err instanceof Error ? err.message : 'Impossible de charger les expressions';
        setError(errorMessage);

        // No need to handle country errors anymore - country comes from user profile
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [selectedMood, page, hasMore, loadingMore, debouncedSearchQuery]
  );

  // Charge plus d'expressions quand on scrolle avec debounce
  // TEMPORAIREMENT DÉSACTIVÉ POUR DEBUG
  /*
  useEffect(() => {
    if (inView && hasMore && !loadingMore && !loading) {
      const timer = setTimeout(() => {
        loadExpressions();
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [inView, hasMore, loadingMore, loading, loadExpressions]);
  */

  // Recharge quand les filtres ou la recherche changent
  useEffect(() => {
    loadExpressions(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedMood, sortBy, debouncedSearchQuery]);

  // Gestion du relate
  const handleRelate = async (expressionId: string) => {
    if (!isAuthenticated) {
      window.location.href = '/auth/login';
      return;
    }

    try {
      const expression = expressions.find((e) => e.expression_id === expressionId);
      if (expression?.user_has_related) {
        await pillarScanAPI.unrelateFromExpression(expressionId);
      } else {
        await pillarScanAPI.relateToExpression(expressionId);
      }

      // Met à jour localement pour une réponse instantanée
      setExpressions((prev) =>
        prev.map((expr) =>
          expr.expression_id === expressionId
            ? {
                ...expr,
                user_has_related: !expr.user_has_related,
                relate_count: expr.user_has_related
                  ? Math.max(0, (expr.relate_count || 0) - 1)
                  : (expr.relate_count || 0) + 1,
              }
            : expr
        )
      );
    } catch (err) {
      console.error('Erreur relate:', err);
    }
  };

  // Tri local des expressions (en attendant le backend)
  const sortedExpressions = React.useMemo(() => {
    const sorted = [...expressions];

    switch (sortBy) {
      case 'popular':
        return sorted.sort((a, b) => (b.relate_count || 0) - (a.relate_count || 0));
      case 'impact':
        // Score d'impact simple basé sur relates et âge
        return sorted.sort((a, b) => {
          const scoreA = (a.relate_count || 0) * 10;
          const scoreB = (b.relate_count || 0) * 10;
          return scoreB - scoreA;
        });
      case 'recent':
      default:
        return sorted; // Déjà triées par date
    }
  }, [expressions, sortBy]);

  // No country selection needed - comes from user profile or public access

  return (
    <div className="space-y-6">
      {/* Filtres */}
      {showFilters && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          {/* Barre de recherche */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Input
                type="search"
                placeholder="Rechercher dans les expressions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-10"
                startIcon={<Search className="w-5 h-5 text-gray-700" />}
                endIcon={
                  searchQuery && (
                    <button
                      onClick={() => setSearchQuery('')}
                      className="hover:bg-gray-100 rounded-full p-1 transition-colors"
                    >
                      <X className="w-4 h-4 text-gray-700" />
                    </button>
                  )
                }
              />
              {loading && debouncedSearchQuery && (
                <div className="absolute right-12 top-1/2 -translate-y-1/2">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-blue-600" />
                </div>
              )}
            </div>
          </div>

          {/* Filtres de mood */}
          <div className="flex items-center gap-2 flex-wrap">
            {MOOD_FILTERS.map((filter) => (
              <motion.button
                key={filter.value}
                onClick={() => setSelectedMood(filter.value as Mood | 'all')}
                className={`inline-flex items-center gap-1.5 px-4 py-2 rounded-full text-sm font-medium transition-all ${
                  selectedMood === filter.value
                    ? 'bg-blue-600 text-white shadow-lg scale-105'
                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="text-base">{filter.icon}</span>
                {filter.label}
              </motion.button>
            ))}
          </div>

          {/* Options de tri */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">Trier par :</span>
            {SORT_OPTIONS.map((option) => (
              <motion.button
                key={option.value}
                onClick={() => setSortBy(option.value)}
                className={`inline-flex items-center gap-1 px-3 py-1.5 rounded-lg text-sm font-medium transition-all ${
                  sortBy === option.value
                    ? 'bg-gray-900 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="text-sm">{option.icon}</span>
                {option.label}
              </motion.button>
            ))}
          </div>
        </motion.div>
      )}

      {/* Liste des expressions */}
      {loading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: i * 0.1 }}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse"
            >
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full" />
                <div className="flex-1 space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-1/4" />
                  <div className="h-4 bg-gray-200 rounded w-3/4" />
                  <div className="h-4 bg-gray-200 rounded w-1/2" />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : error ? (
        <ErrorMessage
          error={error}
          onRetry={() => {
            setError(null);
            loadExpressions(true);
          }}
          className="py-12"
        />
      ) : sortedExpressions.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center py-12 bg-white rounded-lg border border-gray-200"
        >
          <p className="text-gray-700 mb-4">
            {searchQuery
              ? `Aucun résultat pour "${searchQuery}"`
              : selectedMood === 'all'
                ? 'Aucune expression pour le moment'
                : `Aucune expression "${MOOD_FILTERS.find((m) => m.value === selectedMood)?.label}"`}
          </p>
          {isAuthenticated && (
            <Link href="/create">
              <Button>Soyez le premier à vous exprimer !</Button>
            </Link>
          )}
        </motion.div>
      ) : (
        <AnimatePresence mode="popLayout">
          <div className="space-y-4">
            {sortedExpressions.map((expression, index) => (
              <motion.div
                key={expression.expression_id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.05 }}
                layout
              >
                <ExpressionCard
                  expression={expression}
                  onRelate={() => handleRelate(expression.expression_id)}
                />
              </motion.div>
            ))}
          </div>
        </AnimatePresence>
      )}

      {/* Bouton charger plus - TEMPORAIRE POUR DEBUG */}
      {hasMore && !loading && sortedExpressions.length > 0 && (
        <div className="py-8 text-center">
          {loadingMore ? (
            <div className="flex justify-center items-center gap-2 text-gray-700">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                className="w-5 h-5 border-2 border-gray-300 border-t-blue-600 rounded-full"
              />
              <span className="text-sm">Chargement...</span>
            </div>
          ) : (
            <Button onClick={() => loadExpressions()} variant="secondary" size="md">
              Charger plus d&apos;expressions
            </Button>
          )}
        </div>
      )}

      {/* Message de fin */}
      {!hasMore && sortedExpressions.length > 0 && !loading && !loadingMore && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-center py-8 text-gray-700 text-sm"
        >
          Vous avez tout vu ! 🎉
        </motion.div>
      )}
    </div>
  );
}
