'use client';

import { useEffect } from 'react';
import { Brain, AlertCircle, CheckCircle } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/progress';
import { useRealtimeClassification, useClassification } from '@/hooks/useClassification';
import { cn } from '@/lib/utils';
import { useDebounce } from '@/hooks/useDebounce';

interface ClassificationData {
  status: string;
  classification?: {
    category?: { code: string; name: string; confidence: number };
    subcategory?: { code: string; name: string; confidence: number };
    topic?: { code: string; name: string; confidence: number };
  };
  sentiment?: {
    value: string;
    intensity: number;
  };
}

interface ClassificationPreviewProps {
  text: string;
  expressionId?: string;
  onClassificationComplete?: (classification: ClassificationData) => void;
  className?: string;
}

export function ClassificationPreview({
  text,
  expressionId,
  onClassificationComplete,
  className,
}: ClassificationPreviewProps) {
  const debouncedText = useDebounce(text, 500);
  const { preview, isClassifying, classifyText } = useRealtimeClassification();
  const { classification } = useClassification(expressionId);

  // Classification en temps réel pendant la saisie
  useEffect(() => {
    if (!expressionId && debouncedText) {
      classifyText(debouncedText);
    }
  }, [debouncedText, expressionId, classifyText]);

  // Callback quand la classification est terminée
  useEffect(() => {
    if (classification?.status === 'completed' && onClassificationComplete) {
      onClassificationComplete(classification);
    }
  }, [classification, onClassificationComplete]);

  // Si on a un ID d'expression, afficher la vraie classification
  if (expressionId && classification) {
    return (
      <Card className={cn('p-4', className)}>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Brain className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Classification AI</span>
            </div>
            <StatusBadge status={classification.status} />
          </div>

          {classification.status === 'completed' && classification.classification && (
            <>
              <div className="space-y-2">
                <CategoryDisplay
                  label="Pilier"
                  category={classification.classification.category}
                />
                {classification.classification.subcategory && (
                  <CategoryDisplay
                    label="Sous-catégorie"
                    category={classification.classification.subcategory}
                    isSubLevel
                  />
                )}
                {classification.classification.topic && (
                  <CategoryDisplay
                    label="Sujet"
                    category={classification.classification.topic}
                    isSubLevel
                  />
                )}
              </div>

              {classification.sentiment && (
                <SentimentDisplay sentiment={classification.sentiment} />
              )}

              {classification.key_phrases && classification.key_phrases.length > 0 && (
                <div className="space-y-1">
                  <span className="text-xs text-muted-foreground">Phrases clés</span>
                  <div className="flex flex-wrap gap-1">
                    {classification.key_phrases.slice(0, 3).map((phrase, idx) => (
                      <Badge key={idx} variant="secondary" className="text-xs">
                        {phrase.text}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}

          {classification.status === 'pending' && (
            <div className="text-center py-4">
              <div className="animate-pulse flex items-center justify-center gap-2">
                <Brain className="h-5 w-5 text-primary animate-bounce" />
                <span className="text-sm text-muted-foreground">
                  Classification en cours...
                </span>
              </div>
            </div>
          )}

          {classification.status === 'failed' && (
            <div className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">Échec de la classification</span>
            </div>
          )}
        </div>
      </Card>
    );
  }

  // Sinon, afficher le preview en temps réel
  if (!preview && !isClassifying) return null;

  return (
    <Card className={cn('p-3 animate-in fade-in duration-200', className)}>
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Brain className="h-4 w-4 text-primary animate-pulse" />
          <span className="text-xs text-muted-foreground">
            {isClassifying ? 'Analyse en cours...' : 'Prédiction'}
          </span>
        </div>

        {preview && (
          <div className="text-xs text-muted-foreground">
            L&apos;IA analysera votre expression pour la classifier dans l&apos;un des 12 piliers
            du développement humain.
          </div>
        )}
      </div>
    </Card>
  );
}

interface CategoryDisplayProps {
  label: string;
  category: {
    code: string;
    name: string;
    confidence: number;
  };
  isSubLevel?: boolean;
}

function CategoryDisplay({ label, category, isSubLevel }: CategoryDisplayProps) {
  const confidenceColor = 
    category.confidence >= 0.8 ? 'text-green-600' :
    category.confidence >= 0.6 ? 'text-yellow-600' :
    'text-red-600';

  return (
    <div className={cn('flex items-center justify-between', isSubLevel && 'pl-4')}>
      <div className="flex items-center gap-2">
        <span className="text-xs text-muted-foreground">{label}:</span>
        <span className="text-sm font-medium">{category.name}</span>
      </div>
      <div className="flex items-center gap-2">
        <Progress 
          value={category.confidence * 100} 
          className="w-16 h-2" 
        />
        <span className={cn('text-xs font-medium', confidenceColor)}>
          {Math.round(category.confidence * 100)}%
        </span>
      </div>
    </div>
  );
}

interface SentimentDisplayProps {
  sentiment: {
    value: string;
    intensity: number;
  };
}

function SentimentDisplay({ sentiment }: SentimentDisplayProps) {
  const sentimentConfig = {
    POSITIVE: { label: 'Positif', color: 'text-green-600', bg: 'bg-green-50' },
    NEGATIVE: { label: 'Négatif', color: 'text-red-600', bg: 'bg-red-50' },
    NEUTRAL: { label: 'Neutre', color: 'text-gray-800', bg: 'bg-gray-50' },
    MIXED: { label: 'Mixte', color: 'text-orange-600', bg: 'bg-orange-50' },
  };

  const config = sentimentConfig[sentiment.value as keyof typeof sentimentConfig] || sentimentConfig.NEUTRAL;

  return (
    <div className="flex items-center justify-between pt-2 border-t">
      <span className="text-xs text-muted-foreground">Sentiment</span>
      <div className="flex items-center gap-2">
        <Badge className={cn(config.bg, config.color, 'border-0')}>
          {config.label}
        </Badge>
        <span className="text-xs text-muted-foreground">
          {Math.round(sentiment.intensity * 100)}%
        </span>
      </div>
    </div>
  );
}

function StatusBadge({ status }: { status: string }) {
  const statusConfig = {
    pending: { icon: Brain, label: 'En cours', className: 'bg-yellow-50 text-yellow-700' },
    completed: { icon: CheckCircle, label: 'Complété', className: 'bg-green-50 text-green-700' },
    failed: { icon: AlertCircle, label: 'Échoué', className: 'bg-red-50 text-red-700' },
    manual: { icon: CheckCircle, label: 'Manuel', className: 'bg-blue-50 text-blue-700' },
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
  const Icon = config.icon;

  return (
    <Badge className={cn('gap-1', config.className)}>
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
}