import React from 'react';
import { motion } from 'framer-motion';

interface EmptyStateProps {
  title: string;
  description?: string;
  action?: React.ReactNode;
  variant?: 'default' | 'search' | 'error' | 'success';
}

export function EmptyState({ 
  title, 
  description, 
  action,
  variant = 'default' 
}: EmptyStateProps) {
  
  const illustrations = {
    default: (
      <svg className="w-64 h-64" viewBox="0 0 200 200" fill="none">
        <motion.circle
          cx="100"
          cy="100"
          r="80"
          stroke="url(#gradient1)"
          strokeWidth="2"
          strokeDasharray="10 5"
          initial={{ rotate: 0 }}
          animate={{ rotate: 360 }}
          transition={{ duration: 50, repeat: Infinity, ease: "linear" }}
        />
        <motion.path
          d="M70 80 Q100 60 130 80 T130 120 Q100 140 70 120 T70 80"
          fill="url(#gradient2)"
          initial={{ scale: 0.8, opacity: 0.5 }}
          animate={{ scale: 1, opacity: 0.8 }}
          transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
        />
        <defs>
          <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.2" />
            <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.2" />
          </linearGradient>
          <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.1" />
            <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.1" />
          </linearGradient>
        </defs>
      </svg>
    ),
    search: (
      <svg className="w-64 h-64" viewBox="0 0 200 200" fill="none">
        <motion.circle
          cx="80"
          cy="80"
          r="40"
          stroke="#3b82f6"
          strokeWidth="8"
          fill="none"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5 }}
        />
        <motion.line
          x1="110"
          y1="110"
          x2="140"
          y2="140"
          stroke="#3b82f6"
          strokeWidth="8"
          strokeLinecap="round"
          initial={{ opacity: 0, x: -10, y: -10 }}
          animate={{ opacity: 1, x: 0, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        />
        <motion.path
          d="M60 80 Q80 70 100 80"
          stroke="#8b5cf6"
          strokeWidth="4"
          strokeLinecap="round"
          fill="none"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
        />
      </svg>
    ),
    error: (
      <svg className="w-64 h-64" viewBox="0 0 200 200" fill="none">
        <motion.circle
          cx="100"
          cy="100"
          r="80"
          fill="#fee2e2"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 200, damping: 20 }}
        />
        <motion.path
          d="M100 60 v40 m0 20 v0"
          stroke="#ef4444"
          strokeWidth="8"
          strokeLinecap="round"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        />
      </svg>
    ),
    success: (
      <svg className="w-64 h-64" viewBox="0 0 200 200" fill="none">
        <motion.circle
          cx="100"
          cy="100"
          r="80"
          fill="#d1fae5"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 200, damping: 20 }}
        />
        <motion.path
          d="M60 100 l30 30 l50 -60"
          stroke="#10b981"
          strokeWidth="8"
          strokeLinecap="round"
          strokeLinejoin="round"
          fill="none"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        />
      </svg>
    ),
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center py-12 px-4"
    >
      <div className="mb-8">
        {illustrations[variant]}
      </div>
      
      <h3 className="text-xl font-semibold text-gray-900 mb-2 text-center">
        {title}
      </h3>
      
      {description && (
        <p className="text-gray-800 text-center mb-6 max-w-md">
          {description}
        </p>
      )}
      
      {action && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          {action}
        </motion.div>
      )}
    </motion.div>
  );
}

// Illustration animée pour le hero/header
export function HeroIllustration() {
  return (
    <svg className="w-full h-full" viewBox="0 0 400 300" fill="none">
      {/* Formes de fond animées */}
      <motion.circle
        cx="50"
        cy="50"
        r="30"
        fill="url(#hero-gradient-1)"
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 0.5 }}
        transition={{ duration: 1, delay: 0 }}
      />
      <motion.circle
        cx="350"
        cy="250"
        r="40"
        fill="url(#hero-gradient-2)"
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 0.5 }}
        transition={{ duration: 1, delay: 0.2 }}
      />
      <motion.path
        d="M200 50 Q250 100 200 150 T200 250"
        stroke="url(#hero-gradient-3)"
        strokeWidth="2"
        fill="none"
        initial={{ pathLength: 0, opacity: 0 }}
        animate={{ pathLength: 1, opacity: 0.3 }}
        transition={{ duration: 2, delay: 0.5 }}
      />
      
      {/* Éléments principaux */}
      <motion.g
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.8 }}
      >
        {/* Icônes des moods */}
        <text x="100" y="150" fontSize="40" textAnchor="middle">😤</text>
        <text x="200" y="150" fontSize="40" textAnchor="middle">😊</text>
        <text x="300" y="150" fontSize="40" textAnchor="middle">💡</text>
        
        {/* Connexions entre les moods */}
        <motion.path
          d="M120 150 L180 150 M220 150 L280 150"
          stroke="#e5e7eb"
          strokeWidth="2"
          strokeDasharray="4 4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2 }}
        />
      </motion.g>
      
      <defs>
        <linearGradient id="hero-gradient-1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3b82f6" />
          <stop offset="100%" stopColor="#8b5cf6" />
        </linearGradient>
        <linearGradient id="hero-gradient-2" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#10b981" />
          <stop offset="100%" stopColor="#3b82f6" />
        </linearGradient>
        <linearGradient id="hero-gradient-3" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stopColor="#8b5cf6" />
          <stop offset="100%" stopColor="#ec4899" />
        </linearGradient>
      </defs>
    </svg>
  );
}