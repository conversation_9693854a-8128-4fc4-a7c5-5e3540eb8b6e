/**
 * Indicateur de statut de connexion
 */

'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { WifiOff, Wifi, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

export function OfflineIndicator() {
  const [isOnline, setIsOnline] = useState(true);
  const [showIndicator, setShowIndicator] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  useEffect(() => {
    // Vérifier le statut initial
    setIsOnline(navigator.onLine);
    
    // Écouter les changements de connexion
    const handleOnline = () => {
      setIsOnline(true);
      setShowIndicator(true);
      
      // Masquer après 3 secondes si en ligne
      setTimeout(() => {
        if (navigator.onLine) {
          setShowIndicator(false);
        }
      }, 3000);

      // Déclencher la synchronisation
      syncPendingData();
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowIndicator(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Vérifier périodiquement la connexion
    const intervalId = setInterval(() => {
      const wasOnline = isOnline;
      const nowOnline = navigator.onLine;
      
      if (wasOnline !== nowOnline) {
        setIsOnline(nowOnline);
        setShowIndicator(true);
        
        if (nowOnline) {
          handleOnline();
        }
      }
    }, 5000);

    // Montrer l'indicateur si hors ligne au démarrage
    if (!navigator.onLine) {
      setShowIndicator(true);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(intervalId);
    };
  }, [isOnline]);

  const syncPendingData = async () => {
    if (!navigator.onLine || !('serviceWorker' in navigator)) return;

    setIsSyncing(true);

    try {
      // Déclencher la synchronisation via le Service Worker
      const registration = await navigator.serviceWorker.ready;
      if ('sync' in registration) {
        await (registration.sync as SyncManager).register('sync-expressions');
        console.log('Synchronisation déclenchée');
      }
    } catch (error) {
      console.error('Erreur synchronisation:', error);
    } finally {
      setTimeout(() => setIsSyncing(false), 2000);
    }
  };

  return (
    <AnimatePresence>
      {showIndicator && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className={cn(
            "fixed top-16 left-1/2 -translate-x-1/2 z-50",
            "px-4 py-2 rounded-full",
            "backdrop-blur-lg shadow-lg",
            "flex items-center gap-2",
            "text-sm font-medium",
            isOnline 
              ? "bg-green-500/10 text-green-700 dark:text-green-400 border border-green-500/20"
              : "bg-red-500/10 text-red-700 dark:text-red-400 border border-red-500/20"
          )}
        >
          {isSyncing ? (
            <>
              <RefreshCw className="w-4 h-4 animate-spin" />
              <span>Synchronisation...</span>
            </>
          ) : isOnline ? (
            <>
              <Wifi className="w-4 h-4" />
              <span>Connexion rétablie</span>
            </>
          ) : (
            <>
              <WifiOff className="w-4 h-4" />
              <span>Mode hors ligne</span>
            </>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

/**
 * Hook pour détecter le statut de connexion
 */
export function useOnlineStatus() {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    setIsOnline(navigator.onLine);

    const handleStatusChange = () => {
      setIsOnline(navigator.onLine);
    };

    window.addEventListener('online', handleStatusChange);
    window.addEventListener('offline', handleStatusChange);

    return () => {
      window.removeEventListener('online', handleStatusChange);
      window.removeEventListener('offline', handleStatusChange);
    };
  }, []);

  return isOnline;
}