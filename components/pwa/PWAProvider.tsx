/**
 * Provider pour gérer l'initialisation PWA
 */

'use client';

import React, { useEffect } from 'react';
import { InstallPrompt } from './InstallPrompt';
import { OfflineIndicator } from './OfflineIndicator';
import { getOfflineStorage } from '@/lib/services/OfflineStorageService';

export function PWAProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Enregistrer le Service Worker
    if ('serviceWorker' in navigator) {
      registerServiceWorker();
    }

    // Initialiser IndexedDB
    initOfflineStorage();
  }, []);

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      console.log('Service Worker enregistré:', registration.scope);

      // Vérifier les mises à jour
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (!newWorker) return;

        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'activated') {
            // Nouvelle version disponible
            if (confirm('Une nouvelle version est disponible. Recharger ?')) {
              window.location.reload();
            }
          }
        });
      });

      // Écouter les messages du SW
      navigator.serviceWorker.addEventListener('message', (event) => {
        console.log('Message du SW:', event.data);
        
        if (event.data.type === 'SYNC_SUCCESS') {
          // Une expression a été synchronisée
          // TODO: Mettre à jour l'UI ou afficher une notification
        }
      });

      // Vérifier si un SW est en attente
      if (registration.waiting) {
        // Un nouveau SW est prêt
        if (confirm('Une mise à jour est disponible. Installer maintenant ?')) {
          registration.waiting.postMessage({ type: 'SKIP_WAITING' });
        }
      }

    } catch (error) {
      console.error('Erreur enregistrement Service Worker:', error);
    }
  };

  const initOfflineStorage = async () => {
    try {
      const storage = getOfflineStorage();
      await storage.init();
      console.log('IndexedDB initialisée');

      // Nettoyer les anciennes données
      await storage.cleanup(7);

      // Afficher les stats
      const stats = await storage.getStorageStats();
      console.log('Stats stockage:', stats);
    } catch (error) {
      console.error('Erreur initialisation IndexedDB:', error);
    }
  };

  return (
    <>
      {children}
      <OfflineIndicator />
      <InstallPrompt />
    </>
  );
}