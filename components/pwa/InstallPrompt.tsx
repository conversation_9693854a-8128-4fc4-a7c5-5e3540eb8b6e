/**
 * Composant pour proposer l'installation de la PWA
 */

'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, X, Smartphone, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export function InstallPrompt() {
  const [showPrompt, setShowPrompt] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstalling, setIsInstalling] = useState(false);
  const [isIOS, setIsIOS] = useState(false);

  useEffect(() => {
    // Vérifier si déjà installé
    if (window.matchMedia('(display-mode: standalone)').matches) {
      return;
    }

    // Vérifier si iOS
    const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);
    setIsIOS(isIOSDevice);

    // Vérifier si l'utilisateur a déjà refusé
    const dismissed = localStorage.getItem('pwa-install-dismissed');
    const dismissedTime = dismissed ? parseInt(dismissed) : 0;
    const daysSinceDismissed = (Date.now() - dismissedTime) / (1000 * 60 * 60 * 24);
    
    // Ne pas montrer si refusé il y a moins de 7 jours
    if (dismissedTime && daysSinceDismissed < 7) {
      return;
    }

    // Écouter l'événement beforeinstallprompt
    const handleBeforeInstall = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      
      // Montrer le prompt après un délai
      setTimeout(() => {
        setShowPrompt(true);
      }, 2000);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstall);

    // Sur iOS, montrer les instructions manuelles
    if (isIOSDevice && !window.navigator.standalone) {
      setTimeout(() => {
        setShowPrompt(true);
      }, 3000);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstall);
    };
  }, []);

  const handleInstall = async () => {
    if (!deferredPrompt) return;

    setIsInstalling(true);

    try {
      // Montrer le prompt natif
      await deferredPrompt.prompt();
      
      // Attendre le choix de l'utilisateur
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('PWA installée');
        setShowPrompt(false);
      } else {
        console.log('Installation refusée');
        handleDismiss();
      }
    } catch (error) {
      console.error('Erreur installation:', error);
    } finally {
      setIsInstalling(false);
      setDeferredPrompt(null);
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    localStorage.setItem('pwa-install-dismissed', Date.now().toString());
  };

  if (!showPrompt) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 100 }}
        className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 z-50"
      >
        <div className={cn(
          "bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden",
          "border border-gray-200 dark:border-gray-700"
        )}>
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-4 text-white">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-white/20 backdrop-blur rounded-xl p-2">
                  <Smartphone className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">
                    Installer PillarScan
                  </h3>
                  <p className="text-sm text-white/80">
                    Accès rapide et mode hors ligne
                  </p>
                </div>
              </div>
              <button
                onClick={handleDismiss}
                className="text-white/80 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-4">
            {isIOS ? (
              // Instructions iOS
              <div className="space-y-3">
                <p className="text-sm text-gray-800 dark:text-gray-300">
                  Pour installer sur iOS :
                </p>
                <ol className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <span className="text-blue-600 font-semibold">1.</span>
                    <span>Appuyez sur le bouton partage 
                      <span className="inline-block mx-1">􀈂</span>
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-blue-600 font-semibold">2.</span>
                    <span>Faites défiler et appuyez sur &quot;Sur l&apos;écran d&apos;accueil&quot;</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-blue-600 font-semibold">3.</span>
                    <span>Appuyez sur &quot;Ajouter&quot;</span>
                  </li>
                </ol>
              </div>
            ) : (
              // Avantages PWA
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-sm">
                  <div className="text-green-600">✓</div>
                  <span className="text-gray-700 dark:text-gray-300">
                    Accès direct depuis votre écran d&apos;accueil
                  </span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <div className="text-green-600">✓</div>
                  <span className="text-gray-700 dark:text-gray-300">
                    Fonctionne même hors ligne
                  </span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <div className="text-green-600">✓</div>
                  <span className="text-gray-700 dark:text-gray-300">
                    Notifications en temps réel
                  </span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <div className="text-green-600">✓</div>
                  <span className="text-gray-700 dark:text-gray-300">
                    Expérience plein écran optimisée
                  </span>
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="mt-4 flex gap-2">
              {!isIOS && (
                <Button
                  onClick={handleInstall}
                  variant="primary"
                  className="flex-1"
                  disabled={isInstalling || !deferredPrompt}
                >
                  <Download className="w-4 h-4 mr-2" />
                  {isInstalling ? 'Installation...' : 'Installer'}
                </Button>
              )}
              <Button
                onClick={handleDismiss}
                variant="secondary"
                className={isIOS ? 'w-full' : ''}
              >
                {isIOS ? 'Compris' : 'Plus tard'}
              </Button>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 dark:bg-gray-700/50 px-4 py-3">
            <a 
              href="/about/pwa"
              className="flex items-center justify-between text-sm text-gray-800 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-200 transition-colors"
            >
              <span>En savoir plus sur les PWA</span>
              <ChevronRight className="w-4 h-4" />
            </a>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}