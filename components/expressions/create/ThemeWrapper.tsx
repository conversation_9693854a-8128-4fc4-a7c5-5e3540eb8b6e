'use client';

import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';

interface ThemeWrapperProps {
  children: React.ReactNode;
  className?: string;
}

export const ThemeWrapper: React.FC<ThemeWrapperProps> = ({ children, className = '' }) => {
  const { theme } = useTheme();
  
  // Créer une classe CSS personnalisée basée sur le thème
  const themeClass = `theme-${theme.mode}`;
  
  return (
    <div className={`${themeClass} ${className}`}>
      <style jsx global>{`
        /* Thème Light */
        .theme-light input,
        .theme-light textarea,
        .theme-light select {
          background-color: white;
          color: rgb(17 24 39);
          border-color: rgb(229 231 235);
        }
        
        .theme-light input::placeholder,
        .theme-light textarea::placeholder {
          color: rgb(156 163 175);
        }
        
        /* Thème Dark */
        .theme-dark input,
        .theme-dark textarea,
        .theme-dark select {
          background-color: rgb(17 24 39);
          color: rgb(243 244 246);
          border-color: rgb(55 65 81);
        }
        
        .theme-dark input::placeholder,
        .theme-dark textarea::placeholder {
          color: rgb(156 163 175);
        }
        
        /* Thème Ocean */
        .theme-ocean input,
        .theme-ocean textarea,
        .theme-ocean select {
          background-color: rgba(255, 255, 255, 0.95);
          color: rgb(30 64 175);
          border-color: rgb(191 219 254);
        }
        
        .theme-ocean input::placeholder,
        .theme-ocean textarea::placeholder {
          color: rgb(96 165 250);
        }
        
        /* Thème Sunset */
        .theme-sunset input,
        .theme-sunset textarea,
        .theme-sunset select {
          background-color: rgba(255, 255, 255, 0.98);
          color: rgb(17 24 39);
          border-color: rgb(254 215 170);
        }
        
        .theme-sunset input::placeholder,
        .theme-sunset textarea::placeholder {
          color: rgb(251 146 60);
        }
        
        /* Thème Aurora */
        .theme-aurora input,
        .theme-aurora textarea,
        .theme-aurora select {
          background-color: rgb(15 23 42);
          color: rgb(243 244 246);
          border-color: rgba(34 197 94 / 0.5);
        }
        
        .theme-aurora input::placeholder,
        .theme-aurora textarea::placeholder {
          color: rgb(156 163 175);
        }
        
        /* Focus states pour tous les thèmes */
        input:focus,
        textarea:focus,
        select:focus {
          outline: none;
          ring-width: 2px;
          ring-color: rgb(59 130 246);
        }
      `}</style>
      {children}
    </div>
  );
};