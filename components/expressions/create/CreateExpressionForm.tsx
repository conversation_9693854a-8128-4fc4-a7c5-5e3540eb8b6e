'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ArrowLeft, ArrowRight, Check, X } from 'lucide-react';
import { useCreateExpression } from '@/hooks/useCreateExpression';
import { StepMood } from './steps/StepMood';
import { StepText } from './steps/StepText';
import { StepPersonsPillars } from './steps/StepPersonsPillars';
import { StepMediaLocation } from './steps/StepMediaLocation';
import { StepVisibilityConfirm } from './steps/StepVisibilityConfirm';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/contexts/ThemeContext';
import { ThemeWrapper } from './ThemeWrapper';

const STEP_TITLES = [
  'Humeur',
  'Expression',
  'Contexte',
  'Médias',
  'Confirmer',
];

export const CreateExpressionForm: React.FC = () => {
  const router = useRouter();
  const { user, profile } = useAuth();
  const { theme } = useTheme();
  const {
    // Step management
    currentStep,
    canGoNext,
    goToNextStep,
    goToPreviousStep,
    
    // Form data
    mood,
    text,
    personReferences,
    suggestedPillar,
    media,
    location,
    locationName,
    visibility,
    
    // Form handlers
    setMood,
    setText,
    setPersonReferences,
    setSuggestedPillar,
    setMedia,
    setLocation,
    setLocationName,
    setVisibility,
    
    // Actions
    submitExpression,
    
    // UI state
    isSubmitting,
    showSuccess,
  } = useCreateExpression();

  // Step progress
  const progress = (currentStep / 5) * 100;

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <StepMood
            selectedMood={mood}
            onMoodSelect={setMood}
          />
        );
      case 2:
        return (
          <StepText
            text={text}
            onTextChange={setText}
            selectedMood={mood}
          />
        );
      case 3:
        return (
          <StepPersonsPillars
            personReferences={personReferences}
            onPersonReferencesChange={setPersonReferences}
            suggestedPillar={suggestedPillar}
            onPillarChange={setSuggestedPillar}
          />
        );
      case 4:
        return (
          <StepMediaLocation
            media={media}
            onMediaChange={setMedia}
            location={location}
            onLocationChange={setLocation}
            locationName={locationName}
            onLocationNameChange={setLocationName}
          />
        );
      case 5:
        return (
          <StepVisibilityConfirm
            visibility={visibility}
            onVisibilityChange={setVisibility}
            mood={mood!}
            text={text}
            personReferences={personReferences}
            suggestedPillar={suggestedPillar}
            media={media}
            locationName={locationName}
            userDisplayName={profile?.nickname || user?.email || 'Utilisateur'}
          />
        );
    }
  };

  if (showSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 200, damping: 20 }}
          className="text-center"
        >
          <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Check className="w-12 h-12 text-green-600" />
          </div>
          <h2 className={`text-2xl font-bold ${theme.text.primary} mb-2`}>
            Expression créée !
          </h2>
          <p className={theme.text.secondary}>
            Votre voix a été entendue. Merci de contribuer au changement.
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <ThemeWrapper className={`min-h-screen bg-gradient-to-br ${theme.background.primary}`}>
      <div className="max-w-2xl mx-auto p-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.push('/')}
            className={`flex items-center gap-2 ${theme.text.secondary} hover:opacity-80 transition-opacity mb-4`}
          >
            <X className="w-5 h-5" />
            <span>Annuler</span>
          </button>

          {/* Progress bar */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h1 className={`text-2xl font-bold ${theme.text.primary}`}>
                Nouvelle Expression
              </h1>
              <span className={`text-sm ${theme.text.secondary}`}>
                Étape {currentStep} sur 5
              </span>
            </div>

            <div className="relative">
              <div className={`h-2 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-gray-700' : 'bg-gray-200'} rounded-full overflow-hidden`}>
                <motion.div
                  role="progressbar"
                  aria-valuenow={progress}
                  aria-valuemin={0}
                  aria-valuemax={100}
                  className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
                  initial={{ width: '0%' }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
              
              {/* Step indicators */}
              <div className="absolute -top-1 left-0 right-0 flex justify-between">
                {STEP_TITLES.map((title, index) => {
                  const stepNumber = index + 1;
                  const isCompleted = stepNumber < currentStep;
                  const isCurrent = stepNumber === currentStep;
                  
                  return (
                    <div
                      key={stepNumber}
                      className="flex flex-col items-center"
                      style={{ width: '20%' }}
                    >
                      <motion.div
                        className={`
                          w-4 h-4 rounded-full border-2
                          ${isCompleted 
                            ? 'bg-blue-600 border-blue-600' 
                            : isCurrent 
                              ? 'bg-white border-blue-600' 
                              : theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-gray-700 border-gray-600' : 'bg-gray-200 border-gray-300'
                          }
                        `}
                        animate={isCurrent ? { scale: [1, 1.2, 1] } : {}}
                        transition={{ repeat: Infinity, duration: 2 }}
                      />
                      <span className={`
                        text-xs mt-2 
                        ${isCurrent 
                          ? `${theme.text.primary} font-medium` 
                          : theme.text.secondary
                        }
                      `}>
                        {title}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Step content */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderStep()}
              </motion.div>
            </AnimatePresence>
          </CardContent>
        </Card>

        {/* Navigation buttons */}
        <div className="flex justify-between gap-4">
          <Button
            onClick={goToPreviousStep}
            disabled={currentStep === 1}
            variant="secondary"
            className="flex-1"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Précédent
          </Button>

          {currentStep < 5 ? (
            <Button
              onClick={goToNextStep}
              disabled={!canGoNext()}
              variant="primary"
              className="flex-1"
            >
              Suivant
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={submitExpression}
              disabled={isSubmitting || !canGoNext()}
              variant="primary"
              className="flex-1"
            >
              {isSubmitting ? (
                <>
                  <motion.div
                    className="w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                  />
                  Création...
                </>
              ) : (
                <>
                  Publier
                  <Check className="w-4 h-4 ml-2" />
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </ThemeWrapper>
  );
};