'use client';

import React from 'react';
import { motion } from 'framer-motion';
import type { Mood } from '@/lib/types/pillarscan';
import { useTheme } from '@/contexts/ThemeContext';

export interface MoodOption {
  value: Mood;
  emoji: string;
  label: string;
  color: string;
  gradient: string;
}

const MOODS: MoodOption[] = [
  { 
    value: 'frustrated', 
    emoji: '😤', 
    label: 'Frustré', 
    color: '#ef4444',
    gradient: 'from-red-400 to-red-600'
  },
  { 
    value: 'happy', 
    emoji: '😊', 
    label: 'Heureux', 
    color: '#10b981',
    gradient: 'from-green-400 to-green-600'
  },
  { 
    value: 'idea', 
    emoji: '💡', 
    label: 'Idée', 
    color: '#f59e0b',
    gradient: 'from-yellow-400 to-yellow-600'
  },
  { 
    value: 'question', 
    emoji: '❓', 
    label: 'Question', 
    color: '#3b82f6',
    gradient: 'from-blue-400 to-blue-600'
  },
];

interface StepMoodProps {
  selectedMood: Mood | null;
  onMoodSelect: (mood: Mood) => void;
}

export const StepMood: React.FC<StepMoodProps> = ({ selectedMood, onMoodSelect }) => {
  const { theme } = useTheme();
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className={`text-2xl font-bold ${theme.text.primary}`}>
          Comment vous sentez-vous ?
        </h2>
        <p className={`mt-2 ${theme.text.secondary}`}>
          Choisissez l&apos;humeur qui correspond le mieux à votre expression
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {MOODS.map((mood) => (
          <motion.button
            key={mood.value}
            onClick={() => onMoodSelect(mood.value)}
            className={`
              relative overflow-hidden rounded-2xl p-6 
              transition-all duration-300 transform
              ${selectedMood === mood.value 
                ? `ring-4 ring-offset-2 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'ring-offset-gray-900' : 'ring-offset-white'}` 
                : 'hover:scale-105'
              }
            `}
            style={{
              boxShadow: selectedMood === mood.value 
                ? `0 10px 30px -10px ${mood.color}` 
                : undefined
            }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className={`
              absolute inset-0 bg-gradient-to-br ${mood.gradient} 
              ${selectedMood === mood.value ? 'opacity-100' : 'opacity-80'}
            `} />
            
            <div className="relative z-10 text-white">
              <motion.div 
                className="text-5xl mb-2"
                animate={selectedMood === mood.value ? {
                  scale: [1, 1.2, 1],
                  rotate: [0, 10, -10, 0]
                } : {}}
                transition={{ duration: 0.5 }}
              >
                {mood.emoji}
              </motion.div>
              <div className="font-semibold text-lg">{mood.label}</div>
            </div>

            {selectedMood === mood.value && (
              <motion.div
                className="absolute top-2 right-2"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              >
                <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </motion.div>
            )}
          </motion.button>
        ))}
      </div>
    </div>
  );
};