'use client';

import React, { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import type { Mood } from '@/lib/types/pillarscan';
import { useTheme } from '@/contexts/ThemeContext';

interface StepTextProps {
  text: string;
  onTextChange: (text: string) => void;
  selectedMood: Mood | null;
  maxLength?: number;
}

const getMoodEmoji = (mood: Mood | null): string => {
  switch (mood) {
    case 'frustrated': return '😤';
    case 'happy': return '😊';
    case 'idea': return '💡';
    case 'question': return '❓';
    default: return '🙂';
  }
};

const getMoodColor = (mood: Mood | null): string => {
  switch (mood) {
    case 'frustrated': return 'text-red-500';
    case 'happy': return 'text-green-500';
    case 'idea': return 'text-yellow-500';
    case 'question': return 'text-blue-500';
    default: return 'text-gray-700';
  }
};

export const StepText: React.FC<StepTextProps> = ({ 
  text, 
  onTextChange, 
  selectedMood,
  maxLength = 280 
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { theme } = useTheme();

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [text]);

  // Focus on mount
  useEffect(() => {
    textareaRef.current?.focus();
  }, []);

  const remainingChars = maxLength - text.length;
  const isNearLimit = remainingChars <= 20;
  const isOverLimit = remainingChars < 0;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <motion.div 
          className="inline-flex items-center gap-2 mb-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <span className={`text-3xl ${getMoodColor(selectedMood)}`}>
            {getMoodEmoji(selectedMood)}
          </span>
          <h2 className={`text-2xl font-bold ${theme.text.primary}`}>
            Exprimez-vous
          </h2>
        </motion.div>
        <p className={theme.text.secondary}>
          Partagez votre pensée, votre idée ou votre question
        </p>
      </div>

      <div className="relative">
        <textarea
          ref={textareaRef}
          value={text}
          onChange={(e) => onTextChange(e.target.value)}
          placeholder="Que voulez-vous exprimer ?"
          className={`
            w-full min-h-[120px] p-4 text-lg
            ${theme.ui.card} 
            ${theme.text.primary}
            border-2 rounded-xl resize-none
            transition-all duration-200
            placeholder:opacity-60
            focus:outline-none focus:ring-2 focus:ring-blue-500
            ${isOverLimit 
              ? 'border-red-500 focus:ring-red-500' 
              : `${theme.ui.border} hover:opacity-80`
            }
          `}
          maxLength={maxLength + 50} // Allow some overflow for better UX
        />

        {/* Character counter */}
        <motion.div 
          className={`
            absolute bottom-2 right-2 text-sm font-medium
            ${isOverLimit ? 'text-red-600' : isNearLimit ? 'text-yellow-600' : theme.text.secondary}
          `}
          animate={isNearLimit ? { scale: [1, 1.1, 1] } : {}}
          transition={{ duration: 0.3 }}
        >
          {remainingChars}
        </motion.div>

        {/* Minimum characters hint */}
        {text.length > 0 && text.length < 10 && (
          <motion.div 
            className="absolute bottom-2 left-2 text-sm text-yellow-600"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            Minimum 10 caractères ({10 - text.length} restants)
          </motion.div>
        )}

        {/* Writing tips */}
        {text.length === 0 && (
          <motion.div 
            className={`mt-4 p-4 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-blue-900/20' : 'bg-blue-50'} rounded-lg`}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <p className={`text-sm ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-blue-300' : 'text-blue-800'}`}>
              💡 <strong>Conseil :</strong> Soyez précis et authentique. 
              Les meilleures expressions sont celles qui viennent du cœur.
            </p>
          </motion.div>
        )}

        {/* Dynamic feedback based on mood */}
        {text.length > 50 && (
          <motion.div 
            className={`mt-4 flex items-center gap-2 text-sm ${theme.text.secondary}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            {selectedMood === 'frustrated' && (
              <>
                <span>💪</span>
                <span>Votre voix compte. Exprimez ce qui doit changer.</span>
              </>
            )}
            {selectedMood === 'happy' && (
              <>
                <span>🌟</span>
                <span>Partagez votre joie, elle est contagieuse !</span>
              </>
            )}
            {selectedMood === 'idea' && (
              <>
                <span>🚀</span>
                <span>Les grandes innovations commencent par une simple idée.</span>
              </>
            )}
            {selectedMood === 'question' && (
              <>
                <span>🤝</span>
                <span>Poser des questions, c&apos;est déjà commencer à trouver des solutions.</span>
              </>
            )}
          </motion.div>
        )}
      </div>
    </div>
  );
};