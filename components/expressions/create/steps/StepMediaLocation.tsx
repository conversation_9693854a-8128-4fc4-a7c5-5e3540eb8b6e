'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { MapPin, Navigation, AlertCircle } from 'lucide-react';
import { useGeolocation } from '@/hooks/useGeolocation';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/contexts/ThemeContext';
import { SimpleImageUpload } from '@/components/upload/SimpleImageUpload';

interface StepMediaLocationProps {
  media: File[];
  onMediaChange: (files: File[]) => void;
  location: { latitude: number; longitude: number } | null;
  onLocationChange: (location: { latitude: number; longitude: number } | null) => void;
  locationName: string;
  onLocationNameChange: (name: string) => void;
}

export const StepMediaLocation: React.FC<StepMediaLocationProps> = ({
  media,
  onMediaChange,
  location,
  onLocationChange,
  locationName,
  onLocationNameChange,
}) => {
  const { getPosition, loading: geoLoading, error: geoError } = useGeolocation();
  const [showLocationOptions, setShowLocationOptions] = useState(false);
  const [manualLocationInput, setManualLocationInput] = useState('');
  const { theme } = useTheme();

  const handleGetLocation = async () => {
    try {
      const position = await getPosition();
      if (position) {
        onLocationChange({
          latitude: position.latitude,
          longitude: position.longitude,
        });
        // En production, vous pourriez faire un reverse geocoding ici
        onLocationNameChange('Ma position actuelle');
      }
    } catch (error) {
      console.error('Erreur géolocalisation:', error);
    }
  };

  const handleManualLocation = () => {
    if (manualLocationInput.trim()) {
      onLocationNameChange(manualLocationInput.trim());
      setManualLocationInput('');
      setShowLocationOptions(false);
    }
  };

  const removeLocation = () => {
    onLocationChange(null);
    onLocationNameChange('');
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className={`text-2xl font-bold ${theme.text.primary} mb-2`}>
          Médias et Localisation
        </h2>
        <p className={theme.text.secondary}>
          Ajoutez des images et précisez le lieu (optionnel)
        </p>
      </div>

      {/* Media Upload Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <span className="text-lg">📸</span>
          <h3 className={`text-lg font-semibold ${theme.text.primary}`}>
            Images
          </h3>
          <span className={`text-sm ${theme.text.secondary}`}>
            ({media.length}/4)
          </span>
        </div>

        {/* Image Upload Component */}
        <SimpleImageUpload
          files={media}
          onFilesChange={onMediaChange}
          maxFiles={4}
          maxSize={5}
        />

        {media.length === 0 && (
          <motion.div 
            className={`p-4 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-gray-800' : 'bg-gray-50'} rounded-lg text-sm ${theme.text.secondary}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            💡 Une image vaut mille mots. Ajoutez des photos pour illustrer votre expression.
          </motion.div>
        )}
      </div>

      {/* Location Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <span className="text-lg">📍</span>
          <h3 className={`text-lg font-semibold ${theme.text.primary}`}>
            Localisation
          </h3>
        </div>

        {/* Current location display */}
        {locationName && (
          <motion.div 
            className={`p-4 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-blue-900/20' : 'bg-blue-50'} rounded-lg`}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <MapPin className={`w-5 h-5 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-blue-400' : 'text-blue-600'} mt-0.5`} />
                <div>
                  <p className={`font-medium ${theme.text.primary}`}>
                    {locationName}
                  </p>
                  {location && (
                    <p className={`text-sm ${theme.text.secondary} mt-1`}>
                      {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
                    </p>
                  )}
                </div>
              </div>
              <button
                onClick={removeLocation}
                className={`text-sm ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-red-400 hover:text-red-300' : 'text-red-600 hover:text-red-700'}`}
              >
                Retirer
              </button>
            </div>
          </motion.div>
        )}

        {/* Location options */}
        {!locationName && (
          <div className="space-y-3">
            {/* Auto geolocation */}
            <Button
              onClick={handleGetLocation}
              disabled={geoLoading}
              variant="secondary"
              className="w-full justify-center"
            >
              <Navigation className="w-4 h-4 mr-2" />
              {geoLoading ? 'Localisation en cours...' : 'Utiliser ma position actuelle'}
            </Button>

            {geoError && (
              <motion.div 
                className={`p-3 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-red-900/20' : 'bg-red-50'} rounded-lg flex items-start gap-2`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <AlertCircle className={`w-5 h-5 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-red-400' : 'text-red-600'} flex-shrink-0 mt-0.5`} />
                <p className={`text-sm ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-red-300' : 'text-red-800'}`}>
                  Impossible d&apos;accéder à votre position. Vérifiez vos paramètres.
                </p>
              </motion.div>
            )}

            {/* Manual location */}
            <button
              onClick={() => setShowLocationOptions(!showLocationOptions)}
              className={`w-full text-center text-sm ${theme.text.secondary} hover:opacity-80`}
            >
              Ou saisir manuellement un lieu
            </button>

            {showLocationOptions && (
              <motion.div 
                className="space-y-3"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
              >
                <input
                  type="text"
                  value={manualLocationInput}
                  onChange={(e) => setManualLocationInput(e.target.value)}
                  placeholder="Ex: Paris, Tour Eiffel"
                  className={`w-full px-4 py-2 border ${theme.ui.border} rounded-lg ${theme.ui.card} ${theme.text.primary} focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  onKeyPress={(e) => e.key === 'Enter' && handleManualLocation()}
                />
                <Button
                  onClick={handleManualLocation}
                  disabled={!manualLocationInput.trim()}
                  variant="primary"
                  className="w-full"
                >
                  Confirmer le lieu
                </Button>
              </motion.div>
            )}
          </div>
        )}

        {/* Privacy note */}
        <motion.div 
          className={`p-4 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-amber-900/20' : 'bg-amber-50'} rounded-lg text-sm`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <p className={`${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-amber-300' : 'text-amber-800'}`}>
            🔒 <strong>Confidentialité :</strong> La localisation est optionnelle. 
            Si vous la partagez, elle aide à contextualiser votre expression mais reste approximative pour protéger votre vie privée.
          </p>
        </motion.div>
      </div>
    </div>
  );
};