'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { PersonMention } from '@/components/pillarscan/expression/PersonMention';
import { PILLARS } from '@/lib/types/pillarscan';
import type { PersonReference } from '@/types/person';
import { ChevronDown } from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';

interface StepPersonsPillarsProps {
  personReferences: PersonReference[];
  onPersonReferencesChange: (refs: PersonReference[]) => void;
  suggestedPillar: number;
  onPillarChange: (pillar: number) => void;
}

export const StepPersonsPillars: React.FC<StepPersonsPillarsProps> = ({
  personReferences,
  onPersonReferencesChange,
  suggestedPillar,
  onPillarChange,
}) => {
  const [showPillarDetails, setShowPillarDetails] = useState(false);
  const { theme } = useTheme();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className={`text-2xl font-bold ${theme.text.primary} mb-2`}>
          Contexte et Classification
        </h2>
        <p className={theme.text.secondary}>
          Associez des personnes et choisissez le pilier le plus pertinent
        </p>
      </div>

      {/* Person References Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <span className="text-lg">👥</span>
          <h3 className={`text-lg font-semibold ${theme.text.primary}`}>
            Personnes mentionnées
          </h3>
          <span className={`text-sm ${theme.text.secondary}`}>
            ({personReferences.length}/5)
          </span>
        </div>

        <PersonMention
          value={personReferences}
          onChange={onPersonReferencesChange}
          maxPersons={5}
        />

        {personReferences.length === 0 && (
          <motion.div 
            className={`p-4 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-gray-800' : 'bg-gray-50'} rounded-lg text-sm ${theme.text.secondary}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            💡 Vous pouvez mentionner des personnes, entreprises ou groupes concernés par votre expression.
          </motion.div>
        )}
      </div>

      {/* Pillar Selection Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <span className="text-lg">🏛️</span>
          <h3 className={`text-lg font-semibold ${theme.text.primary}`}>
            Pilier thématique
          </h3>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {PILLARS.map((pillar) => (
            <motion.button
              key={pillar.id}
              onClick={() => onPillarChange(pillar.id)}
              className={`
                relative p-4 rounded-xl border-2 transition-all
                ${suggestedPillar === pillar.id
                  ? `border-blue-500 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-blue-900/20' : 'bg-blue-50'}`
                  : `${theme.ui.border} hover:opacity-80`
                }
              `}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-start gap-3">
                <span className="text-2xl">{pillar.emoji}</span>
                <div className="flex-1 text-left">
                  <div className={`font-medium ${theme.text.primary}`}>
                    {pillar.name_fr}
                  </div>
                  {suggestedPillar === pillar.id && (
                    <motion.div 
                      className={`text-xs ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-blue-400' : 'text-blue-600'} mt-1`}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                    >
                      ✓ Sélectionné
                    </motion.div>
                  )}
                </div>
              </div>
            </motion.button>
          ))}
        </div>

        {/* Pillar explanation toggle */}
        <button
          onClick={() => setShowPillarDetails(!showPillarDetails)}
          className={`flex items-center gap-2 text-sm ${theme.text.secondary} hover:opacity-80 transition-opacity`}
        >
          <ChevronDown 
            className={`w-4 h-4 transition-transform ${showPillarDetails ? 'rotate-180' : ''}`} 
          />
          Qu&apos;est-ce que les piliers ?
        </button>

        {showPillarDetails && (
          <motion.div 
            className={`p-4 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-amber-900/20' : 'bg-amber-50'} rounded-lg text-sm`}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <p className={`${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-amber-300' : 'text-amber-800'} mb-2`}>
              Les <strong>7 piliers</strong> représentent les domaines fondamentaux de notre société :
            </p>
            <ul className={`space-y-1 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-amber-400' : 'text-amber-700'}`}>
              {PILLARS.map((pillar) => (
                <li key={pillar.id} className="flex items-center gap-2">
                  <span>{pillar.emoji}</span>
                  <span>{pillar.name_fr}</span>
                </li>
              ))}
            </ul>
            <p className={`mt-3 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-amber-300' : 'text-amber-800'}`}>
              Choisir le bon pilier aide à classer votre expression et la rendre visible aux bonnes personnes.
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
};