'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { PILLARS } from '@/lib/types/pillarscan';
import { Eye, EyeOff, Globe, Users, Lock, AlertCircle } from 'lucide-react';
import type { Mood } from '@/lib/types/pillarscan';
import type { PersonReference } from '@/types/person';
import { useTheme } from '@/contexts/ThemeContext';

interface StepVisibilityConfirmProps {
  visibility: 'public' | 'anonymous' | 'private';
  onVisibilityChange: (visibility: 'public' | 'anonymous' | 'private') => void;
  // Preview data
  mood: Mood;
  text: string;
  personReferences: PersonReference[];
  suggestedPillar: number;
  media: File[];
  locationName: string;
  userDisplayName?: string;
  userAvatarUrl?: string;
}

const VISIBILITY_OPTIONS = [
  {
    value: 'public' as const,
    icon: Globe,
    label: 'Public',
    description: 'Visible par tous avec votre nom',
    color: 'text-green-600',
    bgColor: 'bg-green-50',
  },
  {
    value: 'anonymous' as const,
    icon: EyeOff,
    label: 'Anonyme',
    description: 'Visible par tous sans votre nom',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50',
  },
  {
    value: 'private' as const,
    icon: Lock,
    label: 'Privé',
    description: 'Visible uniquement par vous',
    color: 'text-red-600',
    bgColor: 'bg-red-50',
  },
];

const getMoodEmoji = (mood: Mood): string => {
  switch (mood) {
    case 'frustrated': return '😤';
    case 'happy': return '😊';
    case 'idea': return '💡';
    case 'question': return '❓';
    default: return '🙂';
  }
};

export const StepVisibilityConfirm: React.FC<StepVisibilityConfirmProps> = ({
  visibility,
  onVisibilityChange,
  mood,
  text,
  personReferences,
  suggestedPillar,
  media,
  locationName,
  userDisplayName = 'Utilisateur',
}) => {
  const selectedPillar = PILLARS.find(p => p.id === suggestedPillar);
  const { theme } = useTheme();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className={`text-2xl font-bold ${theme.text.primary} mb-2`}>
          Visibilité et Confirmation
        </h2>
        <p className={theme.text.secondary}>
          Choisissez qui peut voir votre expression
        </p>
      </div>

      {/* Visibility Options */}
      <div className="space-y-3">
        {VISIBILITY_OPTIONS.map((option) => (
          <motion.button
            key={option.value}
            onClick={() => onVisibilityChange(option.value)}
            className={`
              w-full p-4 rounded-xl border-2 transition-all
              ${visibility === option.value
                ? `border-blue-500 ${theme.mode === 'dark' || theme.mode === 'aurora' ? option.bgColor.replace('bg-', 'bg-').replace('-50', '-900/20') : option.bgColor}`
                : `${theme.ui.border} hover:opacity-80`
              }
            `}
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <option.icon className={`w-5 h-5 ${option.color}`} />
                <div className="text-left">
                  <div className={`font-medium ${theme.text.primary}`}>
                    {option.label}
                  </div>
                  <div className={`text-sm ${theme.text.secondary}`}>
                    {option.description}
                  </div>
                </div>
              </div>
              {visibility === option.value && (
                <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
          </motion.button>
        ))}
      </div>

      {/* Preview Section */}
      <div className="space-y-4">
        <h3 className={`text-lg font-semibold ${theme.text.primary} flex items-center gap-2`}>
          <Eye className="w-5 h-5" />
          Aperçu de votre expression
        </h3>

        <Card className="overflow-hidden">
          <div className="p-6">
            {/* Author info */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                {visibility !== 'anonymous' ? (
                  <>
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-bold">
                      {userDisplayName.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <p className={`font-medium ${theme.text.primary}`}>
                        {userDisplayName}
                      </p>
                      <p className={`text-sm ${theme.text.secondary}`}>À l&apos;instant</p>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="w-10 h-10 rounded-full bg-gray-400 flex items-center justify-center text-white">
                      <Users className="w-5 h-5" />
                    </div>
                    <div>
                      <p className={`font-medium ${theme.text.primary}`}>
                        Anonyme
                      </p>
                      <p className={`text-sm ${theme.text.secondary}`}>À l&apos;instant</p>
                    </div>
                  </>
                )}
              </div>
              <span className="text-2xl">{getMoodEmoji(mood)}</span>
            </div>

            {/* Expression text */}
            <p className={`${theme.text.primary} mb-4 whitespace-pre-wrap`}>
              {text}
            </p>

            {/* Metadata */}
            <div className="flex flex-wrap gap-2 mb-4">
              {selectedPillar && (
                <Badge variant="secondary">
                  {selectedPillar.emoji} {selectedPillar.name_fr}
                </Badge>
              )}
              {locationName && (
                <Badge variant="secondary">
                  📍 {locationName}
                </Badge>
              )}
              {media.length > 0 && (
                <Badge variant="secondary">
                  📸 {media.length} image{media.length > 1 ? 's' : ''}
                </Badge>
              )}
            </div>

            {/* Person references */}
            {personReferences.length > 0 && (
              <div className={`pt-4 border-t ${theme.ui.border}`}>
                <p className={`text-sm ${theme.text.secondary} mb-2`}>
                  Personnes mentionnées :
                </p>
                <div className="flex flex-wrap gap-2">
                  {personReferences.map((ref, index) => (
                    <Badge key={index} variant="secondary">
                      {ref.person_name} ({ref.role})
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Privacy warning for private */}
        {visibility === 'private' && (
          <motion.div 
            className={`p-4 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'bg-amber-900/20' : 'bg-amber-50'} rounded-lg flex items-start gap-3`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <AlertCircle className={`w-5 h-5 ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-amber-400' : 'text-amber-600'} flex-shrink-0 mt-0.5`} />
            <div className={`text-sm ${theme.mode === 'dark' || theme.mode === 'aurora' ? 'text-amber-300' : 'text-amber-800'}`}>
              <p className="font-medium mb-1">Expression privée</p>
              <p>
                Cette expression sera uniquement visible par vous. 
                Elle ne sera pas partagée avec la communauté et ne pourra pas recevoir de réactions.
              </p>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};