# Add lockfile and package.json's
FROM node:22-alpine AS installer
USER root
WORKDIR /app

ENV NODE_ENV=build
ENV NEXT_BUILD_OUTPUT=standalone

# Install python3
RUN apk add --no-cache python3 pkgconfig build-base
RUN apk add --no-cache pixman-dev cairo-dev pango-dev jpeg-dev giflib-dev

# First install the dependencies (as they change less often)
COPY package.json package-lock.json ./
COPY .gitignore .gitignore

# Install deps
RUN npm install --frozen-lockfile

# Build the project
COPY . .
# Build the apps
RUN npm run build
# Prune dev deps
RUN npm prune --omit=dev

#--
# Build
FROM node:22-alpine

USER node

WORKDIR /app

ENV NODE_ENV=production

COPY --from=installer --chown=node:node /app/.next/standalone ./
COPY --from=installer --chown=node:node /app/.next/static ./.next/static
COPY --from=installer --chown=node:node /app/public ./public

VOLUME /app/.next/cache

ENV PORT=3000

EXPOSE 3000

CMD ["node", "server.js"]