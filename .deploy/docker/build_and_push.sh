#!/bin/bash
set -e

# This script builds and pushes Docker images based on CI/CD variables
# It only tags as 'latest' if the tag is not a prerelease or dev version

echo "Building and pushing Docker images..."

# Check if required variables are set
if [ -z "$CI_REGISTRY_IMAGE" ]; then
    echo "Error: CI_REGISTRY_IMAGE is not set"
    exit 1
fi

if [ -z "$CI_COMMIT_TAG" ]; then
    echo "Error: CI_COMMIT_TAG is not set"
    exit 1
fi

# Try to pull the latest image to use as cache
echo "Pulling latest image for cache..."
docker pull $CI_REGISTRY_IMAGE:latest || echo "No latest image found for cache, building from scratch"

# Build the Docker image with the tag, using cache from the latest image
echo "Building image: $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG"
docker build \
  --cache-from $CI_REGISTRY_IMAGE:latest \
  --build-arg BUILDKIT_INLINE_CACHE=1 \
  --file .deploy/docker/Dockerfile \
  --tag $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG \
  .

# Push the tagged image
echo "Pushing image: $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG"
docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG

# Check if the tag is a prerelease or dev version
# This checks for common prerelease patterns like:
# - alpha, beta, rc, dev, test
# - v1.0.0-alpha, v1.0.0-beta.1, etc.
if [[ "$CI_COMMIT_TAG" =~ ^v?[0-9]+\.[0-9]+\.[0-9]+(-alpha|-beta|-rc|-dev|-test|\.dev|\.alpha|\.beta|\.rc|\.test|dev|alpha|beta|rc|test) ]]; then
    echo "Tag $CI_COMMIT_TAG is a prerelease version. Not tagging as 'latest'."
else
    echo "Tag $CI_COMMIT_TAG is a release version. Tagging as 'latest'."
    docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG $CI_REGISTRY_IMAGE:latest
    docker push $CI_REGISTRY_IMAGE:latest
fi

echo "Docker build and push completed successfully."