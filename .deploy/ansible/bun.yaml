---
- name: Ensure curl is installed
  apt:
    name: curl
    state: present
  become: true
  when: ansible_os_family == "Debian"

- name: Ensure curl is installed (RedHat family)
  dnf:
    name: curl
    state: present
  become: true
  when: ansible_os_family == "RedHat"

- name: Create temporary directory for Bun installation
  file:
    path: /tmp/bun-install
    state: directory
    mode: "0755"

- name: Download Bun installation script
  get_url:
    url: https://bun.sh/install
    dest: /tmp/bun-install/install.sh
    mode: "0755"

- name: Run Bun installation script
  shell:
    cmd: /tmp/bun-install/install.sh
  environment:
    BUN_INSTALL: "/usr/local"
  become: true

- name: Add Bun to system-wide PATH
  copy:
    dest: /etc/profile.d/bun.sh
    content: 'export PATH="/usr/local/bin:$PATH"'
    mode: "0644"
  become: true

- name: Clean up installation files
  file:
    path: /tmp/bun-install
    state: absent

- name: Verify Bun installation
  command: bun --version
  register: bun_version
  changed_when: false

- name: Display Bun version
  debug:
    var: bun_version.stdout
