- name: Install the gpg key for nodejs LTS
  become: true
  apt_key:
    url: "https://deb.nodesource.com/gpgkey/nodesource.gpg.key"
    state: present

- name: Install the nodejs {{ nodejs_version }} LTS repos
  become: true
  apt_repository:
    repo: "deb https://deb.nodesource.com/node_{{ nodejs_version }}.x {{ ansible_distribution_release }} main"
    state: present
    update_cache: yes

- name: Install the nodejs {{ nodejs_version }} LTS
  become: true
  package:
    update_cache: yes
    name:
      - nodejs
    state: latest

- name: NPM | Install PNPM, YARN, PM2
  become: true
  npm:
    name: "{{ item }}"
    global: yes
    state: latest
  loop:
    - pnpm
    - yarn
    - pm2
