---
# Check if .auth-secret file exists
- name: Check if auth secret file exists
  stat:
    path: "{{ project_dir }}/.auth-secret"
  register: auth_secret_file

# Generate new secret if file doesn't exist
- name: Generate OpenSSL random secret
  shell: "openssl rand -base64 32"
  register: new_secret
  when: not auth_secret_file.stat.exists

# Create .auth-secret file with the new secret if it doesn't exist
- name: Create auth secret file
  copy:
    content: "{{ new_secret.stdout }}"
    dest: "{{ project_dir }}/.auth-secret"
  when: not auth_secret_file.stat.exists

# Read existing secret from file
- name: Read auth secret
  slurp:
    src: "{{ project_dir }}/.auth-secret"
  register: auth_secret_raw

# Set the secret as a fact for later use
- name: Set auth secret fact
  set_fact:
    auth_secret: "{{ auth_secret_raw.content | b64decode | trim }}"

# Example of using the secret later in the playbook
- name: Debug secret (remove in production)
  debug:
    msg: "Secret is available as {{ auth_secret }}"
  when: debug_mode is defined and debug_mode | bool
