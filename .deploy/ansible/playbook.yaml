---
- name: Deploy
  hosts: all
  become: false
  vars:
    project_name: "pillar-scan-nextjs"
    project_dir: "/home/<USER>/{{ project_name }}"
    app_port: 3011

  tasks:
    - name: Install Dkr dependencies
      become: true
      package:
        name: "{{ item }}"
        state: present
      loop:
        - git
        - curl
        - ca-certificates
        - unzip

    - name: Clone Git repository
      git:
        repo: "{{ repo_url }}"
        dest: "{{ project_dir }}"
        clone: yes
        update: yes
        force: true
        version: main

    - include_tasks: port_check.yaml
    - include_tasks: auth-secret.yaml

    - include_tasks: nodejs.yaml
      vars:
        nodejs_version: 20

    - name: Template file .env.j2
      ansible.builtin.template:
        src: .env.j2
        dest: "{{ project_dir }}/.env.local"
        force: true

    - name: Install dependencies
      command: "pnpm install"
      args:
        chdir: "{{ project_dir }}"

    - name: Build Next.js application
      command: "pnpm run build"
      args:
        chdir: "{{ project_dir }}"

    - name: Delete old pm2 process
      command: "pm2 delete {{ project_name }}"
      args:
        chdir: "{{ project_dir }}"
      ignore_errors: true

    - name: Start the server
      command: "pm2 start npm --name {{ project_name }} -- run start"
      args:
        chdir: "{{ project_dir }}"
      environment:
        PORT: "{{ app_port }}"

    - name: Save PM2 process list
      command: "pm2 save"
      changed_when: false
      environment:
        PORT: "{{ app_port }}"

    - name: Generate PM2 startup script
      become: true
      command: "pm2 startup systemd -u {{ ansible_user }} --hp /home/<USER>"
      args:
        creates: "/etc/systemd/system/pm2-{{ ansible_user }}.service"
      ignore_errors: true
      environment:
        PORT: "{{ app_port }}"
