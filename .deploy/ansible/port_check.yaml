---
- name: Check if port is available
  block:
    - set_fact:
        directory_dest: "{{ project_name }}/ansible"

    - name: Check if port verification notice file exists
      stat:
        path: "{{ directory_dest }}/port_{{ app_port }}.info"
      register: port_verification_file

    - name: Check port availability
      wait_for:
        port: "{{ app_port }}"
        state: stopped
        timeout: 1
      register: port_check_result
      when: not port_verification_file.stat.exists

    - name: Create port verification notice file
      copy:
        content: "Port {{ app_port }} was verified available on {{ ansible_date_time.iso8601 }}\n"
        dest: "{{ directory_dest }}/port_{{ app_port }}.info"
        mode: "0644"
      when: not port_verification_file.stat.exists and port_check_result.state is defined

  rescue:
    - name: Fail when port is unavailable on first check
      fail:
        msg: "Port {{ app_port }} is not available. Another service is using it."
      when: not port_verification_file.stat.exists
