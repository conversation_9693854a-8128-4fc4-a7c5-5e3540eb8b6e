NEXTAUTH_URL="{{ lookup('ansible.builtin.env', 'NEXTAUTH_URL') }}"
NEXTAUTH_SECRET="{{ auth_secret }}"

NEXT_PUBLIC_VERCEL_URL="{{ lookup('ansible.builtin.env', 'NEXTAUTH_URL') }}"

SMATFLOW_OAUTH_ISSUER="{{ lookup('ansible.builtin.env', 'SMATFLOW_OAUTH_ISSUER') }}"
SMATFLOW_OAUTH_CLIENT_ID="{{ lookup('ansible.builtin.env', 'SMATFLOW_OAUTH_CLIENT_ID') }}"
SMATFLOW_OAUTH_CLIENT_SECRET="{{ lookup('ansible.builtin.env', 'SMATFLOW_OAUTH_CLIENT_SECRET') }}"
