/**
 * Page d'accueil PillarScan
 * Design moderne avec illustrations et animations
 */

'use client';

import { useAuth } from '@/contexts/AuthContext';
import { ExpressionFeed } from '@/components/feed/ExpressionFeed';
import { StreakDisplay, StreakAlert } from '@/components/gamification/StreakDisplay';
import { BadgeNotification } from '@/components/gamification/BadgeDisplay';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { HeroIllustration } from '@/components/illustrations/EmptyState';
import Link from 'next/link';
import { motion } from 'framer-motion';

export default function HomePage() {
  const { isAuthenticated } = useAuth();

  return (
    <div className="flex-1 bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Alertes et notifications */}
      <StreakAlert />
      <BadgeNotification />

      {/* En-tête hero moderne */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600">
        {/* Pattern de fond */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="relative max-w-6xl mx-auto px-4 py-16">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            {/* Contenu texte */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="text-white"
            >
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Exprimez vos
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-pink-300">
                  besoins citoyens
                </span>
              </h1>
              <p className="text-xl mb-8 text-white/90">
                Rejoignez une communauté engagée qui façonne l&apos;avenir en partageant ses préoccupations et ses idées.
              </p>
              
              {!isAuthenticated ? (
                <div className="flex flex-wrap gap-4">
                  <Link href="/auth/register">
                    <Button size="lg" variant="secondary" className="bg-white text-purple-600 hover:bg-gray-100">
                      Commencer gratuitement
                    </Button>
                  </Link>
                  <Link href="/auth/login">
                    <Button size="lg" variant="ghost" className="text-white border-white/30 hover:bg-white/10">
                      Se connecter
                    </Button>
                  </Link>
                </div>
              ) : (
                <Link href="/create">
                  <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
                    <span className="mr-2">✨</span>
                    Nouvelle expression
                  </Button>
                </Link>
              )}
            </motion.div>

            {/* Illustration */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.2 }}
              className="hidden md:block"
            >
              <HeroIllustration />
            </motion.div>
          </div>
        </div>

        {/* Vague en bas */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0V120Z" fill="white"/>
          </svg>
        </div>
      </div>

      {/* Section statistiques */}
      <div className="max-w-6xl mx-auto px-4 -mt-10 relative z-10">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { label: 'Expressions', value: '10K+', emoji: '💬' },
            { label: 'Citoyens actifs', value: '2.5K+', emoji: '👥' },
            { label: 'Villes connectées', value: '150+', emoji: '🏙️' },
            { label: 'Impact positif', value: '95%', emoji: '✨' },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 + index * 0.1 }}
            >
              <Card variant="elevated" className="text-center">
                <div className="text-3xl mb-2">{stat.emoji}</div>
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <div className="text-sm text-gray-800">{stat.label}</div>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Contenu principal */}
      <main className="max-w-4xl mx-auto px-4 py-12">
        {/* Streak pour les utilisateurs connectés */}
        {isAuthenticated && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="mb-8"
          >
            <div className="flex flex-col items-center space-y-4">
              <StreakDisplay variant="full" />
              <Link href="/profile" className="text-purple-600 hover:text-purple-700 text-sm font-medium">
                Voir mon profil complet →
              </Link>
            </div>
          </motion.div>
        )}

        {/* Feed des expressions */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Expressions récentes de votre communauté
          </h2>
          <ExpressionFeed />
        </motion.div>
      </main>

      {/* CTA Section */}
      {!isAuthenticated && (
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Prêt à faire entendre votre voix ?
              </h2>
              <p className="text-xl text-gray-800 mb-8">
                Rejoignez des milliers de citoyens qui construisent un meilleur avenir ensemble.
              </p>
              <Link href="/auth/register">
                <Button size="xl" className="shadow-xl">
                  Créer mon compte gratuitement
                  <span className="ml-2">→</span>
                </Button>
              </Link>
            </motion.div>
          </div>
        </section>
      )}

      {/* Bouton flottant pour mobile */}
      {isAuthenticated && (
        <Link 
          href="/create"
          className="fixed bottom-6 right-6 w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-2xl flex items-center justify-center hover:scale-110 transition-all lg:hidden group"
        >
          <svg className="w-8 h-8 group-hover:rotate-90 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        </Link>
      )}
    </div>
  );
}