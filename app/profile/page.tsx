'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useStreak } from '@/hooks/useStreak';
import { useBadges } from '@/hooks/useBadges';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { PillarScanAvatar } from '@/components/ui/Avatar';
import { StreakDisplay } from '@/components/gamification/StreakDisplay';
import { BadgeItem, LevelDisplay } from '@/components/gamification/BadgeDisplay';
import { ImpactScore } from '@/components/gamification/ImpactScore';
import { BADGES } from '@/lib/types/pillarscan';

export default function ProfilePage() {
  const router = useRouter();
  const { isAuthenticated, profile } = useAuth();
  const streak = useStreak();
  // const { nextMilestone } = useStreakMilestones(streak.currentStreak); // Pour une utilisation future
  const badges = useBadges();

  // Rediriger si non authentifié
  React.useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, router]);

  if (!isAuthenticated || !profile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Calcul d'un score d'impact fictif basé sur les données disponibles
  const impactScore = Math.min(100, 
    streak.totalExpressions * 5 + 
    streak.currentStreak * 2 + 
    badges.unlockedBadges.length * 10
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 py-8">
      <div className="max-w-4xl mx-auto px-4 space-y-6">
        {/* En-tête du profil */}
        <Card className="backdrop-blur-sm bg-white/90 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-start gap-6">
              <PillarScanAvatar
                avatarStyle={profile.avatar_style}
                nickname={profile.nickname}
                size="xl"
              />
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 mb-1">
                  {profile.nickname}
                </h1>
                <p className="text-gray-800 mb-4">
                  Membre depuis {new Date(profile.created_at).toLocaleDateString('fr-FR')}
                </p>
                
                {/* Stats rapides */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      {streak.totalExpressions}
                    </div>
                    <div className="text-sm text-gray-700">Expressions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      {badges.unlockedBadges.length}
                    </div>
                    <div className="text-sm text-gray-700">Badges</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      {badges.level}
                    </div>
                    <div className="text-sm text-gray-700">Niveau</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Niveau et XP */}
        <LevelDisplay
          level={badges.level}
          totalXP={badges.totalXP}
          levelProgress={badges.levelProgress}
          xpForNextLevel={badges.xpForNextLevel}
        />

        {/* Impact Score et Streak */}
        <div className="grid md:grid-cols-2 gap-6">
          <ImpactScore score={impactScore} variant="full" />
          <StreakDisplay variant="full" />
        </div>

        {/* Badges */}
        <Card className="backdrop-blur-sm bg-white/90 shadow-xl">
          <CardContent className="p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              Badges & Accomplissements
            </h2>
            
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {Object.values(BADGES).map(badge => {
                const progress = badges.getBadgeProgress(badge.id);
                const isUnlocked = !!progress?.earnedAt;
                
                return (
                  <BadgeItem
                    key={badge.id}
                    badge={badge}
                    isUnlocked={isUnlocked}
                    progress={progress?.progress || 0}
                    earnedAt={progress?.earnedAt || undefined}
                  />
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-center gap-4">
          <Button
            onClick={() => router.push('/create')}
            size="lg"
          >
            Nouvelle expression
          </Button>
          <Button
            onClick={() => router.push('/')}
            variant="secondary"
            size="lg"
          >
            Voir le feed
          </Button>
        </div>
      </div>
    </div>
  );
}