import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
// CountryProvider retiré - le pays vient maintenant du profil utilisateur
import { AppLayout } from "@/components/layout/AppLayout";
import { AppInitializer } from "@/components/providers/AppInitializer";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "PillarScan - Exprimez vos besoins citoyens",
  description: "Plateforme d'expression citoyenne pour identifier et partager vos besoins selon les 12 piliers de vie",
  keywords: "citoyen, expression, besoins, communauté, participation",
  authors: [{ name: "SMATFLOW" }],
  openGraph: {
    title: "PillarScan - Exprimez vos besoins citoyens",
    description: "Plateforme d'expression citoyenne pour identifier et partager vos besoins",
    type: "website",
  },
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "PillarScan",
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  viewportFit: 'cover',
  themeColor: '#3b82f6',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr">
      <body className={`${inter.className} antialiased`}>
        <AppInitializer>
          <AuthProvider>
            <ThemeProvider>
              <AppLayout>
                {children}
              </AppLayout>
            </ThemeProvider>
          </AuthProvider>
        </AppInitializer>
      </body>
    </html>
  );
}
