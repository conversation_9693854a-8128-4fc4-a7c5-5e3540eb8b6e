/**
 * API Route pour obtenir le statut d'enrichissement d'un média
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getMediaEnrichmentService } from '@/lib/services/MediaEnrichmentService';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ mediaId: string }> }
) {
  const { mediaId } = await params;
  try {
    // Vérifier l'authentification
    const cookieStore = await cookies();
    const token = cookieStore.get('pillarscan-token');
    
    if (!token) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const enrichmentService = getMediaEnrichmentService();
    const task = enrichmentService.getTaskStatus(mediaId);

    if (!task) {
      return NextResponse.json(
        { error: 'Tâche d\'enrichissement non trouvée' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      task
    });

  } catch (error) {
    console.error('Erreur lors de la récupération du statut:', error);
    return NextResponse.json(
      { error: 'Erreur lors de la récupération du statut' },
      { status: 500 }
    );
  }
}