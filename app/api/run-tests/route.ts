import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

// CORS headers for development
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
};

interface TestRunRequest {
  testPath?: string;
  coverage?: boolean;
  watch?: boolean;
}

interface TestRunResponse {
  success: boolean;
  message: string;
  output?: string;
  error?: string;
  duration?: number;
}

export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const body: TestRunRequest = await request.json().catch(() => ({}));
    const { testPath, coverage = true } = body;

    // Ensure test-reports directory exists
    const testReportsDir = path.join(process.cwd(), 'test-reports');
    await fs.mkdir(testReportsDir, { recursive: true });

    // Use the appropriate npm script based on requirements
    let command = coverage ? 'npm run test:dashboard' : 'npm run test:json';
    
    // Add specific test path if provided
    if (testPath) {
      command += ` -- ${testPath}`;
    }

    console.log('Running test command:', command);

    // Execute the test command
    try {
      const { stdout, stderr } = await execAsync(command, {
        cwd: process.cwd(),
        env: {
          ...process.env,
          CI: 'true', // Run in non-interactive mode
          FORCE_COLOR: '0', // Disable color output for cleaner logs
        },
        maxBuffer: 1024 * 1024 * 10, // 10MB buffer for large test outputs
      });

      const duration = Date.now() - startTime;

      // Check if test results file was created
      let testResultsCreated = false;
      let testResults = null;
      try {
        const resultsPath = path.join(testReportsDir, 'test-results.json');
        await fs.access(resultsPath);
        testResultsCreated = true;
        
        // Try to read and parse the results
        const resultsContent = await fs.readFile(resultsPath, 'utf-8');
        testResults = JSON.parse(resultsContent);
      } catch (error) {
        console.error('Error reading test results:', error);
      }

      const response: TestRunResponse = {
        success: testResults ? testResults.success : true,
        message: testResultsCreated 
          ? 'Tests completed successfully' 
          : 'Tests completed but results file was not created',
        output: stdout,
        duration,
      };

      if (stderr && !stderr.toLowerCase().includes('warning')) {
        response.error = stderr;
      }

      return NextResponse.json(response, { 
        status: 200,
        headers: corsHeaders 
      });
    } catch (execError) {
      // Even if the command fails, try to read the results
      const duration = Date.now() - startTime;
      let testResults = null;
      
      try {
        const resultsPath = path.join(testReportsDir, 'test-results.json');
        const resultsContent = await fs.readFile(resultsPath, 'utf-8');
        testResults = JSON.parse(resultsContent);
      } catch (_error) { // eslint-disable-line @typescript-eslint/no-unused-vars
        // Ignore read errors
      }

      return NextResponse.json(
        {
          success: false,
          message: testResults ? 'Tests completed with failures' : 'Failed to run tests',
          error: (execError as { stderr?: string }).stderr || (execError as Error).message,
          output: (execError as { stdout?: string }).stdout,
          duration,
        },
        { 
          status: 200, // Return 200 even on test failures so the client can process results
          headers: corsHeaders 
        }
      );
    }
  } catch (error) {
    console.error('Error in test runner:', error);
    
    const duration = Date.now() - startTime;
    
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to run tests',
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
      },
      { 
        status: 500,
        headers: corsHeaders 
      }
    );
  }
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function GET(_request: NextRequest) {
  // GET endpoint to check if tests are currently running
  try {
    // Check if npm test process is running
    const { stdout } = await execAsync('ps aux | grep "npm test" | grep -v grep || true');
    const isRunning = stdout.trim().length > 0;

    return NextResponse.json(
      {
        isRunning,
        message: isRunning ? 'Tests are currently running' : 'No tests are running',
      },
      { 
        status: 200,
        headers: corsHeaders 
      }
    );
  } catch (error) { // eslint-disable-line @typescript-eslint/no-unused-vars
    return NextResponse.json(
      {
        isRunning: false,
        error: 'Failed to check test status',
      },
      { 
        status: 500,
        headers: corsHeaders 
      }
    );
  }
}