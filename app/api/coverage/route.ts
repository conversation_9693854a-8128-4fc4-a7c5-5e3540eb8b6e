import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

// CORS headers for development
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
};

interface CoverageData {
  total: {
    lines: { total: number; covered: number; skipped: number; pct: number };
    statements: { total: number; covered: number; skipped: number; pct: number };
    functions: { total: number; covered: number; skipped: number; pct: number };
    branches: { total: number; covered: number; skipped: number; pct: number };
  };
  [key: string]: unknown;
}

interface CoverageSummary {
  total: {
    lines: { pct: number };
    statements: { pct: number };
    functions: { pct: number };
    branches: { pct: number };
  };
}

export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function GET(_request: NextRequest) {
  try {
    const coveragePath = path.join(process.cwd(), 'coverage', 'coverage-summary.json');
    const coverageFinalPath = path.join(process.cwd(), 'coverage', 'coverage-final.json');
    
    let coverageData: CoverageData | CoverageSummary | null = null;

    // Try to read coverage-summary.json first
    try {
      const summaryData = await fs.readFile(coveragePath, 'utf-8');
      coverageData = JSON.parse(summaryData);
    } catch (_error) { // eslint-disable-line @typescript-eslint/no-unused-vars
      // If summary doesn't exist, try coverage-final.json
      try {
        const finalData = await fs.readFile(coverageFinalPath, 'utf-8');
        const finalJson = JSON.parse(finalData);
        
        // Convert coverage-final format to summary format
        const summary: CoverageData = {
          total: {
            lines: { total: 0, covered: 0, skipped: 0, pct: 0 },
            statements: { total: 0, covered: 0, skipped: 0, pct: 0 },
            functions: { total: 0, covered: 0, skipped: 0, pct: 0 },
            branches: { total: 0, covered: 0, skipped: 0, pct: 0 },
          }
        };

        // Aggregate coverage data from all files
        for (const filePath in finalJson) {
          const file = finalJson[filePath];
          if (file.s) {
            const statementKeys = Object.keys(file.s);
            const coveredStatements = statementKeys.filter(key => file.s[key] > 0).length;
            summary.total.statements.total += statementKeys.length;
            summary.total.statements.covered += coveredStatements;
          }
          if (file.f) {
            const functionKeys = Object.keys(file.f);
            const coveredFunctions = functionKeys.filter(key => file.f[key] > 0).length;
            summary.total.functions.total += functionKeys.length;
            summary.total.functions.covered += coveredFunctions;
          }
          if (file.b) {
            const branchKeys = Object.keys(file.b);
            summary.total.branches.total += branchKeys.length * 2; // Each branch has 2 paths
            for (const key of branchKeys) {
              summary.total.branches.covered += file.b[key].filter((c: number) => c > 0).length;
            }
          }
        }

        // Calculate percentages
        const calculatePct = (covered: number, total: number) => 
          total > 0 ? (covered / total) * 100 : 0;

        summary.total.lines.pct = calculatePct(summary.total.lines.covered, summary.total.lines.total);
        summary.total.statements.pct = calculatePct(summary.total.statements.covered, summary.total.statements.total);
        summary.total.functions.pct = calculatePct(summary.total.functions.covered, summary.total.functions.total);
        summary.total.branches.pct = calculatePct(summary.total.branches.covered, summary.total.branches.total);

        coverageData = summary;
      } catch (_err) { // eslint-disable-line @typescript-eslint/no-unused-vars
        // No coverage data found
      }
    }

    if (!coverageData) {
      // Return default coverage data if none found
      return NextResponse.json(
        {
          total: {
            lines: { total: 0, covered: 0, skipped: 0, pct: 0 },
            statements: { total: 0, covered: 0, skipped: 0, pct: 0 },
            functions: { total: 0, covered: 0, skipped: 0, pct: 0 },
            branches: { total: 0, covered: 0, skipped: 0, pct: 0 },
          },
          message: 'No coverage data found. Run tests with coverage to generate data.',
        },
        { 
          status: 200,
          headers: corsHeaders 
        }
      );
    }

    return NextResponse.json(coverageData, { 
      status: 200,
      headers: corsHeaders 
    });
  } catch (error) {
    console.error('Error reading coverage data:', error);
    return NextResponse.json(
      { 
        error: 'Failed to read coverage data', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { 
        status: 500,
        headers: corsHeaders 
      }
    );
  }
}