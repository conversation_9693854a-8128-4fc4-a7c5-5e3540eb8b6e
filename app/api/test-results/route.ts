import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

// CORS headers for development
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
};

interface TestResult {
  testResults: Array<{
    assertionResults: Array<{
      ancestorTitles: string[];
      failureMessages: string[];
      fullName: string;
      location: unknown;
      status: 'passed' | 'failed' | 'pending' | 'skipped';
      title: string;
    }>;
    endTime: number;
    message: string;
    name: string;
    startTime: number;
    status: 'passed' | 'failed';
    summary: string;
  }>;
  success: boolean;
  numTotalTests: number;
  numPassedTests: number;
  numFailedTests: number;
  numPendingTests: number;
  numTodoTests: number;
  startTime: number;
  wasInterrupted: boolean;
}

export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function GET(_request: NextRequest) {
  try {
    // Try multiple possible locations for test results
    const possiblePaths = [
      path.join(process.cwd(), 'test-reports', 'test-results.json'),
      path.join(process.cwd(), 'coverage', 'test-results.json'),
      path.join(process.cwd(), 'test-results.json'),
    ];

    let testResults: TestResult | null = null;

    for (const testPath of possiblePaths) {
      try {
        const data = await fs.readFile(testPath, 'utf-8');
        testResults = JSON.parse(data);
        break;
      } catch (_error) { // eslint-disable-line @typescript-eslint/no-unused-vars
        // Continue to next path
      }
    }

    if (!testResults) {
      // Return mock data if no test results found
      return NextResponse.json(
        {
          success: true,
          numTotalTests: 0,
          numPassedTests: 0,
          numFailedTests: 0,
          numPendingTests: 0,
          numTodoTests: 0,
          startTime: Date.now(),
          endTime: Date.now(),
          testResults: [],
          message: 'No test results found. Run tests to generate results.',
        },
        { 
          status: 200,
          headers: corsHeaders 
        }
      );
    }

    // Transform test results to match the dashboard format
    const transformedResults = {
      numTotalTests: testResults.numTotalTests || 0,
      numPassedTests: testResults.numPassedTests || 0,
      numFailedTests: testResults.numFailedTests || 0,
      numPendingTests: testResults.numPendingTests || 0,
      numTodoTests: testResults.numTodoTests || 0,
      startTime: testResults.startTime || Date.now(),
      endTime: testResults.testResults?.[testResults.testResults.length - 1]?.endTime || Date.now(),
      testResults: testResults.testResults?.map(suite => 
        suite.assertionResults.map(test => ({
          name: test.fullName || test.title,
          status: test.status,
          duration: 0, // Jest doesn't provide individual test durations in JSON output
          failureMessages: test.failureMessages,
        }))
      ).flat() || [],
    };

    return NextResponse.json(transformedResults, { 
      status: 200,
      headers: corsHeaders 
    });
  } catch (error) {
    console.error('Error reading test results:', error);
    return NextResponse.json(
      { 
        error: 'Failed to read test results', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { 
        status: 500,
        headers: corsHeaders 
      }
    );
  }
}