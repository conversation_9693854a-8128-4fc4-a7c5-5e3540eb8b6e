'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useOnboardingState } from '@/hooks/useLocalStorage';
import { pillarScanAPI } from '@/lib/api/client';
import { useAuth } from '@/contexts/AuthContext';

// Variants d'animation pour les transitions
const pageVariants = {
  initial: { opacity: 0, x: 50 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -50 },
};

const pageTransition = {
  type: 'spring',
  stiffness: 300,
  damping: 30,
};

// Composants pour chaque étape
const Step1 = ({ onNext }: { onNext: () => void }) => (
  <motion.div
    key="step1"
    variants={pageVariants}
    initial="initial"
    animate="animate"
    exit="exit"
    transition={pageTransition}
    className="text-center space-y-8"
  >
    {/* Animation du logo */}
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
      className="w-32 h-32 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl"
    >
      <span className="text-6xl">🎯</span>
    </motion.div>

    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.4 }}
      className="space-y-4"
    >
      <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
        Votre voix compte
      </h1>
      <p className="text-xl text-gray-800 max-w-md mx-auto">
        Exprimez vos besoins citoyens et participez à l&apos;amélioration de votre communauté
      </p>
    </motion.div>

    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.6 }}
    >
      <Button onClick={onNext} size="lg" className="min-w-[200px]">
        Découvrir
      </Button>
    </motion.div>
  </motion.div>
);

const Step2 = ({ onNext }: { onNext: () => void }) => {
  const [activeDemo, setActiveDemo] = useState(0);

  const demos = [
    { emoji: '😤', mood: 'frustrated', text: 'Le bus n&apos;est jamais à l&apos;heure' },
    { emoji: '💡', mood: 'idea', text: 'Installer des bancs dans le parc' },
    { emoji: '😊', mood: 'happy', text: 'La nouvelle piste cyclable est super!' },
  ];

  return (
    <motion.div
      key="step2"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={pageTransition}
      className="space-y-8"
    >
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-4xl font-bold text-gray-900"
        >
          3 gestes pour changer
        </motion.h2>
        <p className="text-lg text-gray-800">
          Simple, rapide et impactant
        </p>
      </div>

      {/* Démo interactive */}
      <div className="space-y-6">
        {/* Étapes */}
        <div className="flex justify-between max-w-lg mx-auto">
          {['Ressentir', 'Exprimer', 'Connecter'].map((step, i) => (
            <motion.div
              key={step}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: i * 0.1 + 0.2 }}
              className={`text-center ${i <= activeDemo ? 'text-blue-600' : 'text-gray-700'}`}
            >
              <div className={`w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center text-xl font-bold ${
                i <= activeDemo ? 'bg-blue-100' : 'bg-gray-100'
              }`}>
                {i + 1}
              </div>
              <span className="text-sm font-medium">{step}</span>
            </motion.div>
          ))}
        </div>

        {/* Carte de démo */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-2xl shadow-xl p-6 max-w-md mx-auto"
        >
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-2xl">
              {demos[activeDemo].emoji}
            </div>
            <div>
              <p className="font-medium">Citoyen anonyme</p>
              <p className="text-sm text-gray-700">Quartier Centre</p>
            </div>
          </div>
          <p className="text-lg text-gray-800 mb-4">{demos[activeDemo].text}</p>
          <div className="flex items-center justify-between">
            <button
              onClick={() => setActiveDemo((activeDemo + 1) % 3)}
              className="text-blue-600 font-medium hover:underline"
            >
              Voir un autre exemple →
            </button>
            <span className="text-sm text-gray-700">12 relates</span>
          </div>
        </motion.div>
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="text-center"
      >
        <Button onClick={onNext} size="lg" className="min-w-[200px]">
          C&apos;est parti !
        </Button>
      </motion.div>
    </motion.div>
  );
};

const Step3 = ({ onComplete, onSkip }: { onComplete: () => void; onSkip: () => void }) => {
  const [nickname, setNickname] = useState('');
  const [selectedEmoji, setSelectedEmoji] = useState('😊');
  const [selectedColor, setSelectedColor] = useState('#3b82f6');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const emojis = ['😊', '🌟', '🎨', '🚀', '🌈', '💫', '🎯', '✨'];
  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#14b8a6', '#f97316'];

  const handleCreateProfile = async () => {
    if (!nickname.trim()) {
      setError('Veuillez choisir un pseudo');
      return;
    }

    try {
      setLoading(true);
      setError('');

      await pillarScanAPI.createOrUpdateProfile({
        nickname: nickname.trim(),
        avatar_style: {
          emoji: selectedEmoji,
          color: selectedColor,
        },
      });

      onComplete();
    } catch (err) {
      setError('Impossible de créer le profil. Veuillez réessayer.');
      console.error('Erreur création profil:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      key="step3"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={pageTransition}
      className="space-y-8 max-w-md mx-auto"
    >
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-4xl font-bold text-gray-900"
        >
          Commençons par vous
        </motion.h2>
        <p className="text-lg text-gray-800">
          Créez votre avatar unique en 30 secondes
        </p>
      </div>

      {/* Aperçu de l'avatar */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: 'spring', stiffness: 200 }}
        className="w-32 h-32 mx-auto rounded-full flex items-center justify-center text-5xl shadow-lg"
        style={{ backgroundColor: selectedColor }}
      >
        {selectedEmoji}
      </motion.div>

      {/* Formulaire */}
      <div className="space-y-6">
        {/* Pseudo */}
        <Input
          label="Votre pseudo"
          placeholder="Ex: CitoyenEngagé42"
          value={nickname}
          onChange={(e) => setNickname(e.target.value)}
          error={error}
          maxLength={20}
        />

        {/* Sélection emoji */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Choisissez votre emoji
          </label>
          <div className="grid grid-cols-4 gap-3">
            {emojis.map((emoji) => (
              <motion.button
                key={emoji}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedEmoji(emoji)}
                className={`w-16 h-16 rounded-xl text-2xl flex items-center justify-center border-2 transition-colors ${
                  selectedEmoji === emoji
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                {emoji}
              </motion.button>
            ))}
          </div>
        </div>

        {/* Sélection couleur */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Couleur de fond
          </label>
          <div className="grid grid-cols-4 gap-3">
            {colors.map((color) => (
              <motion.button
                key={color}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedColor(color)}
                className={`w-16 h-16 rounded-xl border-2 transition-all ${
                  selectedColor === color
                    ? 'border-gray-800 shadow-lg'
                    : 'border-gray-200'
                }`}
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="space-y-3">
        <Button
          onClick={handleCreateProfile}
          size="lg"
          fullWidth
          disabled={loading || !nickname.trim()}
        >
          {loading ? 'Création...' : 'Créer mon profil'}
        </Button>
        <Button
          onClick={onSkip}
          variant="ghost"
          fullWidth
          disabled={loading}
        >
          Explorer d&apos;abord
        </Button>
      </div>
    </motion.div>
  );
};

export default function OnboardingPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const { markCompleted, markSkipped } = useOnboardingState();
  const { refreshAuth } = useAuth();

  const handleComplete = async () => {
    markCompleted();
    await refreshAuth(); // Recharger le profil
    router.push('/');
  };

  const handleSkip = () => {
    markSkipped();
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
      <div className="max-w-3xl mx-auto px-4 py-12">
        {/* Progress bar */}
        <div className="mb-12">
          <div className="bg-white/50 h-2 rounded-full overflow-hidden backdrop-blur-sm">
            <motion.div
              className="bg-gradient-to-r from-blue-600 to-purple-600 h-full"
              initial={{ width: 0 }}
              animate={{ width: `${(currentStep / 3) * 100}%` }}
              transition={{ duration: 0.5, ease: 'easeInOut' }}
            />
          </div>
          <div className="flex justify-between mt-2">
            {[1, 2, 3].map((step) => (
              <span
                key={step}
                className={`text-sm font-medium ${
                  step <= currentStep ? 'text-blue-600' : 'text-gray-700'
                }`}
              >
                Étape {step}
              </span>
            ))}
          </div>
        </div>

        {/* Contenu animé */}
        <AnimatePresence mode="wait">
          {currentStep === 1 && (
            <Step1 onNext={() => setCurrentStep(2)} />
          )}
          {currentStep === 2 && (
            <Step2 onNext={() => setCurrentStep(3)} />
          )}
          {currentStep === 3 && (
            <Step3 onComplete={handleComplete} onSkip={handleSkip} />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}