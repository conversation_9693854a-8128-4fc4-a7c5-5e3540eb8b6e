'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { CreateExpressionForm } from '@/components/expressions/create/CreateExpressionForm';
import { motion } from 'framer-motion';

export default function CreateExpressionPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();
  const [authChecked, setAuthChecked] = useState(false);

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        // Sauvegarder l'URL de retour
        const returnUrl = window.location.pathname;
        localStorage.setItem('returnUrl', returnUrl);
        router.push('/auth/login');
      } else {
        setAuthChecked(true);
      }
    }
  }, [isAuthenticated, isLoading, router]);

  // Afficher un loader pendant la vérification
  if (isLoading || !authChecked) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          className="w-8 h-8 border-3 border-gray-300 border-t-blue-600 rounded-full"
        />
      </div>
    );
  }

  // Une fois authentifié, afficher le formulaire
  return <CreateExpressionForm />;
}