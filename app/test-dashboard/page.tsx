'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { motion } from 'framer-motion';

interface TestResults {
  numTotalTests: number;
  numPassedTests: number;
  numFailedTests: number;
  numPendingTests: number;
  numTodoTests: number;
  startTime: number;
  endTime: number;
  testResults: Array<{
    name: string;
    status: 'passed' | 'failed' | 'pending';
    duration: number;
    failureMessages?: string[];
  }>;
}

interface CoverageData {
  total: {
    lines: { pct: number };
    statements: { pct: number };
    functions: { pct: number };
    branches: { pct: number };
  };
}

export default function TestDashboard() {
  const [testResults, setTestResults] = useState<TestResults | null>(null);
  const [coverage, setCoverage] = useState<CoverageData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRunning, setIsRunning] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [error, setError] = useState<string | null>(null);

  const fetchResults = async () => {
    try {
      setError(null);
      
      // Fetch test results
      const testRes = await fetch('/api/test-results');
      if (testRes.ok) {
        const data = await testRes.json();
        setTestResults(data);
      } else if (testRes.status !== 404) {
        const errorData = await testRes.json().catch(() => ({}));
        setError(errorData.error || 'Failed to fetch test results');
      }

      // Fetch coverage data
      const coverageRes = await fetch('/api/coverage');
      if (coverageRes.ok) {
        const data = await coverageRes.json();
        setCoverage(data);
      } else if (coverageRes.status !== 404) {
        const errorData = await coverageRes.json().catch(() => ({}));
        console.error('Coverage fetch error:', errorData);
      }

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching test results:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch results');
    } finally {
      setIsLoading(false);
    }
  };

  const checkTestStatus = async () => {
    try {
      const res = await fetch('/api/run-tests');
      if (res.ok) {
        const data = await res.json();
        setIsRunning(data.isRunning);
      }
    } catch (error) {
      console.error('Error checking test status:', error);
    }
  };

  useEffect(() => {
    fetchResults();
    checkTestStatus();
    
    // Check status more frequently when tests are running
    const statusInterval = setInterval(() => {
      checkTestStatus();
      if (isRunning) {
        fetchResults();
      }
    }, isRunning ? 2000 : 5000);
    
    return () => clearInterval(statusInterval);
  }, [isRunning]);

  const runTests = async () => {
    setIsLoading(true);
    setIsRunning(true);
    setError(null);
    
    try {
      const res = await fetch('/api/run-tests', { 
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ coverage: true }),
      });
      
      const data = await res.json();
      
      if (!res.ok) {
        setError(data.error || 'Failed to run tests');
      } else if (!data.success) {
        setError(data.error || 'Tests completed with errors');
      }
      
      // Wait a bit for results to be written
      setTimeout(() => {
        fetchResults();
        setIsRunning(false);
      }, 1000);
    } catch (error) {
      console.error('Error running tests:', error);
      setError(error instanceof Error ? error.message : 'Failed to run tests');
      setIsRunning(false);
    }
  };

  if (isLoading && !testResults) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Chargement des résultats de tests...</p>
        </div>
      </div>
    );
  }

  const passRate = testResults 
    ? Math.round((testResults.numPassedTests / testResults.numTotalTests) * 100) 
    : 0;

  const coverageAvg = coverage 
    ? Math.round((
        coverage.total.lines.pct + 
        coverage.total.statements.pct + 
        coverage.total.functions.pct + 
        coverage.total.branches.pct
      ) / 4)
    : 0;

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-4xl font-bold text-gray-900">
              Dashboard de Tests
            </h1>
            <p className="text-gray-800 mt-2">
              Dernière mise à jour : {lastUpdated.toLocaleTimeString()}
            </p>
          </div>
          <div className="flex items-center gap-4">
            {isRunning && (
              <div className="flex items-center text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                <span className="text-sm">Tests en cours...</span>
              </div>
            )}
            <Button onClick={runTests} disabled={isLoading || isRunning}>
              {isRunning ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  En cours...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Relancer les tests
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <motion.div 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4"
          >
            <div className="flex">
              <svg className="w-5 h-5 text-red-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-red-800">Erreur</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <motion.div whileHover={{ scale: 1.05 }}>
            <Card className={passRate === 100 ? 'border-green-500' : passRate > 80 ? 'border-yellow-500' : 'border-red-500'}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-800">
                  Taux de Réussite
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {passRate}%
                </div>
                <p className="text-sm text-gray-700">
                  {testResults?.numPassedTests || 0}/{testResults?.numTotalTests || 0} tests
                </p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div whileHover={{ scale: 1.05 }}>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-800">
                  Couverture de Code
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {coverageAvg}%
                </div>
                <div className="flex gap-2 mt-2">
                  <Badge variant="secondary" className="text-xs">
                    L: {coverage?.total.lines.pct || 0}%
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    B: {coverage?.total.branches.pct || 0}%
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div whileHover={{ scale: 1.05 }}>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-800">
                  Tests Échoués
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-3xl font-bold ${testResults?.numFailedTests ? 'text-red-600' : 'text-green-600'}`}>
                  {testResults?.numFailedTests || 0}
                </div>
                <p className="text-sm text-gray-700">
                  {testResults?.numPendingTests || 0} en attente
                </p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div whileHover={{ scale: 1.05 }}>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-800">
                  Durée Totale
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {testResults ? ((testResults.endTime - testResults.startTime) / 1000).toFixed(1) : 0}s
                </div>
                <p className="text-sm text-gray-700">
                  Dernière exécution
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Test Results Detail */}
        {testResults && testResults.numFailedTests > 0 && (
          <Card className="mb-8 border-red-200">
            <CardHeader>
              <CardTitle className="text-red-600">Tests Échoués</CardTitle>
              <CardDescription>
                Détails des tests qui ont échoué
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testResults.testResults
                  .filter(test => test.status === 'failed')
                  .map((test, index) => (
                    <div key={index} className="border-l-4 border-red-500 pl-4">
                      <h4 className="font-medium text-gray-900">{test.name}</h4>
                      {test.failureMessages?.map((msg, msgIndex) => (
                        <pre key={msgIndex} className="mt-2 text-sm text-red-600 whitespace-pre-wrap">
                          {msg}
                        </pre>
                      ))}
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Coverage Details */}
        {coverage && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Couverture de Code Détaillée</CardTitle>
              <CardDescription>
                Pourcentage de code testé par catégorie
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries({
                  'Lignes': coverage.total.lines.pct,
                  'Instructions': coverage.total.statements.pct,
                  'Fonctions': coverage.total.functions.pct,
                  'Branches': coverage.total.branches.pct,
                }).map(([label, value]) => (
                  <div key={label}>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">{label}</span>
                      <span className="text-sm text-gray-800">{value}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <motion.div 
                        className="bg-blue-600 h-2.5 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${value}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Links to Reports */}
        <Card>
          <CardHeader>
            <CardTitle>Rapports Détaillés</CardTitle>
            <CardDescription>
              Accédez aux rapports HTML complets
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <a 
                href="/test-reports/test-report.html" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-blue-600 hover:text-blue-800"
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Rapport de Tests HTML
              </a>
              <a 
                href="/coverage/lcov-report/index.html" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-blue-600 hover:text-blue-800"
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Rapport de Couverture
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}