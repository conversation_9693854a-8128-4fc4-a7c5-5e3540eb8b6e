/**
 * Page de connexion
 */

'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { isValidEmail } from '@/lib/utils';

export default function LoginPage() {
  const { login, isLoading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!isValidEmail(email)) {
      setError('Email invalide');
      return;
    }
    
    if (password.length < 6) {
      setError('Le mot de passe doit contenir au moins 6 caractères');
      return;
    }
    
    try {
      setError('');
      await login(email, password);
    } catch (err) {
      console.error('Login error:', err);
      setError('Email ou mot de passe incorrect');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Connexion
            </h1>
            <p className="text-gray-800">
              Connectez-vous à votre compte PillarScan
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Mot de passe
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="••••••••"
                required
              />
            </div>

            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            <Button
              type="submit"
              fullWidth
              loading={isLoading}
            >
              Se connecter
            </Button>
          </form>

          <div className="mt-6 text-center space-y-2">
            <p className="text-sm text-gray-800">
              Pas encore de compte ?{' '}
              <Link href="/auth/register" className="text-blue-600 hover:underline font-medium">
                Créer un compte
              </Link>
            </p>
            
            <p className="text-sm text-gray-800">
              <Link href="/auth/forgot-password" className="text-blue-600 hover:underline">
                Mot de passe oublié ?
              </Link>
            </p>
          </div>

          {/* Compte de test pour dev */}
          {process.env.NEXT_PUBLIC_ENVIRONMENT === 'development' && (
            <div className="mt-6 p-4 bg-gray-100 rounded-lg">
              <p className="text-xs text-gray-800 mb-2">Compte de test :</p>
              <p className="text-xs font-mono"><EMAIL></p>
              <p className="text-xs font-mono">Smatflow@2024</p>
            </div>
          )}
        </div>

        <p className="text-center mt-4">
          <Link href="/" className="text-sm text-gray-800 hover:text-gray-900">
            ← Retour à l&apos;accueil
          </Link>
        </p>
      </div>
    </div>
  );
}