@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* High contrast text utilities */
@layer utilities {
  .text-high-contrast {
    @apply text-gray-900 dark:text-gray-100;
  }
  
  .text-high-contrast-secondary {
    @apply text-gray-800 dark:text-gray-200;
  }
  
  .text-high-contrast-muted {
    @apply text-gray-700 dark:text-gray-300;
  }
  
  /* Replace low contrast colors */
  .text-gray-400 {
    @apply text-gray-700 dark:text-gray-300;
  }
  
  .text-gray-500 {
    @apply text-gray-700 dark:text-gray-300;
  }
  
  .text-gray-600 {
    @apply text-gray-800 dark:text-gray-200;
  }
  
  .dark\:text-gray-400 {
    @apply dark:text-gray-300;
  }
  
  .dark\:text-gray-500 {
    @apply dark:text-gray-300;
  }
  
  .dark\:text-gray-600 {
    @apply dark:text-gray-200;
  }
}

html, body {
  height: 100%;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
}

#__next {
  height: 100%;
  display: flex;
  flex-direction: column;
}
