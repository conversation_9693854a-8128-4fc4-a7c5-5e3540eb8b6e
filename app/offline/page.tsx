/**
 * Page affichée en mode hors ligne
 */

'use client';

import React from 'react';
import { WifiOff, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { motion } from 'framer-motion';
import Link from 'next/link';

export default function OfflinePage() {
  const [isRetrying, setIsRetrying] = React.useState(false);

  const handleRetry = async () => {
    setIsRetrying(true);
    
    try {
      // Tenter de recharger la page
      const response = await fetch(window.location.href);
      if (response.ok) {
        window.location.reload();
      }
    } catch {
      // Toujours hors ligne
      console.log('Toujours hors ligne');
    } finally {
      setTimeout(() => setIsRetrying(false), 1000);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="text-center px-4 py-8 max-w-md">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", duration: 0.5 }}
          className="mb-8"
        >
          <div className="relative inline-block">
            <div className="absolute inset-0 bg-gray-300 dark:bg-gray-600 rounded-full blur-xl opacity-90" />
            <div className="relative bg-white dark:bg-gray-800 rounded-full p-6 shadow-lg">
              <WifiOff className="w-16 h-16 text-gray-700 dark:text-gray-300" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Vous êtes hors ligne
          </h1>
          
          <p className="text-gray-800 dark:text-gray-300 mb-8">
            Impossible de charger cette page sans connexion internet. 
            Vérifiez votre connexion et réessayez.
          </p>

          <div className="space-y-3">
            <Button
              onClick={handleRetry}
              variant="primary"
              className="w-full"
              disabled={isRetrying}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
              {isRetrying ? 'Vérification...' : 'Réessayer'}
            </Button>

            <Link href="/" className="block">
              <Button variant="secondary" className="w-full">
                <Home className="w-4 h-4 mr-2" />
                Retour à l&apos;accueil
              </Button>
            </Link>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="mt-12"
        >
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-left">
            <h3 className="font-semibold text-blue-900 dark:text-blue-300 mb-2">
              💡 Le saviez-vous ?
            </h3>
            <p className="text-sm text-blue-700 dark:text-blue-400">
              PillarScan fonctionne aussi hors ligne ! Installez l&apos;application 
              pour accéder à vos contenus favoris même sans connexion.
            </p>
          </div>
        </motion.div>

        {/* Animation de fond */}
        <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
          <motion.div
            className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-br from-blue-200 to-purple-200 dark:from-blue-900 dark:to-purple-900 rounded-full opacity-20"
            animate={{
              rotate: 360,
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
          />
          <motion.div
            className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-tl from-green-200 to-yellow-200 dark:from-green-900 dark:to-yellow-900 rounded-full opacity-20"
            animate={{
              rotate: -360,
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        </div>
      </div>
    </div>
  );
}