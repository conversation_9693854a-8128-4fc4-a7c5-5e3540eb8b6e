const API_URL = 'http://127.0.0.1:8001';

async function testCreateExpression() {
  try {
    // Test login first
    console.log('Testing login...');
    const loginResponse = await fetch(`${API_URL}/api/v2/civicperson/auth/login/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpassword123'
      })
    });

    if (!loginResponse.ok) {
      console.error('Login failed:', loginResponse.status);
      const error = await loginResponse.text();
      console.error('Error:', error);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('Login successful, token:', loginData.access);
    
    // Get profile to check country
    console.log('\nGetting profile...');
    const profileResponse = await fetch(`${API_URL}/api/v2/civicperson/auth/profile/`, {
      headers: {
        'Authorization': `Bearer ${loginData.access}`,
        'Content-Type': 'application/json',
      }
    });

    if (profileResponse.ok) {
      const profile = await profileResponse.json();
      console.log('Profile country:', profile.country);
    }

    // Test create expression
    console.log('\nTesting create expression...');
    const expressionData = {
      text: 'Test expression from debug script',
      mood: 'happy',
      visibility_level: 'public'
    };

    console.log('Request data:', JSON.stringify(expressionData, null, 2));

    const response = await fetch(`${API_URL}/api/v2/pillarscan/expressions/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${loginData.access}`,
        'Content-Type': 'application/json',
        'X-Country-Code': 'FR' // Add country code
      },
      body: JSON.stringify(expressionData)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('Response body:', responseText);

    if (response.ok) {
      const data = JSON.parse(responseText);
      console.log('Expression created successfully:', data);
    } else {
      console.error('Failed to create expression');
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

testCreateExpression();