#!/bin/bash

# Fix case-sensitive imports for UI components
echo "Fixing case-sensitive imports..."

# Fix button imports
find . -type f -name "*.tsx" -o -name "*.ts" | grep -v node_modules | xargs sed -i '' "s|from '@/components/ui/button'|from '@/components/ui/Button'|g"

# Fix input imports
find . -type f -name "*.tsx" -o -name "*.ts" | grep -v node_modules | xargs sed -i '' "s|from '@/components/ui/input'|from '@/components/ui/Input'|g"

echo "✅ Fixed case-sensitive imports"