{"name": "smatflow-pillarscan-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --watch", "test:ci": "jest --ci --coverage --maxWorkers=2", "test:json": "jest --json --outputFile=test-reports/test-results.json", "test:coverage:json": "jest --coverage --json --outputFile=test-reports/test-results.json --coverageReporters=json-summary", "test:dashboard": "jest --coverage --json --outputFile=test-reports/test-results.json --coverageReporters=json-summary --coverageReporters=lcov --coverageReporters=html", "test:report": "jest --coverage --reporters=default --reporters=jest-html-reporter", "test:watch": "jest --watch --coverage"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@tanstack/react-query": "^5.77.0", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "canvas": "^2.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "form-data": "^4.0.2", "framer-motion": "^12.12.1", "lucide-react": "^0.511.0", "next": "15.1.8", "node-fetch": "^2.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "eventsource": "^4.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-html-reporter": "^4.1.0", "lint-staged": "^16.0.0", "msw": "^2.8.4", "postcss": "^8", "prettier": "^3.5.3", "sharp": "^0.34.2", "tailwindcss": "^3.4.1", "typescript": "^5", "whatwg-fetch": "^3.6.20"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "jest --findRelatedTests --passWithNoTests"], "*.{json,css,scss,md}": ["prettier --write"]}}