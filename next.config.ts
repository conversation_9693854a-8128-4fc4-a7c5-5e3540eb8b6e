import type { NextConfig } from 'next';

const OUTPUT = process.env.NEXT_BUILD_OUTPUT as 'standalone' | 'export' | undefined;
const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000/api';

const nextConfig: NextConfig = {
  output: ['standalone', 'export', undefined].includes(OUTPUT) ? OUTPUT : undefined,
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${apiUrl}/:path*`,
      },
    ];
  },
};

export default nextConfig;
