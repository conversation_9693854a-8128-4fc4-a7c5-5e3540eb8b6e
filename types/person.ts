/**
 * Types pour le système de Person (Physical, Moral, Group)
 */

export enum PersonType {
  HUMAN = 'physical',
  MORAL = 'moral',
  GROUP = 'group'
}

export interface PersonReference {
  person_id?: string;      // UUID si la personne existe dans la DB
  person_code?: string;    // Identifiant unique
  person_name: string;     // Nom d'affichage (requis)
  person_type: PersonType;
  role: 'target' | 'source' | 'owner';
  // Pour les personnes non résolues (pas encore dans la DB)
  temp_id?: string;        // ID temporaire pour le suivi frontend
  needs_resolution?: boolean;
}

export interface Person {
  person_id: string;
  person_code: string;
  person_type: PersonType;
  display_name: string;
  // Champs spécifiques selon le type
  human_first_name?: string;
  human_last_name?: string;
  moral_legal_name?: string;
  moral_industry?: string;
  group_category?: string;
  // Métadonnées
  country_code: string;
  tags: string[];
  avatar_url?: string;
  verified?: boolean;
}

export interface PersonSearchResult {
  persons: Person[];
  total: number;
  page: number;
  limit: number;
}

export interface PersonSearchParams {
  query: string;
  type?: PersonType;
  country_code?: string;
  limit?: number;
  offset?: number;
}