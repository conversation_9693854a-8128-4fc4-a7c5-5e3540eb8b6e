/**
 * Système typographique PillarScan
 * Utilise Inter pour le texte et Sora pour les titres
 */

export const typography = {
  // Font families
  fonts: {
    sans: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
    display: ['Sora', 'Inter', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],
    mono: ['JetBrains Mono', 'Monaco', 'Consolas', 'monospace'],
  },

  // Font sizes avec line-height
  sizes: {
    xs: { size: '0.75rem', lineHeight: '1rem' },      // 12px
    sm: { size: '0.875rem', lineHeight: '1.25rem' },  // 14px
    base: { size: '1rem', lineHeight: '1.5rem' },     // 16px
    lg: { size: '1.125rem', lineHeight: '1.75rem' },  // 18px
    xl: { size: '1.25rem', lineHeight: '1.75rem' },   // 20px
    '2xl': { size: '1.5rem', lineHeight: '2rem' },    // 24px
    '3xl': { size: '1.875rem', lineHeight: '2.25rem' }, // 30px
    '4xl': { size: '2.25rem', lineHeight: '2.5rem' },  // 36px
    '5xl': { size: '3rem', lineHeight: '1' },          // 48px
    '6xl': { size: '3.75rem', lineHeight: '1' },       // 60px
  },

  // Font weights
  weights: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
  },

  // Styles de texte prédéfinis
  styles: {
    // Titres
    h1: {
      fontFamily: 'Sora',
      fontSize: '2.25rem',
      fontWeight: 700,
      lineHeight: '2.5rem',
      letterSpacing: '-0.02em',
    },
    h2: {
      fontFamily: 'Sora',
      fontSize: '1.875rem',
      fontWeight: 600,
      lineHeight: '2.25rem',
      letterSpacing: '-0.01em',
    },
    h3: {
      fontFamily: 'Inter',
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: '2rem',
    },
    h4: {
      fontFamily: 'Inter',
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: '1.75rem',
    },
    h5: {
      fontFamily: 'Inter',
      fontSize: '1.125rem',
      fontWeight: 600,
      lineHeight: '1.75rem',
    },
    h6: {
      fontFamily: 'Inter',
      fontSize: '1rem',
      fontWeight: 600,
      lineHeight: '1.5rem',
    },

    // Corps de texte
    body: {
      fontFamily: 'Inter',
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: '1.5rem',
    },
    bodyLarge: {
      fontFamily: 'Inter',
      fontSize: '1.125rem',
      fontWeight: 400,
      lineHeight: '1.75rem',
    },
    bodySmall: {
      fontFamily: 'Inter',
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: '1.25rem',
    },

    // Éléments UI
    button: {
      fontFamily: 'Inter',
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: '1.25rem',
      letterSpacing: '0.01em',
      textTransform: 'none' as const,
    },
    buttonLarge: {
      fontFamily: 'Inter',
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: '1.5rem',
      letterSpacing: '0.01em',
      textTransform: 'none' as const,
    },
    label: {
      fontFamily: 'Inter',
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: '1.25rem',
    },
    caption: {
      fontFamily: 'Inter',
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: '1rem',
      letterSpacing: '0.01em',
    },

    // Spéciaux
    code: {
      fontFamily: 'JetBrains Mono',
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: '1.25rem',
    },
    quote: {
      fontFamily: 'Inter',
      fontSize: '1.125rem',
      fontWeight: 400,
      lineHeight: '1.75rem',
      fontStyle: 'italic' as const,
    },
  },
} as const;

// Types helpers
export type Typography = typeof typography;
export type FontSize = keyof typeof typography.sizes;
export type FontWeight = keyof typeof typography.weights;
export type TextStyle = keyof typeof typography.styles;

// Helper pour créer des classes CSS
export const getTypographyClass = (style: TextStyle): string => {
  const config = typography.styles[style];
  return Object.entries(config)
    .map(([key, value]) => {
      const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
      return `${cssKey}: ${value}`;
    })
    .join('; ');
};