/**
 * Palette de couleurs PillarScan
 * Basée sur le design system SMATFLOW
 */

export const colors = {
  // Couleurs primaires
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6', // Bleu principal
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },

  // Couleurs secondaires
  secondary: {
    50: '#f5f3ff',
    100: '#ede9fe',
    200: '#ddd6fe',
    300: '#c4b5fd',
    400: '#a78bfa',
    500: '#8b5cf6', // Violet
    600: '#7c3aed',
    700: '#6d28d9',
    800: '#5b21b6',
    900: '#4c1d95',
  },

  // Couleurs par mood
  mood: {
    frustrated: {
      light: '#fee2e2',
      main: '#ef4444',
      dark: '#dc2626',
    },
    happy: {
      light: '#d1fae5',
      main: '#10b981',
      dark: '#059669',
    },
    idea: {
      light: '#fef3c7',
      main: '#f59e0b',
      dark: '#d97706',
    },
    question: {
      light: '#dbeafe',
      main: '#3b82f6',
      dark: '#2563eb',
    },
  },

  // Couleurs des piliers (12 couleurs distinctes)
  pillars: {
    1: '#3b82f6',  // Famille - Bleu
    2: '#10b981',  // Santé - Vert
    3: '#f59e0b',  // Éducation - Jaune
    4: '#6366f1',  // Travail - Indigo
    5: '#ef4444',  // Logement - Rouge
    6: '#8b5cf6',  // Mobilité - Violet
    7: '#ec4899',  // Culture - Rose
    8: '#14b8a6',  // Alimentation - Turquoise
    9: '#f97316',  // Environnement - Orange
    10: '#06b6d4', // Sécurité - Cyan
    11: '#84cc16', // Gouvernance - Vert clair
    12: '#8b5cf6', // Numérique - Violet foncé
  },

  // Couleurs neutres
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },

  // Couleurs sémantiques
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',

  // Couleurs de fond
  background: {
    primary: '#ffffff',
    secondary: '#f9fafb',
    tertiary: '#f3f4f6',
  },

  // Couleurs de texte
  text: {
    primary: '#111827',
    secondary: '#6b7280',
    tertiary: '#9ca3af',
    inverse: '#ffffff',
  },
} as const;

// Type helper pour TypeScript
export type Colors = typeof colors;
export type ColorKey = keyof Colors;

// Fonction helper pour obtenir une couleur avec opacité
export const withOpacity = (color: string, opacity: number): string => {
  return `${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`;
};

// Générateur de gradient
export const gradients = {
  primary: `linear-gradient(135deg, ${colors.primary[500]} 0%, ${colors.secondary[500]} 100%)`,
  mood: `linear-gradient(135deg, ${colors.mood.happy.main} 0%, ${colors.mood.idea.main} 100%)`,
  sunset: `linear-gradient(135deg, ${colors.error} 0%, ${colors.warning} 100%)`,
  ocean: `linear-gradient(135deg, ${colors.primary[600]} 0%, ${colors.primary[400]} 100%)`,
} as const;