/**
 * Client API pour PillarScan
 * Gère la communication avec le backend Django
 */

import { 
  PillarScanProfile, 
  PillarScanExpression, 
  PillarScanAssessment,
  CreateExpressionRequest,
  CreateProfileRequest,
  CreateAssessmentRequest,
  Pillar,
  PILLARS
} from '@/lib/types/pillarscan';
import { 
  NetworkError, 
  AuthenticationError, 
  CountrySelectionError, 
  ServerError, 
  ValidationError
} from './errors';

// Configuration de base
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';
const API_VERSION = 'v2';
const API_PREFIX = `/api/${API_VERSION}`;

// Classe principale du client API
export class PillarScanAPI {
  private baseUrl: string;
  private token: string | null = null;
  private countryCode: string | null = null;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
    // Le pays sera défini UNIQUEMENT après connexion via le profil utilisateur
  }

  // Méthodes d'authentification
  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('pillarscan_token', token);
    }
  }

  getToken(): string | null {
    if (this.token) return this.token;
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('pillarscan_token');
    }
    return this.token;
  }

  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('pillarscan_token');
    }
  }

  setCountryCode(code: string) {
    this.countryCode = code;
  }

  getCountryCode(): string | null {
    // Le pays vient UNIQUEMENT du profil utilisateur après connexion
    // On ne force plus de pays avant la connexion
    return this.countryCode;
  }

  // Endpoints qui ne nécessitent pas de country code
  private readonly EXEMPT_ENDPOINTS = [
    '/auth/login/',
    '/auth/register/',
    '/auth/refresh/',
    '/auth/password/reset/',
    '/auth/password/reset/confirm/',
  ];

  // Méthode générique pour les requêtes
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = this.getToken();
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // N'ajouter X-Country-Code que si :
    // 1. L'endpoint n'est pas exempté
    // 2. On a un pays défini (utilisateur connecté avec profil)
    const isExempt = this.EXEMPT_ENDPOINTS.some(exempt => endpoint.includes(exempt));
    const countryCode = this.getCountryCode();
    
    console.log('[REQUEST] Endpoint:', endpoint);
    console.log('[REQUEST] Is exempt from country code:', isExempt);
    console.log('[REQUEST] Country code:', countryCode);
    
    if (!isExempt && countryCode) {
      headers['X-Country-Code'] = countryCode;
      console.log('[REQUEST] Adding X-Country-Code header:', countryCode);
    }

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Merge with any headers from options
    if (options.headers) {
      Object.assign(headers, options.headers);
    }

    const url = `${this.baseUrl}${endpoint}`;
    console.log('[REQUEST] Full URL:', url);
    console.log('[REQUEST] Headers:', headers);
    console.log('[REQUEST] Method:', options.method || 'GET');
    
    let response: Response;
    
    try {
      response = await fetch(url, {
        method: options.method || 'GET',
        body: options.body,
        headers,
      });
    } catch (error) {
      // Erreur réseau (pas de connexion, serveur inaccessible, etc.)
      console.error('[NETWORK ERROR]', error);
      throw new NetworkError(error as Error);
    }

    console.log('[RESPONSE] Status:', response.status, response.statusText);
    console.log('[RESPONSE] Headers:', Object.fromEntries(response.headers.entries()));

    // Gestion des erreurs
    if (!response.ok) {
      // Pour le debug de l'erreur 406
      if (response.status === 406) {
        console.error('[ERROR 406] Not Acceptable');
        console.error('[ERROR 406] Response headers:', Object.fromEntries(response.headers.entries()));
        const errorText = await response.text();
        console.error('[ERROR 406] Response body:', errorText);
        
        try {
          const errorJson = JSON.parse(errorText);
          console.error('[ERROR 406] Parsed error:', errorJson);
        } catch {
          console.error('[ERROR 406] Could not parse error as JSON');
        }
      }
      
      const error = await response.json().catch(() => ({ detail: 'Une erreur est survenue' }));
      
      // Si 401, le token est invalide - on le supprime
      if (response.status === 401 && typeof window !== 'undefined') {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        throw new AuthenticationError(error.detail);
      }
      
      // Erreur de sélection de pays
      if (error.detail && typeof error.detail === 'string' && error.detail.toLowerCase().includes('country')) {
        throw new CountrySelectionError(error.detail);
      }
      
      // Erreur de validation
      if (response.status === 400 || response.status === 422) {
        throw new ValidationError(error.detail || error.message);
      }
      
      // Erreur serveur
      if (response.status >= 500) {
        throw new ServerError(response.status, error.detail || error.message);
      }
      
      // Autre erreur
      throw new Error(error.detail || `Erreur HTTP ${response.status}`);
    }

    // Réponse vide (204 No Content)
    if (response.status === 204) {
      return {} as T;
    }

    try {
      const data = await response.json();
      console.log('[RESPONSE] Data:', data);
      return data;
    } catch (err) {
      console.error('[ERROR] JSON parsing failed:', err);
      throw new Error('Erreur lors du parsing de la réponse');
    }
  }

  // === AUTHENTIFICATION ===
  async login(email: string, password: string): Promise<{ access: string; refresh: string; user?: { country?: string; [key: string]: unknown } }> {
    const response = await this.request<{ access: string; refresh: string; user?: { country?: string; [key: string]: unknown } }>(
      `${API_PREFIX}/civicperson/auth/login/`,
      {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      }
    );
    
    if (response.access) {
      this.setToken(response.access);
    }
    
    // Si le login retourne un user avec country, on le définit
    if (response.user?.country) {
      this.setCountryCode(response.user.country);
    }
    
    return response;
  }
  
  // Récupérer le profil utilisateur avec le pays
  async getAuthProfile(): Promise<{ country: string; email: string; id: string; [key: string]: unknown }> {
    const response = await this.request<{ country: string; email: string; id: string; [key: string]: unknown }>(
      `${API_PREFIX}/civicperson/auth/profile/`
    );
    
    // Définir le pays depuis le profil
    if (response.country) {
      this.setCountryCode(response.country);
    }
    
    return response;
  }

  async logout(): Promise<void> {
    try {
      await this.request(`${API_PREFIX}/civicperson/auth/logout/`, { method: 'POST' });
    } finally {
      this.clearToken();
    }
  }

  async refreshToken(refreshToken: string): Promise<{ access: string }> {
    const response = await this.request<{ access: string }>(
      `${API_PREFIX}/civicperson/auth/token/refresh/`,
      {
        method: 'POST',
        body: JSON.stringify({ refresh: refreshToken }),
      }
    );
    
    if (response.access) {
      this.setToken(response.access);
    }
    
    return response;
  }

  // === PROFILS ===
  async getMyProfile(): Promise<PillarScanProfile> {
    return this.request<PillarScanProfile>(`${API_PREFIX}/pillarscan/profiles/me/`);
  }

  async createProfile(data: Partial<PillarScanProfile>): Promise<PillarScanProfile> {
    return this.request<PillarScanProfile>(`${API_PREFIX}/pillarscan/profiles/`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getProfile(personId: string): Promise<PillarScanProfile> {
    return this.request<PillarScanProfile>(`${API_PREFIX}/pillarscan/profiles/${personId}/`);
  }

  async createOrUpdateProfile(data: CreateProfileRequest): Promise<PillarScanProfile> {
    return this.request<PillarScanProfile>(`${API_PREFIX}/pillarscan/profiles/`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // === EXPRESSIONS ===
  async getExpressions(params?: {
    mood?: string;
    limit?: number;
    offset?: number;
    search?: string;
    with_media?: boolean;
  }): Promise<{ results: PillarScanExpression[]; count: number }> {
    const queryParams = new URLSearchParams();
    if (params?.mood) queryParams.append('mood', params.mood);
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());
    if (params?.search) queryParams.append('search', params.search);
    // Nouveau paramètre pour charger les URLs MinIO des médias
    if (params?.with_media) queryParams.append('with_media', 'true');

    const query = queryParams.toString() ? `?${queryParams}` : '';
    
    // Requête publique sans authentification
    const url = `${this.baseUrl}${API_PREFIX}/pillarscan/expressions/${query}`;
    
    // Les médias sont maintenant chargés depuis le backend
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    // Ajouter le header X-Country-Code seulement s'il est défini
    if (this.countryCode) {
      headers['X-Country-Code'] = this.countryCode;
    }
    
    const response = await fetch(url, {
      method: 'GET',
      headers
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Une erreur est survenue' }));
      throw new Error(error.detail || `Erreur HTTP ${response.status}`);
    }

    return response.json();
  }

  async getExpression(expressionId: string): Promise<PillarScanExpression> {
    return this.request<PillarScanExpression>(`${API_PREFIX}/pillarscan/expressions/${expressionId}/`);
  }

  async createExpression(data: CreateExpressionRequest): Promise<PillarScanExpression> {
    return this.request<PillarScanExpression>(`${API_PREFIX}/pillarscan/expressions/`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async relateToExpression(expressionId: string): Promise<{ related: boolean }> {
    return this.request<{ related: boolean }>(
      `${API_PREFIX}/pillarscan/expressions/${expressionId}/relate/`,
      { method: 'POST' }
    );
  }

  async unrelateFromExpression(expressionId: string): Promise<{ related: boolean }> {
    return this.request<{ related: boolean }>(
      `${API_PREFIX}/pillarscan/expressions/${expressionId}/relate/`,
      { method: 'DELETE' }
    );
  }

  async getMyExpressions(): Promise<PillarScanExpression[]> {
    const response = await this.request<{ results: PillarScanExpression[] }>(
      `${API_PREFIX}/pillarscan/expressions/my_expressions/`
    );
    return response.results || [];
  }

  // === ÉVALUATIONS ===
  async getAssessments(): Promise<PillarScanAssessment[]> {
    const response = await this.request<{ results: PillarScanAssessment[] }>(
      `${API_PREFIX}/pillarscan/assessments/`
    );
    return response.results || [];
  }

  async getAssessment(assessmentId: string): Promise<PillarScanAssessment> {
    return this.request<PillarScanAssessment>(`${API_PREFIX}/pillarscan/assessments/${assessmentId}/`);
  }

  async createAssessment(data: CreateAssessmentRequest): Promise<PillarScanAssessment> {
    return this.request<PillarScanAssessment>(`${API_PREFIX}/pillarscan/assessments/`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getLatestAssessment(): Promise<PillarScanAssessment | null> {
    const assessments = await this.getAssessments();
    return assessments.length > 0 ? assessments[0] : null;
  }

  // === PILIERS ===
  async getPillars(): Promise<Pillar[]> {
    // Pour l'instant, retourne les piliers statiques
    // Plus tard, on pourra les récupérer du backend
    return Promise.resolve(PILLARS);
  }

  async getPillar(pillarId: number): Promise<Pillar | undefined> {
    const pillars = await this.getPillars();
    return pillars.find(p => p.id === pillarId);
  }

  // === STATISTIQUES ===
  async getExpressionStats(): Promise<{
    total: number;
    by_mood: Record<string, number>;
    today: number;
    this_week: number;
  }> {
    // TODO: Implémenter quand l'endpoint sera disponible
    return {
      total: 0,
      by_mood: {},
      today: 0,
      this_week: 0,
    };
  }

  // === UTILITAIRES ===
  async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/health/`);
      return response.ok;
    } catch {
      return false;
    }
  }

  // Téléchargement de médias
  getMediaUrl(mediaId: string): string {
    return `${this.baseUrl}/media/${mediaId}`;
  }

  // DEPRECATED - Utiliser createExpressionWithMedia à la place
  async uploadMedia(): Promise<{ id: string; url: string }> {
    throw new Error('uploadMedia est déprécié. Utilisez createExpressionWithMedia à la place');
  }

  // Pipeline corrigé pour créer une expression avec médias (deux étapes)
  async createExpressionWithMedia(
    data: CreateExpressionRequest,
    files: File[]
  ): Promise<PillarScanExpression> {
    console.log('=== DÉBUT createExpressionWithMedia ===');
    console.log('Data:', data);
    console.log('Files:', files.map(f => ({ name: f.name, type: f.type, size: f.size })));
    
    try {
      // 1. Créer d'abord l'expression
      console.log('1. Création de l\'expression...');
      const expression = await this.createExpression(data);
      console.log('Expression créée:', expression);
      
      // 2. Upload des médias via l'endpoint spécifique PillarScan
      if (files.length > 0) {
        console.log('2. Upload des médias...');
        
        // Pour l'instant, on ne supporte qu'une image à la fois
        // L'API PillarScan attend un seul fichier par requête
        const file = files[0];
        console.log(`  - Upload du fichier: ${file.name}`);
        
        const uploadFormData = new FormData();
        uploadFormData.append('file', file); // Le backend attend 'file', pas 'media_image_0'
        
        // URL correcte pour l'upload de média PillarScan
        const uploadUrl = `${this.baseUrl}/api/v2/pillarscan/expressions/${expression.expression_id}/upload_media/`;
        const token = this.getToken();
        console.log('  Upload URL:', uploadUrl);
        console.log('  Token présent:', !!token);
        
        const uploadHeaders: Record<string, string> = {
          'Authorization': `Bearer ${token}`,
          // Ne pas définir Content-Type pour FormData - le navigateur le fait automatiquement
        };
        
        // Ajouter le country code si disponible
        const countryCode = this.getCountryCode();
        if (countryCode) {
          uploadHeaders['X-Country-Code'] = countryCode;
          console.log('  X-Country-Code ajouté:', countryCode);
        }
        
        const uploadResponse = await fetch(uploadUrl, {
          method: 'POST',
          headers: uploadHeaders,
          body: uploadFormData
        });

        console.log('  Response status:', uploadResponse.status, uploadResponse.statusText);

        if (!uploadResponse.ok) {
          const errorText = await uploadResponse.text();
          console.error('  ERREUR upload - Texte brut:', errorText);
          
          try {
            const error = JSON.parse(errorText);
            console.error('  ERREUR upload - JSON parsé:', error);
          } catch {
            console.error('  ERREUR: Impossible de parser la réponse en JSON');
          }
          
          // Retourner l'expression même si l'upload échoue
          return expression;
        }

        const uploadResult = await uploadResponse.json();
        console.log('  Upload réussi:', uploadResult);
        
        // 3. Récupérer l'expression mise à jour avec les médias
        console.log('3. Récupération de l\'expression avec médias...');
        const updatedExpression = await this.getExpression(expression.expression_id);
        console.log('Expression mise à jour:', updatedExpression);
        
        console.log('=== FIN createExpressionWithMedia - Succès avec médias ===');
        return updatedExpression;
      } else {
        // Pas de médias, retourner l'expression simple
        console.log('=== FIN createExpressionWithMedia - Sans médias ===');
        return expression;
      }
    } catch (error) {
      console.error('=== ERREUR createExpressionWithMedia ===');
      console.error('Erreur complète:', error);
      throw error;
    }
  }
}

// Instance singleton par défaut
export const pillarScanAPI = new PillarScanAPI();

// Hooks utilitaires pour React
export function usePillarScanAPI() {
  return pillarScanAPI;
}