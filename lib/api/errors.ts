/**
 * Erreurs personnalisées pour l'API avec messages user-friendly
 */

export class NetworkError extends Error {
  constructor(public originalError: Error) {
    super('Impossible de se connecter au serveur. Veuillez vérifier votre connexion internet ou réessayer plus tard.');
    this.name = 'NetworkError';
  }
}

export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public detail?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export class AuthenticationError extends APIError {
  constructor(detail?: string) {
    super('Votre session a expiré. Veuillez vous reconnecter.', 401, detail);
    this.name = 'AuthenticationError';
  }
}

export class CountrySelectionError extends APIError {
  constructor(detail?: string) {
    super('Veuillez sélectionner votre pays pour continuer.', 400, detail);
    this.name = 'CountrySelectionError';
  }
}

export class ServerError extends APIError {
  constructor(status: number, detail?: string) {
    super('Une erreur serveur est survenue. Veuillez réessayer plus tard.', status, detail);
    this.name = 'ServerError';
  }
}

export class ValidationError extends APIError {
  constructor(detail?: string) {
    super('Les données envoyées sont invalides. Veuillez vérifier votre saisie.', 400, detail);
    this.name = 'ValidationError';
  }
}

/**
 * Transforme les erreurs techniques en messages user-friendly
 */
export function handleAPIError(error: unknown): Error {
  // Erreur réseau (pas de connexion)
  if (error instanceof TypeError && error.message === 'Failed to fetch') {
    return new NetworkError(error);
  }
  
  // Erreur API déjà formatée
  if (error instanceof APIError) {
    return error;
  }
  
  // Autre erreur
  if (error instanceof Error) {
    return error;
  }
  
  // Erreur inconnue
  return new Error('Une erreur inattendue est survenue.');
}