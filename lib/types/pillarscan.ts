/**
 * Types TypeScript pour PillarScan
 * Basés sur les modèles Cassandra Django
 */

// Types de base
export type Mood = 'frustrated' | 'happy' | 'idea' | 'question';
export type VisibilityLevel = 'private' | 'anonymous' | 'public';
export type ScanMethod = 'quick' | 'detailed' | 'guided';

// Interface pour le profil utilisateur PillarScan
export interface PillarScanProfile {
  person_id: string;
  country_code: string;
  created_at: string;
  updated_at: string;
  
  // Profil
  nickname: string;
  avatar_style: {
    color: string;
    emoji: string;
    pattern?: string;
  };
  bio?: string;
  
  // Stats
  expression_count: number;
  current_streak: number;
  longest_streak: number;
  last_expression_date?: string;
  impact_score: number;
  
  // Gamification
  badges: Record<string, string>; // badge_type: earned_at
  achievements: string[];
  level: number;
  experience_points: number;
  pillar_expertise: Record<number, number>; // pillar_id: level
  
  // Préférences
  preferred_language: string;
  notification_preferences?: Record<string, boolean>;
}

// Interface pour une expression
export interface PillarScanExpression {
  expression_id: string;
  created_at: string;
  person_id: string;
  
  // Contenu
  mood: Mood;
  text: string;
  
  // Localisation
  location?: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };
  location_display_name?: string;
  city_code?: string;
  municipality_code?: string;
  department_code?: string;
  region_code?: string;
  country_code: string;
  
  // Classification
  pillar_scores?: Record<number, number>; // pillar_id: score
  suggested_pillar?: number;
  confirmed_pillar?: number;
  
  // Engagement
  visibility_level: VisibilityLevel;
  is_published: boolean;
  relate_count?: number;
  
  // Contexte utilisateur
  user_nickname?: string;
  user_avatar?: {
    color: string;
    emoji: string;
  };
  
  // État pour l'UI
  user_has_related?: boolean;
  earned_badges?: string[];
  
  // Médias attachés (pipeline professionnel)
  media_refs?: Record<string, string>; // role -> media_id (UUID)
  has_media?: boolean;
  media_processing_status?: 'pending' | 'processing' | 'completed' | 'failed';
  
  // URLs des médias (chargées avec with_media=true)
  // Support deux formats : objet (futur) ou tableau (actuel)
  media_urls?: Record<string, {
    id: string;
    url: string;
    type?: string;
    size?: number;
    mime_type?: string;
    thumbnail_url?: string;
  }> | Array<{
    id: string;
    url: string;
    type?: string;
    size?: number;
    mime_type?: string;
    thumbnail_url?: string;
  }>;
}

// Interface pour une évaluation des 12 piliers
export interface PillarScanAssessment {
  assessment_id: string;
  assessment_date: string;
  created_at: string;
  person_id: string;
  
  // Scores
  pillar_scores: Record<number, number>; // pillar_id: score (0-100)
  overall_score?: number;
  
  // Comparaisons
  local_percentiles?: Record<number, number>;
  national_percentiles?: Record<number, number>;
  
  // Tendances
  score_changes?: Record<number, number>; // différence avec scan précédent
  
  // Insights IA
  ai_insights?: string[];
  recommended_actions?: string[];
  identified_strengths?: number[];
  improvement_areas?: number[];
  
  // Contexte
  location?: {
    type: 'Point';
    coordinates: [number, number];
  };
  scan_duration: number;
  scan_method: ScanMethod;
}

// Définition des 12 piliers
export interface Pillar {
  id: number;
  name_fr: string;
  name_en: string;
  description_fr: string;
  description_en: string;
  emoji: string;
  category: 'society' | 'economy' | 'environment' | 'governance';
  keywords: string[];
}

// Les 12 piliers constants
export const PILLARS: Pillar[] = [
  {
    id: 1,
    name_fr: "Famille et Relations",
    name_en: "Family and Relationships",
    description_fr: "Relations familiales, amitié, vie sociale",
    description_en: "Family relationships, friendship, social life",
    emoji: "👨‍👩‍👧‍👦",
    category: "society",
    keywords: ["famille", "amis", "relations", "social"]
  },
  {
    id: 2,
    name_fr: "Finances",
    name_en: "Finance",
    description_fr: "Revenus, épargne, dettes, pouvoir d'achat",
    description_en: "Income, savings, debt, purchasing power",
    emoji: "💰",
    category: "economy",
    keywords: ["argent", "salaire", "économie", "budget"]
  },
  {
    id: 3,
    name_fr: "Travail",
    name_en: "Work",
    description_fr: "Emploi, carrière, conditions de travail",
    description_en: "Employment, career, working conditions",
    emoji: "💼",
    category: "economy",
    keywords: ["emploi", "travail", "carrière", "profession"]
  },
  {
    id: 4,
    name_fr: "Logement",
    name_en: "Housing",
    description_fr: "Habitat, quartier, cadre de vie",
    description_en: "Home, neighborhood, living environment",
    emoji: "🏠",
    category: "society",
    keywords: ["maison", "appartement", "logement", "habitat"]
  },
  {
    id: 5,
    name_fr: "Transport et Mobilité",
    name_en: "Transport and Mobility",
    description_fr: "Déplacements, transports publics, infrastructures",
    description_en: "Travel, public transport, infrastructure",
    emoji: "🚗",
    category: "environment",
    keywords: ["transport", "voiture", "bus", "train", "mobilité"]
  },
  {
    id: 6,
    name_fr: "Environnement",
    name_en: "Environment",
    description_fr: "Nature, pollution, climat, espaces verts",
    description_en: "Nature, pollution, climate, green spaces",
    emoji: "🌍",
    category: "environment",
    keywords: ["nature", "écologie", "climat", "pollution"]
  },
  {
    id: 7,
    name_fr: "Santé",
    name_en: "Health",
    description_fr: "Santé physique, mentale, accès aux soins",
    description_en: "Physical health, mental health, healthcare access",
    emoji: "⚕️",
    category: "society",
    keywords: ["santé", "médecin", "hôpital", "soins"]
  },
  {
    id: 8,
    name_fr: "Alimentation",
    name_en: "Food",
    description_fr: "Nutrition, accès à la nourriture, qualité",
    description_en: "Nutrition, food access, quality",
    emoji: "🍽️",
    category: "society",
    keywords: ["nourriture", "manger", "alimentation", "repas"]
  },
  {
    id: 9,
    name_fr: "Éducation et Culture",
    name_en: "Education and Culture",
    description_fr: "Formation, accès à la culture, loisirs",
    description_en: "Training, cultural access, leisure",
    emoji: "📚",
    category: "society",
    keywords: ["école", "formation", "culture", "apprentissage"]
  },
  {
    id: 10,
    name_fr: "Sécurité",
    name_en: "Security",
    description_fr: "Sécurité personnelle, cybersécurité, justice",
    description_en: "Personal security, cybersecurity, justice",
    emoji: "🛡️",
    category: "governance",
    keywords: ["sécurité", "police", "justice", "protection"]
  },
  {
    id: 11,
    name_fr: "Gouvernance",
    name_en: "Governance",
    description_fr: "Services publics, administration, participation",
    description_en: "Public services, administration, participation",
    emoji: "🏛️",
    category: "governance",
    keywords: ["gouvernement", "administration", "service public", "politique"]
  },
  {
    id: 12,
    name_fr: "Numérique et Innovation",
    name_en: "Digital and Innovation",
    description_fr: "Technologie, internet, innovation, IA",
    description_en: "Technology, internet, innovation, AI",
    emoji: "💻",
    category: "economy",
    keywords: ["technologie", "internet", "numérique", "innovation"]
  }
];

// Types pour les requêtes API
export interface CreateExpressionRequest {
  mood: Mood;
  text: string;
  location?: {
    type: 'Point';
    coordinates: [number, number];
  };
  suggested_pillar?: number;
  visibility_level?: VisibilityLevel;
  person_references?: Array<{
    person_id?: string;
    person_code?: string;
    person_name: string;
    person_type: 'physical' | 'moral' | 'group';
    role: 'target' | 'source' | 'owner';
    temp_id?: string;
    needs_resolution?: boolean;
  }>;
  // media_refs est géré automatiquement par le pipeline, pas envoyé directement
}

export interface CreateProfileRequest {
  nickname: string;
  avatar_style: {
    color: string;
    emoji: string;
    pattern?: string;
  };
  bio?: string;
  preferred_language?: string;
  notification_preferences?: Record<string, boolean>;
}

export interface CreateAssessmentRequest {
  pillar_scores: Record<number, number>;
  scan_duration: number;
  scan_method: ScanMethod;
  location?: {
    type: 'Point';
    coordinates: [number, number];
  };
}

// Types pour les badges
export interface Badge {
  id: string;
  name: string;
  description: string;
  xp: number;
  icon?: string;
}

// Définition des badges disponibles
export const BADGES: Record<string, Badge> = {
  first_expression: {
    id: 'first_expression',
    name: 'Première voix',
    description: 'Votre première expression',
    xp: 100,
    icon: '🎤'
  },
  streak_3: {
    id: 'streak_3',
    name: 'Habitué',
    description: '3 jours consécutifs',
    xp: 150,
    icon: '🔥'
  },
  streak_7: {
    id: 'streak_7',
    name: 'Régulier',
    description: '7 jours consécutifs',
    xp: 300,
    icon: '⭐'
  },
  streak_30: {
    id: 'streak_30',
    name: 'Pilier de la communauté',
    description: '30 jours consécutifs',
    xp: 1000,
    icon: '🏆'
  },
  helper_10: {
    id: 'helper_10',
    name: 'Empathique',
    description: '10 expressions relatées',
    xp: 200,
    icon: '💝'
  },
  diverse_moods: {
    id: 'diverse_moods',
    name: 'Esprit équilibré',
    description: 'Utilisé les 4 moods',
    xp: 250,
    icon: '🎭'
  },
  night_owl: {
    id: 'night_owl',
    name: 'Noctambule',
    description: 'Expression après minuit',
    xp: 50,
    icon: '🦉'
  },
  early_bird: {
    id: 'early_bird',
    name: 'Lève-tôt',
    description: 'Expression avant 6h',
    xp: 50,
    icon: '🐦'
  }
};

// Types utilitaires
export type PillarCategory = 'society' | 'economy' | 'environment' | 'governance';

export interface PaginatedResponse<T> {
  results: T[];
  count: number;
  next?: string;
  previous?: string;
}

export interface ApiError {
  detail: string;
  code?: string;
}