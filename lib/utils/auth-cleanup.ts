/**
 * Utilitaire pour nettoyer les tokens invalides au démarrage
 */
export function cleanupInvalidTokens() {
  if (typeof window === 'undefined') return;
  
  // Vérifie si on a des tokens dans le localStorage
  const accessToken = localStorage.getItem('access_token');
  
  // Si on a des tokens, on vérifie s'ils sont valides (basique)
  if (accessToken) {
    try {
      // Décode le JWT pour vérifier s'il est expiré
      const tokenParts = accessToken.split('.');
      if (tokenParts.length === 3) {
        const payload = JSON.parse(atob(tokenParts[1]));
        const now = Date.now() / 1000;
        
        // Si le token est expiré, on le supprime
        if (payload.exp && payload.exp < now) {
          console.log('Token expiré détecté, nettoyage...');
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('pillarscan_token');
        }
      }
    } catch {
      // Si on ne peut pas décoder le token, il est invalide
      console.log('Token invalide détecté, nettoyage...');
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('pillarscan_token');
    }
  }
}