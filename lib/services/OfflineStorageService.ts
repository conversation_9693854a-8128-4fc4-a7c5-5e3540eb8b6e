/**
 * Service de stockage hors ligne avec IndexedDB
 * Gère la persistance locale des données pour le mode offline
 */

export interface CachedExpression {
  id: string;
  title: string;
  content: string;
  pillar_id: string;
  emotion_type: string;
  author: {
    id: string;
    nickname: string;
    avatar_style: Record<string, unknown>;
  };
  relate_count: number;
  user_related: boolean;
  created_at: string;
  updated_at: string;
  cached_at: string;
  media?: Array<{
    url: string;
    type: 'image' | 'video';
    thumbnail_url?: string;
  }>;
}

export interface PendingExpression {
  id: string;
  token: string;
  data: {
    title: string;
    content: string;
    pillar_id: string;
    emotion_type: string;
    media_ids?: string[];
  };
  created_at: string;
  retry_count: number;
}

export interface OfflineMedia {
  id: string;
  blob: Blob;
  type: string;
  name: string;
  created_at: string;
}

class OfflineStorageService {
  private dbName = 'PillarScanDB';
  private dbVersion = 1;
  private db: IDBDatabase | null = null;

  /**
   * Initialiser la base de données
   */
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        console.error('Erreur ouverture IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('IndexedDB initialisée');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Store pour les expressions en cache
        if (!db.objectStoreNames.contains('cached_expressions')) {
          const expressionStore = db.createObjectStore('cached_expressions', { 
            keyPath: 'id' 
          });
          expressionStore.createIndex('pillar_id', 'pillar_id');
          expressionStore.createIndex('created_at', 'created_at');
          expressionStore.createIndex('cached_at', 'cached_at');
        }

        // Store pour les expressions en attente de sync
        if (!db.objectStoreNames.contains('pending_expressions')) {
          const pendingStore = db.createObjectStore('pending_expressions', { 
            keyPath: 'id' 
          });
          pendingStore.createIndex('created_at', 'created_at');
        }

        // Store pour les médias hors ligne
        if (!db.objectStoreNames.contains('offline_media')) {
          const mediaStore = db.createObjectStore('offline_media', { 
            keyPath: 'id' 
          });
          mediaStore.createIndex('created_at', 'created_at');
        }

        // Store pour les métadonnées de sync
        if (!db.objectStoreNames.contains('sync_metadata')) {
          db.createObjectStore('sync_metadata', { keyPath: 'key' });
        }
      };
    });
  }

  /**
   * Vérifier que la DB est initialisée
   */
  private ensureDb(): void {
    if (!this.db) {
      throw new Error('Database not initialized. Call init() first.');
    }
  }

  /**
   * Mettre en cache des expressions
   */
  async cacheExpressions(expressions: CachedExpression[]): Promise<void> {
    this.ensureDb();
    
    const tx = this.db!.transaction(['cached_expressions'], 'readwrite');
    const store = tx.objectStore('cached_expressions');
    
    const promises = expressions.map(expression => {
      const cached = {
        ...expression,
        cached_at: new Date().toISOString()
      };
      return store.put(cached);
    });

    await Promise.all(promises);
    console.log(`${expressions.length} expressions mises en cache`);
  }

  /**
   * Récupérer les expressions en cache
   */
  async getCachedExpressions(
    filters?: {
      pillar_id?: string;
      limit?: number;
      offset?: number;
    }
  ): Promise<CachedExpression[]> {
    this.ensureDb();
    
    return new Promise((resolve, reject) => {
      const tx = this.db!.transaction(['cached_expressions'], 'readonly');
      const store = tx.objectStore('cached_expressions');
      
      let request: IDBRequest;
      
      if (filters?.pillar_id) {
        const index = store.index('pillar_id');
        request = index.getAll(filters.pillar_id);
      } else {
        request = store.getAll();
      }

      request.onsuccess = () => {
        let results = request.result as CachedExpression[];
        
        // Trier par date de création
        results.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        
        // Appliquer limit et offset
        if (filters?.offset) {
          results = results.slice(filters.offset);
        }
        if (filters?.limit) {
          results = results.slice(0, filters.limit);
        }
        
        resolve(results);
      };

      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Ajouter une expression en attente de synchronisation
   */
  async addPendingExpression(
    token: string,
    data: PendingExpression['data']
  ): Promise<string> {
    this.ensureDb();
    
    const id = `pending_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const pendingExpression: PendingExpression = {
      id,
      token,
      data,
      created_at: new Date().toISOString(),
      retry_count: 0
    };

    return new Promise((resolve, reject) => {
      const tx = this.db!.transaction(['pending_expressions'], 'readwrite');
      const store = tx.objectStore('pending_expressions');
      const request = store.add(pendingExpression);

      request.onsuccess = () => {
        console.log('Expression ajoutée à la file d\'attente:', id);
        this.requestBackgroundSync();
        resolve(id);
      };

      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Récupérer les expressions en attente
   */
  async getPendingExpressions(): Promise<PendingExpression[]> {
    this.ensureDb();
    
    return new Promise((resolve, reject) => {
      const tx = this.db!.transaction(['pending_expressions'], 'readonly');
      const store = tx.objectStore('pending_expressions');
      const request = store.getAll();

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Supprimer une expression en attente
   */
  async removePendingExpression(id: string): Promise<void> {
    this.ensureDb();
    
    return new Promise((resolve, reject) => {
      const tx = this.db!.transaction(['pending_expressions'], 'readwrite');
      const store = tx.objectStore('pending_expressions');
      const request = store.delete(id);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Stocker un média hors ligne
   */
  async storeOfflineMedia(file: File): Promise<string> {
    this.ensureDb();
    
    const id = `media_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const media: OfflineMedia = {
      id,
      blob: file,
      type: file.type,
      name: file.name,
      created_at: new Date().toISOString()
    };

    return new Promise((resolve, reject) => {
      const tx = this.db!.transaction(['offline_media'], 'readwrite');
      const store = tx.objectStore('offline_media');
      const request = store.add(media);

      request.onsuccess = () => {
        console.log('Média stocké hors ligne:', id);
        resolve(id);
      };

      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Récupérer un média hors ligne
   */
  async getOfflineMedia(id: string): Promise<OfflineMedia | null> {
    this.ensureDb();
    
    return new Promise((resolve, reject) => {
      const tx = this.db!.transaction(['offline_media'], 'readonly');
      const store = tx.objectStore('offline_media');
      const request = store.get(id);

      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Nettoyer les anciennes données
   */
  async cleanup(olderThanDays: number = 7): Promise<void> {
    this.ensureDb();
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    const cutoffTime = cutoffDate.toISOString();

    // Nettoyer les expressions en cache
    await this.cleanupStore('cached_expressions', 'cached_at', cutoffTime);
    
    // Nettoyer les médias hors ligne
    await this.cleanupStore('offline_media', 'created_at', cutoffTime);
    
    console.log(`Nettoyage des données plus anciennes que ${olderThanDays} jours`);
  }

  /**
   * Helper pour nettoyer un store
   */
  private async cleanupStore(
    storeName: string, 
    indexName: string, 
    cutoffTime: string
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const tx = this.db!.transaction([storeName], 'readwrite');
      const store = tx.objectStore(storeName);
      const index = store.index(indexName);
      const range = IDBKeyRange.upperBound(cutoffTime);
      const request = index.openCursor(range);

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          store.delete(cursor.primaryKey);
          cursor.continue();
        } else {
          resolve();
        }
      };

      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Demander une synchronisation en arrière-plan
   */
  private async requestBackgroundSync(): Promise<void> {
    if ('serviceWorker' in navigator && 'SyncManager' in window) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await (registration.sync as SyncManager).register('sync-expressions');
        console.log('Synchronisation en arrière-plan programmée');
      } catch (error) {
        console.error('Erreur programmation sync:', error);
      }
    }
  }

  /**
   * Obtenir les statistiques de stockage
   */
  async getStorageStats(): Promise<{
    cachedExpressions: number;
    pendingExpressions: number;
    offlineMedia: number;
    estimatedSize?: number;
  }> {
    this.ensureDb();
    
    const stats = {
      cachedExpressions: 0,
      pendingExpressions: 0,
      offlineMedia: 0,
      estimatedSize: undefined as number | undefined
    };

    // Compter les éléments
    const stores = ['cached_expressions', 'pending_expressions', 'offline_media'];
    
    for (const storeName of stores) {
      const count = await this.countStore(storeName);
      if (storeName === 'cached_expressions') stats.cachedExpressions = count;
      if (storeName === 'pending_expressions') stats.pendingExpressions = count;
      if (storeName === 'offline_media') stats.offlineMedia = count;
    }

    // Estimer l'espace utilisé
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate();
        stats.estimatedSize = estimate.usage;
      } catch (error) {
        console.error('Erreur estimation stockage:', error);
      }
    }

    return stats;
  }

  /**
   * Helper pour compter les éléments d'un store
   */
  private countStore(storeName: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const tx = this.db!.transaction([storeName], 'readonly');
      const store = tx.objectStore(storeName);
      const request = store.count();

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
}

// Instance singleton
let instance: OfflineStorageService | null = null;

export function getOfflineStorage(): OfflineStorageService {
  if (!instance) {
    instance = new OfflineStorageService();
  }
  return instance;
}