/**
 * Service de notifications temps réel avec SSE (Server-Sent Events)
 */

import { EventEmitter } from 'events';

export type NotificationType = 
  | 'badge_earned'              // Nouveau badge obtenu
  | 'streak_milestone'          // Jalon de streak atteint
  | 'expression_related'        // Activité sur vos expressions
  | 'pillar_expert'            // Devenu expert d'un pilier
  | 'weekly_summary'           // Résumé hebdomadaire
  | 'expression_classified'    // Expression classifiée
  | 'community_milestone'      // Jalon communautaire
  | 'system.announcement';     // Annonce système (legacy)

export interface Notification {
  notification_id: string;
  user_id: string;
  type: NotificationType;
  title: string;
  content: string;
  status: 'read' | 'unread';
  entity_type?: string;
  entity_id?: string;
  application_source: string;
  functional_scope?: string;
  priority: number;
  created_at: string;
  read_at?: string;
  expires_at?: string;
  channels: string[];
  // Frontend-only properties
  id?: string; // alias for notification_id for backward compatibility
  message?: string; // alias for content for backward compatibility
  is_read?: boolean;
  is_dismissed?: boolean;
  dismissed_at?: string;
}

export interface NotificationServiceConfig {
  apiUrl: string;
  reconnectDelay?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
}

export class NotificationService extends EventEmitter {
  private eventSource: EventSource | null = null;
  private config: Required<NotificationServiceConfig>;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private token: string | null = null;

  constructor(config: NotificationServiceConfig) {
    super();
    this.config = {
      reconnectDelay: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000,
      ...config
    };
  }

  /**
   * Se connecter au flux SSE
   */
  async connect(token: string): Promise<void> {
    if (this.isConnecting || this.eventSource?.readyState === EventSource.OPEN) {
      return;
    }

    this.isConnecting = true;
    this.token = token;

    try {
      const url = `${this.config.apiUrl}/api/v2/notifications/notifications/stream/`;
      
      // EventSource ne supporte pas les headers custom, on passe le token en query param
      const urlWithAuth = `${url}?token=${encodeURIComponent(token)}`;
      
      this.eventSource = new EventSource(urlWithAuth, {
        withCredentials: true
      });

      this.eventSource.onopen = this.handleOpen.bind(this);
      this.eventSource.onmessage = this.handleMessage.bind(this);
      this.eventSource.onerror = this.handleError.bind(this);

      // Écouter les types de messages spécifiques
      this.eventSource.addEventListener('notification', this.handleNotification.bind(this));
      this.eventSource.addEventListener('heartbeat', this.handleHeartbeat.bind(this));
      
    } catch (error) {
      this.isConnecting = false;
      this.emit('error', error);
      this.scheduleReconnect();
    }
  }

  /**
   * Se déconnecter du flux SSE
   */
  disconnect(): void {
    this.clearTimers();
    
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.token = null;
    
    this.emit('disconnected');
  }

  /**
   * Envoyer une action au serveur (via API REST)
   */
  async markAsRead(notificationId: string): Promise<void> {
    if (!this.token) throw new Error('Not authenticated');
    
    const response = await fetch(
      `${this.config.apiUrl}/api/v2/notifications/notifications/${notificationId}/mark_as_read/`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error('Failed to mark notification as read');
    }
  }

  /**
   * Masquer une notification
   */
  async dismiss(notificationId: string): Promise<void> {
    if (!this.token) throw new Error('Not authenticated');
    
    const response = await fetch(
      `${this.config.apiUrl}/api/v2/notifications/notifications/${notificationId}/dismiss/`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error('Failed to dismiss notification');
    }
  }

  /**
   * Récupérer l'historique des notifications
   */
  async getHistory(params?: { 
    limit?: number; 
    offset?: number; 
    unread_only?: boolean;
    notification_type?: string;
  }): Promise<{ results: Notification[]; count: number }> {
    if (!this.token) throw new Error('Not authenticated');
    
    const queryParams = new URLSearchParams();
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());
    if (params?.unread_only) queryParams.append('is_read', 'false');
    if (params?.notification_type) queryParams.append('notification_type', params.notification_type);
    
    const response = await fetch(
      `${this.config.apiUrl}/api/v2/notifications/notifications/?${queryParams}`,
      {
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error('Failed to fetch notifications');
    }

    const data = await response.json();
    
    // Transform backend response to match our interface
    const transformedResults = data.results.map((notif: Partial<Notification>) => ({
      ...notif,
      id: notif.notification_id, // alias for compatibility
      message: notif.content, // alias for compatibility
      is_read: notif.status === 'read',
      is_dismissed: false // backend doesn't track this yet
    }));

    return {
      results: transformedResults,
      count: data.count
    };
  }

  private handleOpen = (): void => {
    console.log('SSE connection opened');
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.emit('connected');
    this.startHeartbeat();
  };

  private handleMessage = (event: MessageEvent): void => {
    try {
      const data = JSON.parse(event.data);
      this.emit('message', data);
    } catch (error) {
      console.error('Failed to parse SSE message:', error);
    }
  };

  private handleNotification = (event: Event): void => {
    try {
      const messageEvent = event as MessageEvent;
      const notification: Notification = JSON.parse(messageEvent.data);
      this.emit('notification', notification);
    } catch (error) {
      console.error('Failed to parse notification:', error);
    }
  };

  private handleHeartbeat = (): void => {
    this.emit('heartbeat');
  };

  private handleError = (): void => {
    console.warn('SSE connection error - endpoint may not be implemented yet');
    this.isConnecting = false;
    
    // Si c'est une erreur 406 ou 404, on désactive les reconnexions
    if (this.eventSource?.readyState === EventSource.CLOSED) {
      this.emit('disconnected');
      
      // Ne pas essayer de se reconnecter si l'endpoint n'existe pas
      if (this.reconnectAttempts === 0) {
        console.info('SSE endpoint not available, disabling real-time notifications');
        this.eventSource?.close();
        this.eventSource = null;
        return;
      }
      
      this.scheduleReconnect();
    }
  };

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      this.emit('max_reconnect_attempts');
      return;
    }

    this.clearTimers();
    
    const delay = this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts);
    this.reconnectAttempts++;
    
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimer = setTimeout(() => {
      if (this.token) {
        this.connect(this.token);
      }
    }, delay);
  };

  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.eventSource?.readyState === EventSource.OPEN) {
        // Le serveur envoie des heartbeats, on vérifie juste l'état
        this.emit('heartbeat_check');
      } else {
        this.handleError();
      }
    }, this.config.heartbeatInterval);
  }

  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
}

// Instance singleton
let notificationService: NotificationService | null = null;

export function getNotificationService(): NotificationService {
  if (!notificationService) {
    notificationService = new NotificationService({
      apiUrl: process.env.NEXT_PUBLIC_API_URL || ''
    });
  }
  return notificationService;
}